<!--
* @Author: li<PERSON>liang
* @Date: 2024-10-22 17:10:50
* @Description: 招标控制价审计-审减原因=>
-->
<template>
	<ContentWrap class="common-card-search">
		<!-- 搜索工作栏 -->
		<el-form class="-mb-15px common-search-form" :model="queryParams" ref="queryFormRef" :inline="true" label-width="110px">
			<el-form-item label="审计单位" prop="zjjgname">
				<el-input v-model="queryParams.zjjgname" placeholder="请输入审计单位" clearable @keyup.enter="handleQuery"
					class="!w-200px" />
			</el-form-item>
			<el-form-item label="控制价编制单位" prop="bzdw">
				<el-input v-model="queryParams.bzdw" placeholder="请输入控制价编制单位" clearable @keyup.enter="handleQuery"
					class="!w-200px" />
			</el-form-item>
			<el-form-item label="日期" prop="publishDate">
				<el-date-picker v-model="queryParams.publishDate" type="daterange" unlink-panels range-separator="-"
					start-placeholder="开始" end-placeholder="结束" value-format="YYYY-MM-DD" :clearable="false" />
			</el-form-item>
			<el-form-item label="投资单位" prop="proOwnersName">
				<el-input v-model="queryParams.proOwnersName" placeholder="请输入投资单位" clearable @keyup.enter="handleQuery"
					class="!w-200px" />
			</el-form-item>
		</el-form>
		<div class="right-search-btn">
			<el-button type="primary" @click="handleQuery">
				<Icon icon="ep:search" class="mr-5px" />搜索
			</el-button>
			<el-button @click="resetQuery">
				<Icon icon="ep:refresh" class="mr-5px" />重置
			</el-button>
		</div>
	</ContentWrap>

	<!-- 列表 -->
	<ContentWrap>
		<el-button :loading="exportLoading" plain @click="handleExport">
			<Icon class="mr-5px" icon="ep:download" />
			导出
		</el-button>
		<el-table border v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
			<el-table-column label="序号" width="60" align="center">
				<template #default="{ $index }">{{ (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1
					}}</template>
			</el-table-column>
			<el-table-column label="控制价审计发现问题" align="left" prop="question" min-width="180">
				<template #default="scope">
					<span class="click-pointer" @click="openDetailForm(scope.row.question)">{{ scope.row.question }}</span>
				</template>
			</el-table-column>
			<el-table-column label="出现该问题的工程招标控制价数量" align="right" prop="count" />
		</el-table>
		<!-- 分页 -->
		<Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
			@pagination="getList" />
	</ContentWrap>
	<!-- 审减原因详情弹窗 -->
	<Detail ref="detailRef" />
</template>

<script setup lang="ts">
import { BiddingControlPriceApi, ReasonsVO } from '@/api/decision/engineering/biddingControlPrice'
import Detail from './Detail.vue'
import download from '@/utils/download'
defineOptions({ name: 'Reasons' })
const message = useMessage() // 消息弹窗
const loading = ref(true) // 列表的加载中
const list = ref<ReasonsVO[]>([]) // 列表的数据
const queryParams = reactive({
	pageSize: 10,
	pageNo: 1,
	zjjgname: undefined,
	bzdw: undefined,
	proOwnersName: undefined,
	publishDate: [],
	startDate: undefined,
	endDate: undefined,
})
const queryFormRef = ref() // 搜索的表单
const total = ref(0)
/** 查询列表 */
const getList = async () => {
	loading.value = true
	try {
		const data = await BiddingControlPriceApi.getReasonsList(queryParams)
		list.value = data.list
		total.value = data.total
	} finally {
		loading.value = false
	}
}
/** 搜索按钮操作 */
const handleQuery = () => {
	if (queryParams.publishDate !== null) {
		queryParams.startDate = queryParams.publishDate[0];
		queryParams.endDate = queryParams.publishDate[1];
	} else {
		queryParams.publishDate = [];
		queryParams.startDate = queryParams.publishDate[0];
		queryParams.endDate = queryParams.publishDate[1];
	}
	queryParams.pageNo = 1
	getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
	queryFormRef.value.resetFields()
	handleQuery()
}

/** 点击中介机构进详情 */
const detailRef = ref()
const openDetailForm = (row?: string) => {
	detailRef.value.open(row)
}

const exportLoading = ref(false) // 导出的加载中
/** 导出按钮操作 */
const handleExport = async () => {
	try {
		// 导出的二次确认
		await message.exportConfirm()
		// 发起导出
		exportLoading.value = true
		const data = await BiddingControlPriceApi.exportReasons(queryParams)
		download.excel(data, '招标控制价审计-审减原因.xls')
	} catch {
	} finally {
		exportLoading.value = false
	}
}

/** 初始化 **/
onMounted(() => {
	getList()
})
</script>
