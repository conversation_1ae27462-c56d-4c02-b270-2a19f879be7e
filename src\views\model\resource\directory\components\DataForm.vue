<script lang="ts" setup>
	import { ref } from 'vue'
	import * as resourceApi from "@/api/model/resource";
  import { Search } from "@element-plus/icons-vue";
  import SearchPeople from "@/views/model/resource/directory/components/SearchPeople.vue";
  import {DICT_TYPE, getStrDictOptions} from "@/utils/dict";

  const message = useMessage() // 消息弹窗
	const formRef = ref() // 表单 Ref
	const dialogVisible = ref(false) // 弹窗的是否展示
	const dialogTitle = ref('') // 弹窗的标题
	const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
	const formType = ref('') // 表单的类型：create - 新增；update - 修改
	const formData = ref({
		id: '',
		tableNameCn: '',
		tableNameEn: '',
		sourceSystem: '',
    extractMethod: '',
    tableType: '',
    startDate: '',
    latestMonthId: '',
    newlyWrittenDataVolume: '',
    totalDataVolume: '',
		catalogId: '',
		scopeField: '',
    processLogic: '',
		demandProposerId: '',
    proposerUserName:'',
    remark: '',
		sourceTableIds: []
	})
	const tableId = ref('')

	const formRules = reactive({
		tableNameCn: [{ required: true, message: '中文表名不能为空', trigger: 'blur' }],
		tableNameEn: [{ required: true, message: '英文表名不能为空', trigger: 'blur' }],
		catalogId: [{ required: true, message: '归属数据资源不能为空', trigger: 'change' }],
		sourceSystem: [{ required: true, message: '来源系统不能为空', trigger: 'blur' }],
    extractMethod: [{ required: true, message: '取数方式不能为空', trigger: 'blur' }],
    tableType: [{ required: true, message: '表类型式不能为空', trigger: 'blur' }]
	})

	/** 表单重置 */
	const reset = () => {
		formData.value = {
			id: '',
			tableNameCn: '',
			tableNameEn: '',
			sourceSystem: '',
      extractMethod: '',
      tableType: '',
      startDate: '',
      latestMonthId: '',
      newlyWrittenDataVolume: '',
      totalDataVolume: '',
			catalogId: '',
			scopeField: '',
      processLogic: '',
			demandProposerId: '',
      proposerUserName:'',
      remark: '',
			sourceTableIds: []
		};
		tableList.value = []
		formRef.value?.resetFields()
	}

	const data = ref([])
	const open = async (type : string, id ?: string = '') => {
    reset()
    tableList.value = []
		tableId.value = id
		await getResourceTree()
		await getCheckOptions()
		await getResourceTableData()
		// 获取需求提出人下拉选项
		getTree(0)
		dialogVisible.value = true
		formType.value = type
		// 修改时，设置数据
		if (type === 'update') {
			dialogTitle.value = "资源修改";
			formLoading.value = true;
			try {
				await getFields(formData.value.tableNameEn)
			} finally {
				formLoading.value = false;
			}
		} else {
			dialogTitle.value = "资源新增";
      formData.value.sourceTableIds = []
		}
	}

	defineExpose({ open }) // 提供 open 方法，用于打开弹窗
	const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调

	const getResourceTree = async () => {
		data.value = await resourceApi.getResourceTree();
	}

	const options = ref([])
	const getCheckOptions = async () => {
		options.value = await resourceApi.getResourceTableList();
	}

	const getResourceTableData = async () => {
		formData.value = await resourceApi.getResourceTableData(tableId.value)
		params.id = formData.value.id
	}


	const params = reactive({
		id: '',
		tableNameEn: '',
	})

	const tableLoading = ref(false)
	const tableList = ref([])

	const getFields = async (value : string) => {
		if (value) {
		  const result = await resourceApi.validTargetTableExist(value)
      if (result) {
        tableList.value = await targetTableFieldData(value)
        tableLoading.value = false
      } else {
        message.error(`表【${value}】在系统中不存在，请核实后再录入！`)
      }
		}
	}
	const targetTableFieldData = async (value : string) => {
		params.tableNameEn = value
		tableLoading.value = true
		return await resourceApi.getTargetTableField(params);
	}

	const handleSwitchChange = async (row : object) => {
		const newStatus = row.isKeyWord; // 直接从 row 对象中获取新状态
		await resourceApi.updateFieldKey({
			id: row.id,
			isKeyWord: newStatus
		});
	}

	interface userNode {
		id : number
		name : string
		label : string
		parentId : number | string
		children ?: userNode[]
		isLeaf ?: boolean
	}

	// const companyOptions = ref([])
	const userList = ref<userNode[]>([]) // 树形结构
	const defaultExpandedKeys = ref<(string | number)[]>([])

	const getTree = async (id) => {
		userList.value = []
		const res = await resourceApi.getDemandProposerOptions(id, '')
		userList.value = res
		defaultExpandedKeys.value = userList.value.map((node) => node.id)
	}

	const submitForm = async () => {
		// 校验
		if (!formRef.value) return
		const valid = await formRef.value.validate()
		if (!valid) return
		formLoading.value = true;
    const result = await resourceApi.validTargetTableExist(formData.value.tableNameEn)
    if (result) {
      try {
        const data = formData.value;
        // 修改的提交
        if (formType.value === 'update') {
          await resourceApi.saveResourceTable(data);
          message.alertSuccess("修改成功");
          emit('success')
          dialogVisible.value = false;
          return;
        }

        // 添加的提交
        await resourceApi.saveResourceTable(data);
        message.success("新增成功");
        emit('success')
        dialogVisible.value = false;
      } finally {
        formLoading.value = false;
      }
    } else {
      message.error(`表【${formData.value.tableNameEn}】在系统中不存在，请核实后再录入！`)
      formLoading.value = false;
    }

	}

  const choicePeopleRef = ref()
  const showSearch = () =>{
    choicePeopleRef.value.open()
  }

  const checkSuccess = async (data) => {
    formData.value.demandProposerId = data.id
    formData.value.proposerUserName = data.nickname
  }

  const isDisabled = (value) => {
    // 如果选项的Id等于TableId，则返回true以禁用该选项
    return tableId.value === value;
  }
</script>

<template>
	<div class="app-container">
		<!-- 对话框(添加 / 修改)  -->
		<Dialog :title="dialogTitle" v-model="dialogVisible" width="60%">
			<el-form ref="formRef" :model="formData" :rules="formRules" v-loading="formLoading" label-width="120px" class="common-submit-form">
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="中文表名" prop="tableNameCn">
              <el-input v-model="formData.tableNameCn" placeholder="请输入中文表名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="英文表名" prop="tableNameEn">
              <el-input v-model="formData.tableNameEn" placeholder="请输入英文表名" @change="getFields" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="归属数据资源" prop="catalogId">
              <el-tree-select
                class="!w-100%"
                v-model="formData.catalogId"
                :data="data"
                :render-after-expand="false"
                :props="{ label: 'label', children: 'children', value: 'id' }"
                default-expand-all
                check-strictly
                filterable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="来源系统" prop="sourceSystem">
              <el-select v-model="formData.sourceSystem" class="!w-100%" placeholder="请选择来源系统" clearable>
                <el-option v-for="dict in getStrDictOptions(DICT_TYPE.MODEL_SOURCE_SYSTEM)" :key="dict.value" :value="dict.value" :label="dict.label" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="取数方式" prop="extractMethod">
              <el-select v-model="formData.extractMethod" class="!w-100%" placeholder="请选择取数方式" clearable>
                <el-option v-for="dict in getStrDictOptions(DICT_TYPE.MODEL_DATA_EXTRACT_METHOD)" :key="dict.value" :value="dict.value" :label="dict.label" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="表类型 " prop="tableType">
              <el-select v-model="formData.tableType" class="!w-100%" placeholder="请选择表类型" clearable>
                <el-option v-for="dict in getStrDictOptions(DICT_TYPE.MODEL_TABLE_TYPE)" :key="dict.value" :value="dict.value" :label="dict.label" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="开始日期" prop="startDate">
              <el-date-picker v-model="formData.startDate" type="date" class="!w-100%" format="YYYY-MM-DD" value-format="YYYY-MM-DD" placeholder="请选择开始日期" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="最新账期 " prop="latestMonthId">
              <el-date-picker v-model="formData.latestMonthId" type="month" class="!w-100%" format="YYYYMM" value-format="YYYYMM" placeholder="请选择最新账期" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="最新数据量" prop="newlyWrittenDataVolume">
              <el-input v-model="formData.newlyWrittenDataVolume" placeholder="请输入最新数据量" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="累计数据量" prop="totalDataVolume">
              <el-input v-model="formData.totalDataVolume" placeholder="请输入累计数据量" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="对应组织字段">
                <el-select v-model="formData.scopeField" class="!w-100%">
                  <el-option v-for="item in tableList" :key="item.fieldName" :label="item.fieldComment" :value="item.fieldName" />
                </el-select>
              </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="需求提出人" prop="proposerUserName">
              <el-input v-model="formData.proposerUserName" placeholder="请选择需求提出人" readonly clearable>
                <template #append>
                  <el-button :icon="Search" @click="showSearch" />
                </template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="来源表" prop="sourceTableIds">
              <el-select v-model="formData.sourceTableIds" :clearable="false" multiple class="!w-100%">
                <el-option
                  v-for="item in options"
                  :key="item.id"
                  :label="item.tableNameCn"
                  :value="item.id"
                  :disabled="isDisabled(item.id)"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="加工逻辑" prop="processLogic">
              <el-input v-model="formData.processLogic" type="textarea" :rows="3" placeholder="加工逻辑" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="formData.remark" type="textarea" :rows="3" placeholder="备注" />
            </el-form-item>
          </el-col>
        </el-row>
			</el-form>
			<el-table v-loading="tableLoading" :data="tableList" height="260" :show-overflow-tooltip="true" border>
				<el-table-column label="序号" width="60" align="center">
					<template #default="{ $index }">
						{{ $index + 1 }}
					</template>
				</el-table-column>
				<el-table-column label="字段名" align="left" prop="fieldName" width="320" />
				<el-table-column label="字段类型" align="left" prop="fieldTypeName" width="120" />
				<el-table-column label="字段注释" align="left" prop="fieldComment" />
				<el-table-column label="是否关键字段" align="center" prop="isKeyWord" width="110">
					<template #default="scope">
						<el-switch v-model="scope.row.isKeyWord" active-value="1" inactive-value="0" @change="handleSwitchChange(scope.row)" />
					</template>
				</el-table-column>
			</el-table>
			<template #footer>
				<div class="dialog-footer">
					<el-button type="primary" @click="submitForm" :disabled="formLoading">确 定</el-button>
					<el-button @click="dialogVisible = false">取 消</el-button>
				</div>
			</template>
		</Dialog>
	</div>

  <SearchPeople ref="choicePeopleRef" @success="checkSuccess"/>

</template>

<style scoped lang="scss">
.no-border {
  border: none;
  /* 如果需要，你还可以添加其他样式，如背景色、文字颜色等 */
  background-color: transparent; /* 移除背景色 */
  /* 注意：对于某些类型的按钮（如 primary），你可能还需要覆盖其他样式属性 */
}

::v-deep(.el-input-group__append) {
  box-shadow: none;
}

::v-deep(.el-table .el-table__header .el-table__cell) {
  text-align: center;
}
</style>
