<!--
* @Author: lijunliang
* @Date: 2024-10-24 17:10:50
* @Description: 组织信息=>
-->
<template>
	<ContentWrap class="common-card-search">
		<!-- 搜索工作栏 -->
		<el-form class="-mb-15px common-search-form" :model="queryParams" ref="queryFormRef" :inline="true" label-width="60px">
			<el-form-item label="单位" prop="deptId">
				<el-tree-select v-model="queryParams.entName" ref="treeRef" placeholder="请选择所属单位"
					:data="deptList" check-strictly :expand-on-click-node="false" :check-on-click-node="true"
					:default-expand-all="false" highlight-current node-key="name"
					:load="loadNode" @change="getList" :default-expanded-keys="defaultExpandedKeys"
					:filter-node-method="filterNode" lazy class="!w-200px">
					<template #default="{ data: { name } }">{{ name }}</template>
				</el-tree-select>
			</el-form-item>
			<el-form-item label="年份" prop="auditYear">
				<el-date-picker v-model="queryParams.auditYear" type="year" value-format="YYYY" class="!w-200px"
					placeholder="请选择年份" />
			</el-form-item>
		</el-form>
		<div class="right-search-btn">
			<el-button  type="primary"  @click="handleQuery">
					<Icon icon="ep:search" class="mr-5px" />搜索
				</el-button>
				<el-button @click="resetQuery">
					<Icon icon="ep:refresh" class="mr-5px" />重置
				</el-button>
		</div>
	</ContentWrap>

	<!-- 列表 -->
	<ContentWrap>
		<el-tabs v-model="queryParams.code" class="demo-tabs" @tab-change="handleClick">
			<el-tab-pane label="股东信息" name="zhimeng05" />
			<el-tab-pane label="组织架构" name="2" />
			<el-tab-pane label="主要人员（高管）" name="zhimeng04" />
			<el-tab-pane label="对外投资" name="zhimeng07" />
			<el-tab-pane label="员工信息" name="3" />
		</el-tabs>
		<!-- 股东信息 -->
		<el-table border v-show="queryParams.code == 'zhimeng05'" v-loading="loading" :data="list" :stripe="true"
			:show-overflow-tooltip="true">
			<el-table-column label="序号" width="60" align="center">
				<template #default="{ $index }">{{ (queryParams.pageNum - 1) * queryParams.pageSize + $index + 1
					}}</template>
			</el-table-column>
			<el-table-column label="股东" align="center" prop="partnerName" min-width="120" />
			<el-table-column label="持股比例" align="center" prop="percent" />
			<el-table-column label="应缴资本" align="center" prop="shouldCapi" />
			<el-table-column label="应缴币种" align="center" prop="shouldCurrency" />
		</el-table>
		<!-- 组织架构 -->
		<div v-show="queryParams.code == '2'">
			<!-- <el-image :src="src" /> -->
		</div>
		<!-- 主要人员（高管） -->
		<el-table border v-show="queryParams.code == 'zhimeng04'" v-loading="loading" :data="list" :stripe="true"
			:show-overflow-tooltip="true">
			<el-table-column label="序号" width="60" align="center">
				<template #default="{ $index }">{{ (queryParams.pageNum - 1) * queryParams.pageSize + $index + 1
					}}</template>
			</el-table-column>
			<el-table-column label="姓名" align="center" prop="managerName" />
			<el-table-column label="职位" align="center" prop="managerPosition" />
		</el-table>
		<!-- 对外投资 -->
		<el-table border v-show="queryParams.code == 'zhimeng07'" v-loading="loading" :data="list" :stripe="true"
			:show-overflow-tooltip="true">
			<el-table-column label="序号" width="60" align="center">
				<template #default="{ $index }">{{ (queryParams.pageNum - 1) * queryParams.pageSize + $index + 1
					}}</template>
			</el-table-column>
			<el-table-column label="企业名称" align="center" prop="entName" />
			<el-table-column label="实缴投资额" align="center" prop="realCapi" />
			<el-table-column label="股权占比" align="center" prop="percent" />
		</el-table>
		<!-- 员工信息 -->
		<el-table border v-show="queryParams.code == '3'" v-loading="loading" :data="list3" :stripe="true"
			:show-overflow-tooltip="true">
			<el-table-column label="年龄阶段" align="center" prop="ageStage" />
			<el-table-column label="35岁以下" align="center" prop="underThreeFive" />
			<el-table-column label="36-50岁" prop="threeFiveWithFive" align="center" />
			<el-table-column label="50岁以上" prop="overFive" align="center" />
			<el-table-column label="合计" align="center" prop="allAgeCount" />
		</el-table>
		<!-- 分页 -->
		<Pagination v-show="queryParams.code == 'zhimeng05'||queryParams.code == 'zhimeng04'||queryParams.code == 'zhimeng07'" :total="total" v-model:page="queryParams.pageNum"
			v-model:limit="queryParams.pageSize" @pagination="getList" />
	</ContentWrap>
</template>

<script setup lang="ts">
import { CompanyBasicInfoApi, CompanyBasicInfoVO, EmployeeVO } from '@/api/model/companyProfile/auditPortraitAnalysis/companyBasicInfo'
import { handleLazyTree } from '@/utils/tree'
import * as DeptApi from '@/api/system/dept'
defineOptions({ name: 'OrganizationInfo' })

const loading = ref(true) // 列表的加载中
const list = ref<CompanyBasicInfoVO[]>([]) // 列表的数据
const list3 = ref<EmployeeVO[]>([]) // 列表的数据
const queryParams = reactive({
	entName: "山东港口产城融合发展集团有限公司",
	creditNo: "",
	pageNum: 1,
	pageSize: 10,
	code: "zhimeng05",
	auditYear: ''
})
const src = 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg'
const queryFormRef = ref() // 搜索的表单
const total = ref(0)
const handleClick = async (type) => {
	console.log(type);
	queryParams.code = type
	await getList()
}
/** 查询列表 */
const getList = async () => {
	loading.value = true
	try {
		if(queryParams.code == '3'){
			const data = await CompanyBasicInfoApi.getEmployee(queryParams)
			console.log(data);
			list3.value = [{
				ageStage: '员工人数',
				underThreeFive: data.underThreeFive,
				threeFiveWithFive: data.threeFiveWithFive,
				overFive: data.overFive,
				allAgeCount: data.allAgeCount,
				underThreeFiveRate: {},
				threeFiveWithFiveRate: {},
				overFiveRate: {},
				allAgeRate: {}
			},{
				ageStage: '占比%',
				underThreeFive: data.underThreeFiveRate,
				threeFiveWithFive: data.underThreeFiveRate,
				overFive: data.overFiveRate,
				allAgeCount: data.allAgeRate,
				underThreeFiveRate: {},
				threeFiveWithFiveRate: {},
				overFiveRate: {},
				allAgeRate: {}
			}]
		}else{
			const data = await CompanyBasicInfoApi.requestCompanyBasicInfo(queryParams)
			list.value = data.rows
			total.value = data.total
		}

	} finally {
		loading.value = false
	}
}
// 获取单位
interface DeptNode {
	id: number
	masterOrgId?: number | string
	name: string
	parentId: number | string
	children?: DeptNode[]
	isLeaf?: boolean
}
const deptList = ref<DeptNode[]>([]) // 树形结构
const defaultExpandedKeys = ref<(string | number)[]>([])
const getTree = async (id) => {
	deptList.value = []
	const res = await DeptApi.getSimpleDeptList(id)
	deptList.value.push(...handleLazyTree(res, 'id', 'parentId'))
	defaultExpandedKeys.value = deptList.value.map((node) => node.id)
}
const filterNode = (name: string, data: DeptNode) => {
	if (!name) return true
	return data.name.includes(name)
}
const loadNode = async (node: Node, resolve: (data: any[]) => void) => {
	try {
		const nodeId = node.data.id
		if (nodeId == undefined || nodeId == null) {
			return
		}
		const res = await DeptApi.getSimpleDeptList(nodeId)
		const children = handleLazyTree(res, 'id', 'parentId', 'children')
		resolve(children)
	} catch (error) {
		resolve([])
	}
}
/** 搜索按钮操作 */
const handleQuery = () => {
	queryParams.pageNum = 1
	getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
	queryFormRef.value.resetFields()
	handleQuery()
}

/** 初始化 **/
onMounted(() => {
	getList()
	getTree(0)
})
</script>
