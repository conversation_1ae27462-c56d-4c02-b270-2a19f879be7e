<!-- 风险看板 -->
<template>
	<div class="header">
		<div class="header_title">
			<div class="header_title_size">
				风险模型
			</div>
			<div class="flex items-center">
				<div class="header_date mr-12px">
					<div class="pr-14px">年度</div>
					<el-date-picker v-model="queryParams.publishDate" type="year" placeholder="开始" class="!w-90px"
						@change="handleQuery" value-format="YYYY-MM-DD"/>
				</div>
				<div class="header_date !w-270px">
					<div class="pr-14px">公司名称</div>
					<el-input v-model="queryParams.projectName" placeholder="请输入公司名称" clearable
						@keyup.enter="handleQuery" class="!w-180px" />
				</div>
			</div>
		</div>
		<div class="flex items-center pt-12px pr-20px pl-20px">
			<div class="header_card1">
				<div class="header_card_title">
					风险
				</div>
				<el-row class="flex items-center pl-20px pt-10px">
					<el-col :span="4" :xs="24">
						<el-image style="width: 64px; height: 64px" :src="url1" fit="cover" />
					</el-col>
					<el-col :span="5" :xs="24">
						<div class="card_number text-center">
							3
						</div>
						<div class="card_text text-center">
							风险领域统计
						</div>
					</el-col>
					<el-col :span="5" :xs="24">
						<div class="card_number text-center">
							86
						</div>
						<div class="card_text text-center">
							模型数量统计
						</div>
					</el-col>
					<el-col :span="5" :xs="24">
						<div class="card_number text-center">
							25
						</div>
						<div class="card_text text-center">
							主题数量统计
						</div>
					</el-col>
					<el-col :span="5" :xs="24">
						<div class="card_number text-center">
							92.00%
						</div>
						<div class="card_text text-center">
							模型使用率
						</div>
					</el-col>
				</el-row>
			</div>
			<div class="header_card2">
				<div class="header_card_title">
					预警
				</div>
				<el-row class="flex items-center pl-20px pt-10px">
					<el-col :span="7" :xs="24">
						<el-image style="width: 64px; height: 64px" :src="url4" fit="cover" />
					</el-col>
					<el-col :span="9" :xs="24">
						<div class="card_number text-center">
							40
						</div>
						<div class="card_text text-center">
							预警数量统计
						</div>
					</el-col>
					<el-col :span="8" :xs="24">
						<div class="card_number text-center">
							36.25%
						</div>
						<div class="card_text text-center">
							疑点转化率
						</div>
					</el-col>
				</el-row>
			</div>
			<div class="header_card3">
				<div class="header_card_title">
					疑点
				</div>
				<el-row class="flex items-center pl-20px pt-10px">
					<el-col :span="7" :xs="24">
						<el-image style="width: 64px; height: 64px" :src="url5" fit="cover" />
					</el-col>
					<el-col :span="9" :xs="24">
						<div class="card_number text-center">
							55
						</div>
						<div class="card_text text-center">
							疑点数量统计
						</div>
					</el-col>
					<el-col :span="8" :xs="24">
						<div class="card_number text-center">
							40.00%
						</div>
						<div class="card_text text-center">
							疑点数量统计
						</div>
					</el-col>
				</el-row>
			</div>
		</div>
	</div>
	<div class="flex items-center pt-5px">
		<div class="title-left"></div>
		<div class="header_title_size pl-8px">
			业务领域
		</div>
		<div class="header_date mr-12px ml-8px">
			<el-select v-model="queryParams.reportState" placeholder="请选择是否" :clearable="false" class="!w-130px">
				<el-option v-for="dict in dictOptions" :key="dict.value"
					:label="dict.label" :value="dict.value" @change="handleQuery" />
			</el-select>
		</div>
	</div>
	<el-row :gutter="8" class="pt-5px">
		<el-col :span="8" :xs="24">
			<ContentWrap>
				<div class="flex_title">
					<div class="title-left"></div>
					<div class="pl-8px">预警统计</div>
				</div>
				<div :class="twoChart" ref="twoChartRef" style="height:288px;width:100%;"></div>
			</ContentWrap>
		</el-col>
		<el-col :span="8" :xs="24">
			<ContentWrap>
				<div class="flex_title">
					<div class="title-left"></div>
					<div class="pl-8px">疑点统计</div>
				</div>
				<div :class="fourChart" ref="fourChartRef" style="height:288px;width:100%;"></div>
			</ContentWrap>
		</el-col>
		<el-col :span="8" :xs="24">
			<ContentWrap>
				<div class="flex_title">
					<div class="title-left"></div>
					<div class="pl-8px">模型运行效率TOP5</div>
				</div>
				<div :class="threeChart" ref="threeChartRef" style="height:288px;width:100%;"></div>
			</ContentWrap>
		</el-col>
	</el-row>
	<ContentWrap>
		<div class="flex_title">
			<div class="title-left"></div>
			<div class="pl-8px">风险转化率趋势</div>
		</div>
		<div class="flex_layout">
			<el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
				<el-tab-pane v-for="item in editableTabs" :key="item.name" :label="item.title" :name="item.name" />
			</el-tabs>
		</div>
		<div :class="fiveChart" ref="fiveChartRef" style="height:288px;width:100%;"></div>
	</ContentWrap>
</template>

<script lang="ts" setup>
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import * as echarts from 'echarts';
import demoviewIcon1 from '@/assets/imgs/decision/icon-jihua.png';
import demoviewIcon4 from '@/assets/imgs/decision/icon-weilixiang.png';
import demoviewIcon5 from '@/assets/imgs/decision/icon-yichaoqi.png';
import { colorList } from '../index'
defineOptions({
	name: 'Risk'
})
const queryParams = reactive({
	publishDate: undefined,
	projectName: undefined,
	reportState: '0',
})
const loading = ref(true) // 列表的加载中

const url1 = ref(demoviewIcon1)
const url4 = ref(demoviewIcon4)
const url5 = ref(demoviewIcon5)

const activeName = ref('0')
const editableTabs = ref([
	{
		title: '所有领域',
		name: '0',
	},
	{
		title: '涉及金额',
		name: '1',
	},
	{
		title: '采购领域',
		name: '2',
	},
	{
		title: '合同领域',
		name: '3',
	},
	{
		title: '工程领域',
		name: '4',
	},
	{
		title: '其他领域',
		name: '5',
	},
])
const handleClick = () => {

}
const color = ['#2A71C0', '#FF9C01', '#AC3C52', '#C96F37', '#95C2ED', '#E6E8F4', '#ABEDD8']
// 第二个echarts
let twoChart: echarts.ECharts | null = null;
const twoChartRef = ref()
const getTwoChart = async () => {
	twoChart = echarts.init(twoChartRef.value)
	const data = [
		{ value: 100, name: '招投标' },
		{ value: 150, name: '合同' },
		{ value: 155, name: '采购订单' },
		{ value: 60, name: '收货' },
		{ value: 25, name: '出入库' },
	]
	twoChart.setOption({
		color: color,
		tooltip: {
			trigger: 'item',
		},
		legend: {
			top: 'bottom',
			itemWidth: 14,
			data: data
		},
		series: [
			{
				name: '姓名',
				type: 'pie',
				radius: [20, 100],
				center: ['50%', '40%'],
				roseType: 'area',
				data: data,
				itemStyle: {
					borderRadius: 2,
					normal: {
                        color: function (params) {
                            return colorList[params.dataIndex];
                        },
                    },
				},
				label: {
					show: false
				},
				emphasis: {
					itemStyle: {
						shadowBlur: 10,
						shadowOffsetX: 0,
						shadowColor: 'rgba(0, 0, 0, 0.5)'
					}
				}
			}
		]
	})
}

// 第三个echarts
let threeChart: echarts.ECharts | null = null;
const threeChartRef = ref()
const getThreeChart = async () => {
	threeChart = echarts.init(threeChartRef.value)
	threeChart.setOption({
		color: color,
		grid: {
			top: "10%"
		},
		tooltip: {
			trigger: 'axis',
			axisPointer: {
				type: 'cross',
				crossStyle: {
					color: '#999'
				}
			}
		},
		legend: {
			data: ['模型运行次数', '预警数', '疑点数', '核实问题数', '转化率(%)'],
			itemWidth: 14,
			top: 'bottom',
		},
		xAxis: [
			{
				type: 'category',
				data: ['招投标', '合同', '采购订单', '收获', '出入库'],
				axisPointer: {
					type: 'shadow'
				},
				axisLabel: {
					interval: 0,
				}
			}
		],
		yAxis: [
			{
				type: 'value',
			},
			{
				type: 'value',
				splitLine: {
					show: false,
				}
			}
		],
		series: [
			{
				name: '模型运行次数',
				type: 'bar',
				data: [200, 280, 460, 220, 260],
				barMaxWidth: 40,
				itemStyle: {
                    borderRadius: [10, 10, 0, 0]
                }
			},
			{
				name: '预警数',
				type: 'bar',
				data: [100, 150, 155, 60, 25],
				barMaxWidth: 40,
				itemStyle: {
                    borderRadius: [10, 10, 0, 0]
                }
			},
			{
				name: '疑点数',
				type: 'bar',
				data: [37, 55, 60, 20, 5],
				itemStyle: {
                    borderRadius: [10, 10, 0, 0]
                }
			},
			{
				name: '核实问题数',
				type: 'bar',
				data: [20, 25, 28, 11, 8],
				barMaxWidth: 40,
				itemStyle: {
                    borderRadius: [10, 10, 0, 0]
                }
			},
			{
				name: '转化率(%)',
				type: 'line',
				yAxisIndex: 1,
				data: [10, 9, 6, 5, 3]
			}
		]
	})
}

// 第四个echarts
let fourChart: echarts.ECharts | null = null;
const fourChartRef = ref()
const getFourChart = async () => {
	fourChart = echarts.init(fourChartRef.value)
	const data = [
		{ value: 17, name: '疑似围串标' },
		{ value: 29, name: '报价接近预算' },
		{ value: 29, name: '主体资质' },
		{ value: 31, name: '未及时签订' },
		{ value: 14, name: '超计划验收' },
		{ value: 20, name: '竞争不充分' },
	]
	fourChart.setOption({
		color: color,
		tooltip: {
			trigger: 'item'
		},
		legend: {
			top: 'bottom',
			itemWidth: 14,
			data: data
		},
		series: [
			{
				name: '姓名',
				type: 'pie',
				radius: '60%',
				center: ['50%', '40%'],
				data: data,
				label: {
					show: false
				},
				emphasis: {
					itemStyle: {
						shadowBlur: 10,
						shadowOffsetX: 0,
						shadowColor: 'rgba(0, 0, 0, 0.5)'
					}
				},
				selectedMode: "single",
			}
		]
	})
}

// 第五个echarts
let fiveChart: echarts.ECharts | null = null;
const fiveChartRef = ref()
const getFiveChart = async () => {
	fiveChart = echarts.init(fiveChartRef.value)
	fiveChart.setOption({
		color: color,
		grid: {
			top: "10%",
			right: 50,
			left: 50,
		},
		tooltip: {
			trigger: 'axis',
			axisPointer: {
				type: 'cross',
				crossStyle: {
					color: '#999'
				}
			}
		},
		legend: {
			data: ['模型运行次数', '预警数', '疑点数', '问题数', '问题转化率(%)'],
			itemWidth: 14,
			top: 'bottom',
		},
		xAxis: [
			{
				type: 'category',
				data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
				axisPointer: {
					type: 'shadow'
				},
				axisLabel: {
					interval: 0,
				}
			}
		],
		yAxis: [
			{
				type: 'value',
			},
			{
				type: 'value',
				splitLine: {
					show: false,
				}
			}
		],
		series: [
			{
				name: '模型运行次数',
				type: 'bar',
				data: [100, 140, 230, 100, 130, 119, 175, 160, 192, 149, 222, 132],
				barMaxWidth: 40,
				itemStyle: {
                    borderRadius: [10, 10, 0, 0],
					color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 1, color: '#BBC8DE' },
                        { offset: 0, color: '#07397E' }
                    ])
                }
			},
			{
				name: '预警数',
				type: 'bar',
				data: [30, 50, 80, 20, 26, 17, 45, 32, 52, 23, 65, 19],
				barMaxWidth: 40,
				itemStyle: {
                    borderRadius: [10, 10, 0, 0],
					color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 1, color: '#75ACE3' },
                        { offset: 0, color: '#82ADD7' }
                    ])
                }
			},
			{
				name: '疑点数',
				type: 'bar',
				data: [15, 20, 40, 8, 12, 6, 18, 13, 28, 11, 33, 9],
				barMaxWidth: 40,
				itemStyle: {
                    borderRadius: [10, 10, 0, 0],
					color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 1, color: '#DFE7FD' },
                        { offset: 0, color: '#E6E8F4' }
                    ])
                }
			},
			{
				name: '问题数',
				type: 'bar',
				data: [5, 8, 15, 2, 7, 3, 9, 6, 15, 4, 17, 5],
				barMaxWidth: 40,
				itemStyle: {
                    borderRadius: [10, 10, 0, 0],
					color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 1, color: '#E5B066' },
                        { offset: 0, color: '#FCCA69' }
                    ])
                }
			},
			{
				name: '问题转化率(%)',
				type: 'line',
				yAxisIndex: 1,
				data: [5, 6, 7, 2, 5, 3, 5, 3, 8, 3, 8, 4]
			}
		]
	})
}

const getAllApi = async () => {
	await Promise.all([getTwoChart()])
	await Promise.all([getThreeChart()])
	await Promise.all([getFourChart()])
	await Promise.all([getFiveChart()])
	loading.value = false
}

const dictOptions = ref([
	{
		label: '采购领域',
		value: '0'
	},
	{
		label: '财务领域',
		value: '1'
	},
	{
		label: '合同领域',
		value: '2'
	}
])

/** 搜索按钮操作 */
const handleQuery = () => {
	console.log('000')
	getAllApi()
}

/** 初始化 **/
onMounted(() => {
	getAllApi()
})
</script>

<style scoped>
/* 去除输入框边框 */
::v-deep .el-date-editor {
	--el-input-border-color: transparent !important;
	--el-input-bg-color: transparent;
	--el-input-hover-border-color: #fff;
}
::v-deep .el-input {
	--el-input-border-color: transparent !important;
    --el-input-bg-color: transparent;
	--el-input-hover-border-color: #fff;
}
::v-deep .el-select{
	--el-border-color: #fff !important;
}
.flex_layout {
	display: flex;
	align-items: center;
	justify-content: center;
}

::v-deep .el-tabs__nav-wrap::after {
	/* 去掉下划线 */
	position: static !important;
}

.flex_title {
	display: flex;
	align-items: center;
	line-height: 33px;
	font-family: Source Han Sans, Source Han Sans;
	font-weight: 700;
	font-size: 16px;
	color: #333333;
	border-bottom: 1px solid #EEEFF0;
}

.title-left {
	width: 5px;
	height: 15px;
	background: #2F4CAD;
	border-radius: 0px 0px 0px 0px;
}

.header {
	height: 199px;
	border: 2px solid rgba(255, 255, 255, 1);
    background: #F7F7F7;
	border-radius: 8px;
}

.header_title {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 12px 20px 0;
}

.header_title_size {
	height: 26px;
	font-family: Source Han Sans, Source Han Sans;
	font-weight: 500;
	font-size: 18px;
	color: #333333;
	line-height: 26px;
}

.header_date {
	width: 150px;
	height: 34px;
	background: rgba(255, 255, 255, 0.6);
	border-radius: 8px 8px 8px 8px;
	border: 2px solid #FFFFFF;
	font-family: Source Han Sans, Source Han Sans;
	font-weight: 400;
	font-size: 15px;
	color: #999;
	line-height: 22px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.header_card1 {
	flex: 2;
	height: 125px;
	background: linear-gradient(160.24deg, #A0BDF0 13.03%, #07397E 86.79%);
	border-radius: 8px 8px 8px 8px;
	border: 2px solid #FFFFFF;
}

.header_card2 {
	flex: 1;
	height: 125px;
	background: linear-gradient(160.24deg, #A0BDF0 13.03%, #07397E 86.79%);
	border-radius: 8px 8px 8px 8px;
	border: 2px solid #FFFFFF;
	margin-left: 6px;
}

.header_card3 {
	flex: 1;
	height: 125px;
	background: linear-gradient(160.24deg, #FCC535 13.03%, #FF7F00 86.79%);
	border-radius: 8px 8px 8px 8px;
	border: 2px solid #FFFFFF;
	margin-left: 6px;
}

.header_card_title {
	height: 23px;
	font-family: Source Han Sans, Source Han Sans;
	font-weight: 400;
	font-size: 16px;
	color: #fff;
	line-height: 23px;
	padding-top: 14px;
	padding-left: 20px;
}

.header_card_item {
	flex: 1;
}

.card_number {
	height: 41px;
	font-family: Source Han Sans, Source Han Sans;
	font-weight: 700;
	font-size: 26px;
	color: #fff;
	line-height: 41px;
}

.card_text {
	height: 22px;
	font-family: Source Han Sans, Source Han Sans;
	font-weight: 400;
	font-size: 15px;
	color: #fff;
	line-height: 22px;
}
</style>