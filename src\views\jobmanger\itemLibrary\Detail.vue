<!--
* @Author: l<PERSON><PERSON><PERSON>g
* @Date: 2024-09-11 16:38:58
* @Description: 审计事项库详情=>
-->
<template>
	<Dialog v-model="dialogVisible" :scroll="true" title="详情" width="70%">
		<div v-loading="detailLoading">
			<el-descriptions :column="2" border>
				<el-descriptions-item label="风险名称">{{ detailData.matterName }}</el-descriptions-item>
				<el-descriptions-item label="事项编码">{{ detailData.matterCode }}</el-descriptions-item>
				<el-descriptions-item label="所属事项">{{ detailData.mattersName }}</el-descriptions-item>
				<el-descriptions-item label="事项类型">
					<dict-tag :type="'tank_matters_type'" :value="detailData.matterType" />
				</el-descriptions-item>
				<el-descriptions-item :span="3" label="事项描述">{{detailData.mattersDesc}}
					<!-- <span v-html="detailData.mattersDesc"></span> -->
				</el-descriptions-item>
				<el-descriptions-item :span="3" label="控制措施">{{detailData.controlMeasures}}
					<!-- <span v-html="detailData.controlMeasures"></span> -->
				</el-descriptions-item>
				<el-descriptions-item :span="3" label="审计步骤及方法">{{detailData.stepMethod}}
					<!-- <span v-html="detailData.stepMethod"></span> -->
				</el-descriptions-item>
				<el-descriptions-item :span="3" label="主要问题表现">{{detailData.mainQues}}
					<!-- <span v-html="detailData.mainQues"></span> -->
				</el-descriptions-item>
				<el-descriptions-item :span="3" label="审计建议">{{detailData.auditSugg}}
					<!-- <span v-html="detailData.auditSugg"></span> -->
				</el-descriptions-item>
			</el-descriptions>
			<el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
				<el-tab-pane label="外部法律法规" name="0">
					<el-table :data="detailData.lawsAndRegulations" border :stripe="true" :show-overflow-tooltip="true">
						<el-table-column label="文号" align="left" prop="docNum" min-width="180" />
						<el-table-column label="发布主体" align="center" prop="publishMain">
							<template #default="scope">
								<dict-tag :type="DICT_TYPE.EXTERNAL_LAWS_REGULATIONS_SUBJECT"
									:value="scope.row.publishMain" />
							</template>
						</el-table-column>
						<el-table-column label="法律法规名称" align="left" prop="lawName" min-width="180"/>
						<el-table-column label="生效日期" align="center" prop="effeDate" min-width="120">
							<template #default="scope">
								<span>{{ formatTime(scope.row.effeDate, 'yyyy-MM-dd') }}</span>
							</template>
						</el-table-column>
						<!-- <el-table-column label="规定应用描述" align="center" prop="name" /> -->
						<el-table-column label="法规状态" align="center" prop="lawStatus" min-width="100">
							<template #default="scope">
								<dict-tag :type="DICT_TYPE.EXTERNAL_LAWS_REGULATIONS_STATE" :value="scope.row.lawStatus" />
							</template>
						</el-table-column>
						<el-table-column label="操作" align="center" width="100">
							<template #default="scope">
								<!-- <el-button link type="primary" @click="handleView(scope.row.fileId)">预览附件</el-button> -->
								<el-button link type="primary" @click="handleWbView(scope.row.id)">查看</el-button>
							</template>
						</el-table-column>
					</el-table>
				</el-tab-pane>
				<el-tab-pane label="内部规章制度" name="1">
					<el-table :data="detailData.internalRegulations" border :stripe="true" :show-overflow-tooltip="true">
						<el-table-column type="index" label="序号" width="60" align="center" />
						<el-table-column label="标准编号" align="left" prop="standarNum" min-width="180" />
						<el-table-column label="标准名称" align="left" prop="standarName" min-width="180"/>
						<el-table-column label="制定/修正" align="center" prop="amend" min-width="100">
							<template #default="scope">
								<dict-tag type="internalre_vision" :value="scope.row.amend" />
							</template>
						</el-table-column>
						<!-- <el-table-column label="规定应用描述" align="center" prop="name" /> -->
						<el-table-column label="标准状态" align="center" prop="status" min-width="100">
							<template #default="scope">
								<dict-tag type="Internalst_andard_state" :value="scope.row.status" />
							</template>
						</el-table-column>
						<el-table-column label="归口部门" align="left" prop="deptName" min-width="180"/>
						<el-table-column label="操作" align="center" width="100">
							<template #default="scope">
								<!-- <el-button link type="primary" @click="handleView(scope.row.fileId)">预览附件</el-button> -->
								<el-button link type="primary" @click="handleView(scope.row.id)">查看</el-button>
							</template>
						</el-table-column>
					</el-table>
				</el-tab-pane>
				<el-tab-pane label="审计资料" name="2">
					<el-table :data="detailData.auditData" border :stripe="true" :show-overflow-tooltip="true">
						<el-table-column type="index" label="序号" width="60" align="center"/>
						<el-table-column label="资料名称" align="left" prop="materialName" min-width="120" />
						<el-table-column label="事项名称" align="left" prop="mattersName" min-width="180"/>
						<el-table-column label="是否有模版" key="templateFlag" align="center" min-width="100">
							<template #default="{row}">
								<dict-tag :type="'audit_data_list_template'" :value="row.templateFlag" />
							</template>
						</el-table-column>
						<el-table-column label="资料说明" align="left" prop="materialDesc" min-width="180">
							<template #default="{row}">
								<span v-html="row.materialDesc"></span>
							</template>
						</el-table-column>
						<el-table-column label="操作" align="center" width="100">
							<template #default="scope">
								<el-button link type="primary" @click="handleView(scope.row.fileId)">预览附件</el-button>
							</template>
						</el-table-column>
					</el-table>
				</el-tab-pane>
				<el-tab-pane v-if="false" label="审计模型" name="3">
					<el-table :data="detailData.auditModel" border :stripe="true" :show-overflow-tooltip="true">
						<el-table-column type="index" label="序号" width="60" />
						<el-table-column label="模型名称" align="center" prop="name" min-width="120" />
						<el-table-column label="模型编码" align="center" prop="name" />
						<el-table-column label="模型描述" align="center" prop="name" />
						<el-table-column label="模型状态" align="center" prop="name" />
						<el-table-column label="操作" align="center" width="150">
							<template #default="scope">
								<el-button link type="primary" @click="handleMxView(scope.row)">查看模型</el-button>
							</template>
						</el-table-column>
					</el-table>
				</el-tab-pane>
			</el-tabs>
		</div>
	</Dialog>
	<!-- 预览附件 -->
	<DialogFlie ref="DialogFlieRef" />
	<!-- 外部法律法规-查看 -->
	<ItemDetail ref="ItemDetailRef" />
	<!-- 内部法律法规-查看 -->
	<OutDetail ref="OutDetailRef" />
</template>

<script lang="ts" setup>
	import { DICT_TYPE, NumberDictDataType, getIntDictOptions } from '@/utils/dict'
	import { formatTime } from '@/utils'
	import { ItemLibraryApi, ItemLibraryDetailVO } from '@/api/jobmanger/itemLibrary'
	import ItemDetail from '@/views/jobmanger/regulations/outregulations/Detail.vue'
	import OutDetail from '@/views/jobmanger/regulations/innerregulations/Detail.vue'
	defineOptions({ name: 'Detail' })

	const dialogVisible = ref(false) // 弹窗的是否展示
	const detailLoading = ref(false) // 表单地加载中
	const detailData = ref({} as ItemLibraryDetailVO) // 详情数据
	/** 打开弹窗 */
	const open = async (activeName : number, id : number, matterId: number) => {
		dialogVisible.value = true
		// 设置数据
		detailLoading.value = true
		try {
			detailData.value = {}
			if (activeName == '1') {
				detailData.value = await ItemLibraryApi.getItemLibrary(id)
			} else {
				detailData.value = await ItemLibraryApi.getItemLibrary(matterId)
			}
		} finally {
			detailLoading.value = false
		}
	}
	defineExpose({ open }) // 提供 open 方法，用于打开弹窗

	const activeName = ref('0')
	const handleClick = () => {

	}
	// 附件预览
	// const DialogFlieRef = ref()
	// const handleView = async (id : number) => {
	// 	await DialogFlieRef.value.open('预览', 'VIEW', id)
	// }

	// 外部法律法规-查看
	const ItemDetailRef = ref()
	const handleWbView = async (id: number) => {
		await ItemDetailRef.value.open(id)
	}

	// 内部法律法规-查看
	const OutDetailRef = ref()
	const handleView = async (id : number) => {
		await OutDetailRef.value.open(id)
	}
	const handleMxView = () => { }
</script>
