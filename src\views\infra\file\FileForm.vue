<template>
  <Dialog v-model="dialogVisible" title="上传文件">
    <el-radio-group v-model="radioValue" v-if="showRadio">
      <el-radio
        :value="dict.value+''"
        size="large"
        :key="dict.value+''"
        v-for="dict in getStrDictOptions(radioListDic)"
      >
        {{dict.label}}
        <span v-if="dict.uploadFlag == 1" class="color-red font-size-16px">*</span>
      </el-radio>
    </el-radio-group>
    <el-radio-group v-model="radioValue" v-if="showRadioByInterface">
      <el-radio
        :value="dict.value"
        size="large"
        :key="dict.value"
        v-for="dict in radioListByInterface"
      >{{dict.label}}</el-radio>
    </el-radio-group>
    <el-upload
      ref="uploadRef"
      v-model:file-list="fileList"
      :action="uploadUrl"
      :auto-upload="true"
      :data="data"
      :disabled="formLoading"
      :on-change="handleFileChange"
      :on-error="submitFormError"
      :on-exceed="handleExceed"
      :on-success="submitFormSuccess"
      :http-request="httpRequest"
      :before-upload="before_upload"
      :accept="accept"
      :limit="limit"
      :multiple="limit === 1 ? false : true"
      drag
    >
      <i class="el-icon-upload"></i>
      <div class="el-upload__text">
        将文件拖到此处，或
        <em>点击上传</em>
      </div>
      <template #tip>
        <div class="el-upload__tip" style="color: red">提示：仅允许导入 {{ accept }} 格式文件！</div>
      </template>
    </el-upload>
    <template #footer>
      <el-button :disabled="formLoading" :loading='formLoading' type="primary" @click="submitFileForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import { getIntDictOptions, getStrDictOptions } from '@/utils/dict'
import { useUpload } from '@/components/UploadFile/src/useUpload'
import { number } from 'vue-types'

defineOptions({ name: 'InfraFileForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const formLoading = ref(false) // 表单的加载中
const fileList = ref([]) // 文件列表
const data = ref({ path: '' })
const uploadRef = ref()
const radioValue = ref()
const props = defineProps({
  // limit 允许文件上传的最大数量  type文件类型  (file/img)
  limit: {
    default: 100,
    type: Number
  },
  type: {
    default: 'all',
    type: String
  },
  showRadio: {
    default: false,
    type: Boolean
  },
  showRadioByInterface: {
    default: false,
    type: Boolean
  },
  radioListDic: {
    default: '',
    type: String
  },
  radioListByInterface: {
    default: () => [],
    type: Array
  },
  // 是否调用成功提示
  successTip: {
    default: true,
    type: Boolean
  }
})
const accept = ref('.pdf, .docx, .doc, .xlsx, .xls, .jpg, .png, .gif, .zip, .rar')
watch(
  () => props.type,
  (newType) => {
    updateAccept(newType)
  }
)
const updateAccept = (type: string = props.type) => {
  if (type === 'img') {
    accept.value = '.jpg, .png, .gif'
  } else if (type === 'file') {
    accept.value = '.pdf, .docx, .doc, .xlsx, .xls'
  } else if (type === 'docx') {
    accept.value = '.docx'
  } else if (type === 'word') {
    accept.value = '.docx, .doc'
  } else {
    accept.value = '.pdf, .docx, .doc, .xlsx, .xls, .jpg, .png, .gif, .zip, .rar'
  }
}
const { uploadUrl, httpRequest } = useUpload()

/** 打开弹窗 */
const open = async () => {
  console.log(getStrDictOptions(props.radioListDic))
  dialogVisible.value = true
  fileList.value = []
  await nextTick()
  await getDefaultValue()
  updateAccept(props.type)
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 处理上传的文件发生变化 */
const handleFileChange = (file) => {
  data.value.path = file.name
}
const getDefaultValue = () => {
  if (props.showRadio && props.radioListDic) {
    radioValue.value =
      getStrDictOptions(props.radioListDic) && getStrDictOptions(props.radioListDic).length > 0
        ? getStrDictOptions(props.radioListDic)[0].value + ''
        : null
    console.log(radioValue.value)
  }
  if (props.showRadioByInterface) {
    radioValue.value =
      props.radioListByInterface && props.radioListByInterface.length > 0
        ? props.radioListByInterface[0].value + ''
        : null
  }
}
/** 提交表单 */
const submitFileForm = () => {
  if (
    (props.showRadio || props.showRadioByInterface) &&
    (radioValue.value == null || radioValue == undefined)
  ) {
    console.log(getIntDictOptions(props.radioListDic))
    if (
      (props.showRadio && getIntDictOptions(props.radioListDic).length === 0) ||
      (props.showRadioByInterface && props.radioListByInterface?.length === 0)
    ) {
      message.error('请到基础数据-附件类型管理中进行配置')
      return
    }
    message.error('请选择类型')
    return
  }
  if (fileList.value.length == 0) {
    message.error('请上传文件')
    return
  }
  unref(uploadRef)?.submit()
  emit('success', fileList.value, radioValue.value)
  dialogVisible.value = false
  unref(uploadRef)?.clearFiles()
  if (props.successTip) {
    message.success(t('common.createSuccess'))
  }
}

/** 文件上传成功处理 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitFormSuccess = (response: any) => {
  // 清理
  // dialogVisible.value = false
  formLoading.value = false
  // emit('success', fileList.value, radioValue.value)
  // unref(uploadRef)?.clearFiles()
  // message.success(t('common.createSuccess'))
}

/** 上传错误提示 */
const submitFormError = (): void => {
  message.error('上传失败，请您重新上传！')
  formLoading.value = false
}

/** 重置表单 */
const resetForm = () => {
  // 重置上传状态和文件
  formLoading.value = false
  uploadRef.value?.clearFiles()
}
/** 文件数超出提示 */
const handleExceed = (): void => {
  message.error(`最多只能上传${props.limit}个文件！`)
}

// 上传前(限制上传文件类型)
const before_upload = (file) => {
  if (accept.value == '.docx') {
    // 截取上传文件的后缀名
    let fileType = file.name.substring(file.name.lastIndexOf('.') + 1)
    console.log(fileType)
    // 判断文件名的类型，允许多种就判断多个
    if (fileType == 'docx') {
      console.log(file)
      formLoading.value = true
    } else {
      message.error(`文件类型必须为.docx格式`)
      // 返回false 就不会执行上传操作了
      return false
    }
    // 限制判断多个
    // if (!["pdf", "doc", "docx"].includes(fileType)) {
    //    ElMessage.error("文件类型必须为pdf格式");
    //    return false;
    // }
  }
  formLoading.value = true
}
</script>
