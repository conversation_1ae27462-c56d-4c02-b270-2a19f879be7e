<!--
* @Author: li<PERSON><PERSON>g
* @Date: 2024-09-11 15:19:58
* @Description: 审计方案库详情=>
-->
<template>
	<Dialog v-model="dialogVisible" :scroll="true" title="详情" width="70%">
		<el-descriptions :column="1" border>
			<el-descriptions-item label="方案名称">{{ detailData.progName }}</el-descriptions-item>
			<el-descriptions-item label="审计类型">{{ detailData.auditName }}</el-descriptions-item>
			<el-descriptions-item label="方案状态">
				<dict-tag type="audit_document_status" :value="detailData.status" />
			</el-descriptions-item>
		</el-descriptions>
		<el-tabs v-model="activeName" class="demo-tabs">
			<el-tab-pane label="模版列表" name="0">
				<el-table :data="detailData.fileApiVOS" border :stripe="true" :show-overflow-tooltip="true" max-height="500px">
					<el-table-column label="版本号" align="center" prop="version" width="120" />
					<el-table-column label="文档名称" align="left" prop="docName" min-width="120">
						<template #default="scope">
							<el-link :underline="false" type="primary" @click="handleView('预览','VIEW', scope.row.fileId)">{{ scope.row.docName }}</el-link>
						</template>
					</el-table-column>
					<el-table-column label="创建人" align="center" prop="creatorName" width="120" />
					<el-table-column label="创建时间" align="center" prop="createTime" width="200">
						<template #default="scope">
							<span>{{ formatDate(scope.row.createTime) }}</span>
						</template>
					</el-table-column>
					<el-table-column label="状态" align="center" prop="status" width="100">
						<template #default="scope">
							<dict-tag :type="'audit_data_list_template_status'" :value="scope.row.status" />
						</template>
					</el-table-column>
					<el-table-column label="操作" align="center" width="150">
						<template #default="scope">
							<el-button link type="primary" @click="handleDownload(scope.row.fileUrl,scope.row.docName)">下载</el-button>
						</template>
					</el-table-column>
				</el-table>
			</el-tab-pane>
		</el-tabs>
		<el-tabs v-model="activeName" class="demo-tabs">
			<el-tab-pane label="审计事项" name="0">
				<el-table :data="detailData.treesResListVOS" border :stripe="true" :show-overflow-tooltip="true" max-height="500px">
					<el-table-column type="index" label="序号" width="80" align="center"/>
					<el-table-column label="事项名称" align="left" prop="name" min-width="180" />
					<el-table-column label="上级事项" align="left" prop="parentName" min-width="180"/>
				</el-table>
			</el-tab-pane>
		</el-tabs>
	</Dialog>

	<!-- 预览附件 -->
	<DialogFlie ref="DialogFlieRef" />
</template>

<script lang="ts" setup>
	import { DICT_TYPE, NumberDictDataType, getIntDictOptions } from '@/utils/dict'
	import { formatDate } from '@/utils/formatTime'
	import { TankProgrammesApi, AddVO, matterVo, fileListVo } from '@/api/jobmanger/solutionRepository'
	import { downFile } from '@/utils/fileName'
	defineOptions({ name: 'Detail' })

	const formRef = ref() // 表单 Ref
	const message = useMessage() // 消息弹窗
	const dialogVisible = ref(false) // 弹窗的是否展示
	const detailLoading = ref(false) // 表单地加载中
	const detailData = ref({}) // 详情数据
	const activeName = ref('0')
	/** 打开弹窗 */
	const open = async (id : number) => {
		dialogVisible.value = true
		// 设置数据
		detailLoading.value = true
		try {
			detailData.value = await TankProgrammesApi.getDetail(id)
		} finally {
			detailLoading.value = false
		}
	}
	defineExpose({ open }) // 提供 open 方法，用于打开弹窗

	// 附件预览
	const DialogFlieRef = ref()
	const handleView = async (name: string, type: string, id: number) => {
		await DialogFlieRef.value.open(name, type, id)
	}

	 //下载
	const handleDownload = async(url : string, name: string) => {
		// 下载的二次确认
    	await message.confirm('是否确认下载？')
		  downFile(url, name)
	}
</script>
