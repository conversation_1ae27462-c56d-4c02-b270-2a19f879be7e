// 饼状图颜色
export const colorList = [
    {
      type: "linear",
      x: 0,
      y: 1,
      x2: 1,
      y2: 0,
      colorStops: [
        {
          offset: 0,
          color: "rgba(187, 200, 222, 1)", // 100% 处的颜色
        },
        {
          offset: 1,
          
          color: "rgba(30, 83, 146, 1)", // 0% 处的颜色
        },
      ],
      globalCoord: false, // 缺省为 false
    },
    {
      type: "linear",
      x: 0,
      y: 1,
      x2: 1,
      y2: 1,
      colorStops: [
        {
          offset: 0,
          color: "rgba(231, 202, 102, 1)", // 0% 处的颜色
        },
        {
          offset: 1,
          color: "rgba(253, 148, 24, 1)", // 100% 处的颜色
        },
      ],
      globalCoord: false, // 缺省为 false
    },
    {
      type: "linear",
      x: 1,
      y: 0,
      x2: 1,
      y2: 1,
      colorStops: [
        {
          offset: 0,
          color: "rgba(136, 152, 184, 1)", // 0% 处的颜色
        },
        {
          offset: 1,
          color: "rgba(152, 79, 106, 1)", // 100% 处的颜色
        },
      ],
      globalCoord: false, // 缺省为 false
    },
    {
      type: "linear",
      x: 1,
      y: 1,
      x2: 0,
      y2: 1,
      colorStops: [
        {
          offset: 0,
          color: "rgba(229, 176, 102, 1)", // 0% 处的颜色
        },
        {
          offset: 1,
          color: "rgba(180, 93, 85, 1)", // 100% 处的颜色
        },
      ],
      globalCoord: false, // 缺省为 false
    },
    {
      type: "linear",
      x: 1,
      y: 1,
      x2: 1,
      y2: 0,
      colorStops: [
        {
          offset: 0,
          color: "rgba(139, 188, 234, 1)", // 0% 处的颜色
        },
        {
          offset: 1,
          color: "rgba(179, 215, 250, 1)", // 100% 处的颜色
        },
        

      ],
      globalCoord: false, // 缺省为 false
    },
    {
      type: "linear",
      x: 0,
      y: 1,
      x2: 1,
      y2: 1,
      colorStops: [
        {
          offset: 0,
          color: "rgba(223, 231, 253, 1)", // 0% 处的颜色
        },
        {
          offset: 1,
          color: "rgba(183, 198, 231, 1)", // 100% 处的颜色
        },
      ],
      globalCoord: false, // 缺省为 false
    },
  ];