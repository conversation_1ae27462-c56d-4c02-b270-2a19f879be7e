<!-- 画像概览 -->
<template>
  <ContentWrap>
    <div class="flex_title mb-20px">
      <div class="title-left"></div>
      <div class="pl-8px">企业画像</div>
    </div>
    <!-- 搜索工作栏 -->
    <div class="w-100% flex flex-row">
      <el-form :model="queryParams" ref="queryFormRef" :inline="true" class="common-search-form" label-width="60px">
        <el-form-item label="单位" prop="deptId">
          <el-tree-select v-model="queryParams.entName" ref="treeRef" :clearable="false" placeholder="请选择所属单位"
            :data="deptList" check-strictly :expand-on-click-node="false" :check-on-click-node="true"
            :default-expand-all="false" highlight-current @change="getList" node-key="name" :load="loadNode"
            :default-expanded-keys="defaultExpandedKeys" :filter-node-method="filterNode" lazy class="!w-300px">
            <template #default="{ data: { name } }">{{ name }}</template>
          </el-tree-select>
        </el-form-item>
        <el-form-item label="年份" prop="auditYear">
          <el-date-picker v-model="queryParams.auditYear" type="year" value-format="YYYY" class="!w-200px"
            placeholder="请选择年份" :clearable="false" />
        </el-form-item>
      </el-form>
      <div class="right-search-btn">
        <el-button type="primary" @click="handleQuery">
          <Icon icon="ep:search" class="mr-5px" />搜索
        </el-button>
        <!-- <el-button @click="resetQuery"> <Icon icon="ep:refresh" class="mr-5px" />重置 </el-button> -->
      </div>
    </div>
    <el-divider />
    <div class="flex">
      <div class="mr-16px pl-7px">
        <el-avatar shape="square" :size="88" :src="squareUrl" style="border-radius: 10px;" />
      </div>
      <div>
        <div class="title mb-14px">{{ list.entName }}</div>
        <div>
          <el-tag size="large" type="primary">画像指标总数: 67</el-tag>
          <el-tag class="ml-10px" size="large" type="primary">企业画像报告</el-tag>
        </div>
      </div>
    </div>
  </ContentWrap>
  <ContentWrap>
    <div class="flex_title mb-20px">
      <div class="title-left"></div>
      <div class="pl-8px">企业基本信息(12个)</div>
    </div>
    <el-row :gutter="6">
      <el-col :span="6" :xs="24">
        <div class="bg_img1 bg_img">
          <div class="info_more">
            <div class="bg_more1 mr--4px" @click="handleLink(1)">
              <el-link :class="{ 'white-link': true }">查看更多</el-link>
              <Icon icon="ep:arrow-right" />
            </div>
          </div>
          <div class="info_title pl-30px">
            <div class="pr-8px">企业基本信息</div>
            <el-tag effect="plain" round type="info" style="color: #000"> 1个指标 </el-tag>
          </div>
          <div class="pt-20px pb-20px">
            <el-row>
              <el-col :span="8" :xs="24">
                <div class="text-center info_top info_top_color">是</div>
                <div class="text-center info_bot info_top_color">是否上市</div>
              </el-col>
              <el-col :span="8" :xs="24">
                <div class="text-center info_top info_top_color">{{ list.establishDate }}</div>
                <div class="text-center info_bot info_top_color">成立时间</div>
              </el-col>
              <el-col :span="8" :xs="24">
                <div class="text-center info_top info_top_color">{{ list.regCapi }}</div>
                <div class="text-center info_bot info_top_color">注册资本</div>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-col>
      <el-col :span="6" :xs="24">
        <div class="bg_img2 bg_img">
          <div class="info_more">
            <div class="bg_more1 mr--4px" @click="handleLink(2)">
              <el-link :class="{ 'white-link': true }">查看更多</el-link>
              <Icon icon="ep:arrow-right" />
            </div>
          </div>
          <div class="info_title pl-30px">
            <div class="pr-8px">组织信息</div>
            <el-tag effect="plain" type="info" round style="color: #000"> 5个指标 </el-tag>
          </div>
          <div class="pb-20px pt-20px">
            <el-row>
              <el-col :span="8" :xs="24">
                <div class="text-center info_top info_top_color">1667</div>
                <div class="text-center info_bot info_top_color">职工人数</div>
              </el-col>
              <el-col :span="8" :xs="24">
                <div class="text-center info_top_color"><span class="info_top info_top_color">99.33</span>%</div>
                <div class="text-center info_bot info_top_color">股权占比</div>
              </el-col>
              <el-col :span="8" :xs="24">
                <div class="text-center info_top_color"><span class="info_top info_top_color">3</span>家</div>
                <div class="text-center info_bot info_top_color">对外投资</div>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-col>
      <el-col :span="6" :xs="24">
        <div class="bg_img3 bg_img">
          <div class="info_more">
            <div class="bg_more1 mr--4px" @click="handleLink(3)">
              <el-link :class="{ 'white-link': true }">查看更多</el-link>
              <Icon icon="ep:arrow-right" />
            </div>
          </div>
          <div class="info_title pl-30px">
            <div class="pr-8px">法律诉讼</div>
            <el-tag effect="plain" round type="info" style="color: #000"> 3个指标 </el-tag>
          </div>
          <div class="pb-20px pt-20px">
            <el-row>
              <el-col :span="8" :xs="24">
                <div class="text-center info_top_color"><span class="info_top info_top_color">22</span>个</div>
                <div class="text-center info_bot info_top_color">纠纷案件数</div>
              </el-col>
              <el-col :span="8" :xs="24">
                <div class="text-center info_top_color"><span class="info_top info_top_color">4</span>个</div>
                <div class="text-center info_bot info_top_color">行政处罚数</div>
              </el-col>
              <el-col :span="8" :xs="24">
                <div class="text-center info_top_color"><span class="info_top info_top_color">99</span>个</div>
                <div class="text-center info_bot info_top_color">动产抵押</div>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-col>
      <el-col :span="6" :xs="24">
        <div class="bg_img4 bg_img">
          <div class="info_more">
            <div class="bg_more1 mr--4px" @click="handleLink(4)">
              <el-link :class="{ 'white-link': true }">查看更多</el-link>
              <Icon icon="ep:arrow-right" />
            </div>
          </div>
          <div class="info_title pl-30px">
            <div class="pr-8px">内审外查</div>
            <el-tag effect="plain" type="info" round style="color: #000"> 3个指标 </el-tag>
          </div>
          <div class="pb-20px pt-20px">
            <el-row>
              <el-col :span="8" :xs="24">
                <div class="text-center info_top_color"><span class="info_top info_top_color">33</span>个</div>
                <div class="text-center info_bot info_top_color">内审外查项目数</div>
              </el-col>
              <el-col :span="8" :xs="24">
                <div class="text-center info_top_color"><span class="info_top info_top_color">14</span>个</div>
                <div class="text-center info_bot info_top_color">审计发现问题数</div>
              </el-col>
              <el-col :span="8" :xs="24">
                <div class="text-center info_top_color"><span class="info_top info_top_color">99</span>%</div>
                <div class="text-center info_bot info_top_color">问题整改完成率</div>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-col>
    </el-row>
  </ContentWrap>
  <ContentWrap>
    <div class="flex_title">
      <div class="title-left"></div>
      <div class="pl-8px">审计画像分析(55个)</div>
    </div>
    <div class="pt-30px pb-40px text-center tb_title">国资监管指标(8个)</div>
    <el-row :gutter="16" class="pl-20px flex flex-row flex-nowrap!">
      <el-col :span="12" :xs="24">
        <div :class="oneChart" ref="oneChartRef" style="height: 400px; width: 100%"></div>
      </el-col>
      <el-divider direction="vertical" class="h-auto!" />
      <el-col :span="12" :xs="24">
        <div :class="twoChart" ref="twoChartRef" style="height: 400px; width: 100%"></div>
      </el-col>
    </el-row>
    <div class="pt-30px pb-40px text-center tb_title">集团考评指标(32个)</div>
    <el-row :gutter="16" class="pl-20px flex flex-row flex-nowrap!">
      <el-col :span="12" :xs="24">
        <div :class="threeChart" ref="threeChartRef" style="height: 400px; width: 100%"></div>
      </el-col>
      <el-divider direction="vertical" class="h-auto!" />
      <el-col :span="12" :xs="24">
        <div :class="fourChart" ref="fourChartRef" style="height: 400px; width: 100%"></div>
      </el-col>
    </el-row>
    <div class="pt-30px pb-40px text-center tb_title">重点业务指标(15个)</div>
    <el-row :gutter="16" class="pl-20px flex flex-row flex-nowrap!">
      <el-col :span="8" :xs="24">
        <div :class="fiveChart" ref="fiveChartRef" style="height: 400px; width: 100%"></div>
        <div class="pt-20px">
          <el-row class="bottom_tag flex items-center">
            <el-col :span="8" :xs="24">
              <div class="text-center info_top_span"><span class="info_top">25</span>亿元</div>
              <div class="text-center info_bot">货币资金</div>
            </el-col>
            <el-col :span="8" :xs="24">
              <div class="text-center info_top_span"><span class="info_top">25</span>亿元</div>
              <div class="text-center info_bot">存货</div>
            </el-col>
            <el-col :span="8" :xs="24">
              <div class="text-center info_top_span"><span class="info_top">60</span>亿元</div>
              <div class="text-center info_bot">固定资产</div>
            </el-col>
          </el-row>
        </div>
      </el-col>
      <el-divider direction="vertical" class="h-auto!" />
      <el-col :span="8" :xs="24">
        <div :class="sixChart" ref="sixChartRef" style="height: 400px; width: 100%"></div>
        <div class="pt-20px">
          <el-row class="bottom_tag flex items-center">
            <el-col :span="6" :xs="24">
              <div class="text-center info_top_span"><span class="info_top">25</span>个</div>
              <div class="text-center info_bot">客户数量</div>
            </el-col>
            <el-col :span="6" :xs="24">
              <div class="text-center info_top_span"><span class="info_top">25</span>亿元</div>
              <div class="text-center info_bot">应收账款余额</div>
            </el-col>
            <el-col :span="6" :xs="24">
              <div class="text-center info_top_span"><span class="info_top">25</span>亿元</div>
              <div class="text-center info_bot">长帐龄余额</div>
            </el-col>
            <el-col :span="6" :xs="24">
              <div class="text-center info_top_span"><span class="info_top">25</span>天</div>
              <div class="text-center info_bot">应收款周转天数</div>
            </el-col>
          </el-row>
        </div>
      </el-col>
      <el-divider direction="vertical" class="h-auto!" />
      <el-col :span="8" :xs="24">
        <div :class="sevenChart" ref="sevenChartRef" style="height: 400px; width: 100%"></div>
        <div class="pt-20px">
          <el-row class="bottom_tag flex items-center">
            <el-col :span="6" :xs="24">
              <div class="text-center info_top_span"><span class="info_top">25</span>个</div>
              <div class="text-center info_bot">供应商数量</div>
            </el-col>
            <el-col :span="6" :xs="24">
              <div class="text-center info_top_span"><span class="info_top">25</span>亿元</div>
              <div class="text-center info_bot">应付账款余额</div>
            </el-col>
            <el-col :span="6" :xs="24">
              <div class="text-center info_top_span"><span class="info_top">25</span>亿元</div>
              <div class="text-center info_bot">长帐龄余额</div>
            </el-col>
            <el-col :span="6" :xs="24">
              <div class="text-center info_top_span"><span class="info_top">25</span>亿元</div>
              <div class="text-center info_bot">合同签约总额</div>
            </el-col>
          </el-row>
        </div>
      </el-col>
    </el-row>
  </ContentWrap>
</template>

<script lang="ts" setup>
import { handleLazyTree } from '@/utils/tree'
import * as DeptApi from '@/api/system/dept'
import Group from '@/assets/imgs/toux.png'
import * as echarts from 'echarts'
import { CompanyBasicInfoApi, CompanyBasicInfoVO } from '@/api/model/companyProfile/auditPortraitAnalysis/companyBasicInfo'
import { colorList } from '../../../decision/decisionAnalysis/index'
defineOptions({
  name: 'Demoview'
})
const queryParams = reactive({
  entName: "山东港口产城融合发展集团有限公司",
  creditNo: "",
  pageNum: 0,
  pageSize: 0,
  auditYear: '',
  code: "zhimeng01",
})

const queryFormRef = ref() // 搜索的表单
const loading = ref(true) // 列表的加载中
const squareUrl = Group
const { push } = useRouter() // 路由跳转
const list = ref({} as CompanyBasicInfoVO) // 列表的数据
/** 查询数据 */
const getList = async () => {
  loading.value = true
  try {
    // const data = await CompanyBasicInfoApi.requestCompanyBasicInfo(queryParams)
    // list.value = data
    // list.value.regCapi = list.value.regCapi.slice(0, list.value.regCapi.indexOf('万'))
  } finally {
    loading.value = false
  }
}
const handleLink = async (val?: number) => {
  // 跳转路由
  switch (val) {
    case 1:
      push({ path: '/model/companyProfile/auditPortraitAnalysis/companyBasicInfo/basicInfo' });
      break
    case 2:
      push({ path: '/model/companyProfile/auditPortraitAnalysis/companyBasicInfo/organizationInfo' });
      break
    case 3:
      push({ path: '/model/companyProfile/auditPortraitAnalysis/companyBasicInfo/risklitigation' });
      break
    case 4:
      push({ path: '/model/companyProfile/auditPortraitAnalysis/companyBasicInfo/internalAuditExternalInvestigation' });
      break
    default:
      //这里是没有找到对应的值处理
      break
  }
}

const color = ['#2A71C0', '#FF9C01', '#AC3C52', '#C96F37', '#95C2ED', '#E6E8F4', '#ABEDD8']

// 第一个echarts
let oneChart: echarts.ECharts | null = null
const oneChartRef = ref()
const getOneChart = async () => {
  oneChart = echarts.init(oneChartRef.value)
  let colorArray = [
    {
      top: '#2A71C0', //绿
      bottom: '#2A71C010'
    },
    {
      top: '#FF9C01', //蓝
      bottom: '#FF9C0110'
    },
    {
      top: '#AC3C52', //黄
      bottom: '#AC3C5210'
    },
    {
      top: '#C96F37', //橙
      bottom: '#C96F3710'
    }
  ]
  oneChart.setOption({
    title: {
      // text: '国资监管指标(8个)',
      textStyle: {
        fontSize: 14
      }
    },
    tooltip: {
      show: true,
      formatter: '{b}<br/> {c}'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    legend: {
      // top: 'bottom',
      data: ['较差', '较低', '平均', '良好', '优秀']
    },
    xAxis: {
      type: 'value',
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed'
        }
      },
      data: ['较差', '较低', '平均', '良好', '优秀'],
      boundaryGap: [0, 0.01],
      interval: 6.5,
      axisLabel: {
        textStyle: {
          color: '#bac0c0',
          fontWeight: 'normal',
          fontSize: '12'
        },
        formatter: function (params) {
          if (params > 24) {
            return '优秀'
          } else if (params > 18) {
            return '良好'
          } else if (params > 12) {
            return '平均'
          } else if (params > 6) {
            return '较低'
          } else {
            return '较差'
          }
        }
      }
    },
    yAxis: {
      type: 'category',
      axisTick: {
        show: false
      },
      data: [
        '盈余现金保障倍数',
        '资本保值增值率',
        '净资产收益率',
        '资产负债率',
        '营业现金比率',
        '全员劳动生产率',
        '研发投入强度',
        '利润总额增长率'
      ]
    },
    series: [
      {
        name: '评价等级',
        type: 'bar',
        barWidth: '22px',
        itemStyle: {
          normal: {
            show: true,
            barBorderRadius: [0, 20, 20, 0],
            color: function (params: any) {
              console.log(params.data, colorArray[Math.floor(params.data / 6)]);
              // return
              return {
                type: 'bar',
                colorStops: [
                  {
                    offset: 0,
                    color: colorArray[Math.floor(params.data / 6)]?.bottom
                  },
                  {
                    offset: 1,
                    color: colorArray[Math.floor(params.data / 6)]?.top
                  },
                ]
              }
            }
          }
        },
        data: [22, 17, 11, 2, 23, 16, 9, 3]
      }
    ]
  })
}
// 第二个echarts
let twoChart: echarts.ECharts | null = null
const twoChartRef = ref()
const getTwoChart = async () => {
  twoChart = echarts.init(twoChartRef.value)
  let color1 = [
    {
      type: "linear",
      x: 0,
      y: 0,
      x2: 0,
      y2: 1,
      colorStops: [
        {
          offset: 0,
          color: "#ff3e01", // 0% 处的颜色
        },
        {
          offset: 1,
          color: "#f6b52a", // 100% 处的颜色
        },
      ],
      globalCoord: false, // 缺省为 false
    },
    {
      type: "linear",
      x: 0,
      y: 0,
      x2: 0,
      y2: 1,
      colorStops: [
        {
          offset: 0,
          color: "#f4c02e", // 0% 处的颜色
        },
        {
          offset: 1,
          color: "#d4dc66", // 100% 处的颜色
        },
      ],
      globalCoord: false, // 缺省为 false
    },
    {
      type: "linear",
      x: 0,
      y: 0,
      x2: 0,
      y2: 1,
      colorStops: [
        {
          offset: 0,
          color: "#c9dc77", // 0% 处的颜色
        },
        {
          offset: 1,
          color: "#8ddad0", // 100% 处的颜色
        },
      ],
      globalCoord: false, // 缺省为 false
    },
    {
      type: "linear",
      x: 0,
      y: 0,
      x2: 0,
      y2: 1,
      colorStops: [
        {
          offset: 0,
          color: "#82dae0", // 0% 处的颜色
        },
        {
          offset: 1,
          color: "#abdce1", // 100% 处的颜色
        },
      ],
      globalCoord: false, // 缺省为 false
    },
    {
      type: "linear",
      x: 0,
      y: 0,
      x2: 0,
      y2: 1,
      colorStops: [
        {
          offset: 0,
          color: "#b3dce1", // 0% 处的颜色
        },
        {
          offset: 1,
          color: "#e5e5e5", // 100% 处的颜色
        },
      ],
      globalCoord: false, // 缺省为 false
    },
  ];
  twoChart.setOption({
    title: {
      text: '资本保值增值率',
      textStyle: {
        fontSize: 14
      }
    },
    backgroundColor: "#fff",
    calculable: true,
    color: color1,
    legend: {
      show: true,
      left: 'left',
      top: '40',
      orient: 'vertical',
    },
    series: [
      {
        zlevel: 1,
        name: "漏斗图",
        type: "funnel",
        left: "25%",
        top: 0,
        bottom: 60,
        width: "45%",
        height: "95%",
        min: 0,
        max: 120,
        minSize: "0%",
        maxSize: "100%",
        sort: "ascending",
        gap: 2,
        label: {
          show: true,
          position: "right",
          width: "180px",
          align: "right",
          formatter: function (params) {
            return params.data.name;
          },
          normal: {
            formatter: "{b}",
            color: "#bbb",
          },
        },
        labelLine: {
          // length: 10,
          lineStyle: {
            width: 0,
            type: "solid",
          },
        },
        itemStyle: {
          borderColor: "#fff",
          borderWidth: 1,
        },
        emphasis: {
          label: {
            fontSize: 20,
          },
        },
        data: [
          { value: 20, name: "优秀", reaVal: "34.34" },
          { value: 40, name: "良好", reaVal: "34.34" },
          { value: 60, name: "平均", reaVal: "34.34" },
          { value: 80, name: "较低", reaVal: "34.34" },
          { value: 100, name: "较差", reaVal: "34.34" },
        ],
      },
    ],
  })
}
// 第三个echarts
let threeChart: echarts.ECharts | null = null
const threeChartRef = ref()
const getThreeChart = async () => {
  threeChart = echarts.init(threeChartRef.value)
  const data = [
    { name: '利润总额', max: 6500 },
    { name: '经营性利润总额', max: 16000 },
    { name: '货物吞吐量', max: 30000 },
    { name: '集装箱吞吐量', max: 38000 },
    { name: '净资产收益率', max: 52000 },
    { name: '营业收入利润率', max: 25000 },
    { name: '全员劳动生产', max: 25000 },
    { name: '营业现金比率', max: 25000 }
  ]
  threeChart.setOption({
    title: {
      text: '领导班子业绩考核(8个)',
      textStyle: {
        fontSize: 14
      }
    },
    tooltip: {
      trigger: 'item'
    },
    legend: {
      data: ['2023年', '2024年'],
      top: 50,
      orient: 'vertical',
      left: 'left'
    },
    radar: {
      shape: "circle",
      indicator: data
    },
    color: color,
    series: [
      {
        name: 'Budget vs spending',
        type: 'radar',
        data: [
          {
            areaStyle: {
              normal: {
                borderColor: "rgba(160, 189, 240, 0.6)",
              },
            },
            value: [4200, 3000, 20000, 35000, 50000, 18000, 1111, 2222],
            name: '2023年'
          },
          {
            areaStyle: {
              normal: {
                borderColor: "rgba(244, 175, 96, 0.3)",
              },
            },
            value: [5000, 14000, 28000, 26000, 42000, 21000, 3333, 6666],
            name: '2024年'
          }
        ]
      }
    ]
  })
}
// 第四个echarts
let fourChart: echarts.ECharts | null = null
const fourChartRef = ref()
const getFourChart = async () => {
  fourChart = echarts.init(fourChartRef.value)
  fourChart.setOption({
    title: {
      text: '综合绩效指标评价(24个)',
      textStyle: {
        fontSize: 14
      }
    },
    tooltip: {
      show: true,
      formatter: '{b}<br/> {c}'
    },
    // legend: {
    //     top: 'bottom',
    //     data: data
    // },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed'
        }
      },
      boundaryGap: [0, 0.01]
    },
    yAxis: {
      type: 'category',
      axisTick: {
        show: false
      },
      data: ['优秀', '良好', '平均', '较低', '较差']
    },
    color: color,
    series: [
      {
        name: '评价等级',
        type: 'bar',
        barWidth: '30px',
        itemStyle: {
          normal: {
            show: true,
            barBorderRadius: [0, 8, 8, 0],
          }
        },
        data: [10, 20, 15, 8, 5]
      }
    ]
  })
}
// 第五个echarts
let fiveChart: echarts.ECharts | null = null
const fiveChartRef = ref()
const getFiveChart = async () => {
  fiveChart = echarts.init(fiveChartRef.value)
  const data = [
    { value: 335, name: '港口业务' },
    { value: 310, name: '贸易业务' },
    { value: 234, name: '施工业务' },
    { value: 135, name: '物流业务' },
    { value: 135, name: '其他业务' }
  ]
  fiveChart.setOption({
    title: {
      text: '资产指标(5个)',
      textStyle: {
        fontSize: 14
      }
    },
    tooltip: {
      trigger: "item",
      formatter: "{b} : {c} ({d}%)",
    },
    legend: {
      top: 'bottom'
    },
    calculable: false,
    series: [
      {
        name: "姓名",
        type: "pie",
        radius: [10, "60%"], //第一个参数控制玫瑰图圆心大小,第二个参数控制玫瑰图大小
        center: ["55%", "50%"],
        roseType: "radius",
        width: "100%", // for funnel
        max: 80, // for funnel
        label: {
					show: false
				},
        itemStyle: {
          normal: {
            color: function (params) {
              return colorList[params.dataIndex];
            },
          },
          emphasis: {
            label: {
              show: false,
            },
            labelLine: {
              show: false,
            },
          },
        },
        // data:data,
        data: data,
      },
    ],
  })
}
// 第六个echarts
let sixChart: echarts.ECharts | null = null
const sixChartRef = ref()
const getSixChart = async () => {
  sixChart = echarts.init(sixChartRef.value)
  sixChart.setOption({
    title: {
      text: '客户指标(5个)',
      textStyle: {
        fontSize: 14
      }
    },
    tooltip: {
      trigger: 'item'
    },
    legend: {
      top: 'bottom',
      data: ['数量', '收入', '应收款余额']
    },
    xAxis: {
      type: 'category',
      data: ['2022', '2023', '2024']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '数量',
        type: 'bar',
        barWidth: '24px',
        itemStyle: {
          normal: {
            barBorderRadius: [8, 8, 0, 0],
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: "#2A71C0",
                },
                {
                  offset: 1,
                  color: "#2A71C040",
                },
              ],
              globalCoord: false,
            },
          },
        },
        data: [50, 100, 200]
      },
      {
        name: '收入',
        type: 'bar',
        barGap: "0",
        barWidth: '24px',
        itemStyle: {
          normal: {
            barBorderRadius: [8, 8, 0, 0],
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: "#FF9C01",
                },
                {
                  offset: 1,
                  color: "#FF9C0140",
                },
              ],
              globalCoord: false,
            },
          },
        },
        data: [30, 60, 90]
      },
      {
        name: '应收款余额',
        type: 'bar',
        barWidth: '24px',
        itemStyle: {
          normal: {
            barBorderRadius: [8, 8, 0, 0],
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: "#AC3C52",
                },
                {
                  offset: 1,
                  color: "#AC3C5240",
                },
              ],
              globalCoord: false,
            },
          },
        },
        data: [150, 80, 100]
      }
    ]
  })
}
// 第七个echarts
let sevenChart: echarts.ECharts | null = null
const sevenChartRef = ref()
const getSevenChart = async () => {
  sevenChart = echarts.init(sevenChartRef.value)
  const data = [
    { value: 335, name: '港口业务' },
    { value: 310, name: '贸易业务' },
    { value: 234, name: '施工业务' },
    { value: 135, name: '物流业务' },
    { value: 135, name: '其他业务' }
  ]
  sevenChart.setOption({
    title: {
      text: '供应商指标(5个)',
      textStyle: {
        fontSize: 14
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b} : {c} ({d}%)'
    },
    legend: {
      top: 'bottom',
      data: data
    },
    color: color,
    series: [
      {
        name: '姓名',
        type: 'pie',
        radius: '50%',
        center: ['50%', '50%'],
        data: data,
        itemStyle: {
          normal: {
            label: {
              show: false,
            },
          },
          labelLine: { show: true },
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  })
}

const getAllApi = async () => {
  await getList()
  await Promise.all([getOneChart()])
  await Promise.all([getTwoChart()])
  await Promise.all([getThreeChart()])
  await Promise.all([getFourChart()])
  await Promise.all([getFiveChart()])
  await Promise.all([getSixChart()])
  await Promise.all([getSevenChart()])
}

/** 搜索按钮操作 */
const handleQuery = () => {
  getAllApi()
}

/** 重置按钮操作 */
// const resetQuery = () => {
//   queryFormRef.value.resetFields()
//   handleQuery()
// }

// 获取单位
interface DeptNode {
  id: number
  masterOrgId?: number | string
  name: string
  parentId: number | string
  children?: DeptNode[]
  isLeaf?: boolean
}
const deptList = ref<DeptNode[]>([]) // 树形结构
const defaultExpandedKeys = ref<(string | number)[]>([])
const getTree = async (id) => {
  deptList.value = []
  const res = await DeptApi.getSimpleDeptList(id)
  deptList.value.push(...handleLazyTree(res, 'id', 'parentId'))
  defaultExpandedKeys.value = deptList.value.map((node) => node.id)
}
const filterNode = (name: string, data: DeptNode) => {
  if (!name) return true
  return data.name.includes(name)
}
const loadNode = async (node: Node, resolve: (data: any[]) => void) => {
  try {
    const nodeId = node.data.id
    if (nodeId == undefined || nodeId == null) {
      return
    }
    const res = await DeptApi.getSimpleDeptList(nodeId)
    const children = handleLazyTree(res, 'id', 'parentId', 'children')
    resolve(children)
  } catch (error) {
    resolve([])
  }
}

/** 初始化 **/
onMounted(() => {
  getAllApi()
  getTree(0)
})
</script>

<style scoped>
.white-link {
  color: #fff !important;
  /* 使用 !important 确保样式优先级 */
}

.title {
  width: 442px;
  height: 38px;
  font-family:
    Source Han Sans,
    Source Han Sans;
  font-weight: 500;
  font-size: 26px;
  color: #333333;
  line-height: 38px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}

.flex_title {
  display: flex;
  align-items: center;
  line-height: 30px;
  border-bottom: 1px solid #cacaca;
  font-family:
    Source Han Sans,
    Source Han Sans;
  font-weight: 700;
  font-size: 16px;
  color: #333333;
}

.title-left {
  width: 5px;
  height: 15px;
  background: #2f4cad;
  border-radius: 0px 0px 0px 0px;
}

/deep/.el-tabs__nav-wrap::after {
  /* 去掉下划线 */
  position: static !important;
}

.bg_img {
  /* flex flex-col flex-justify-around pt-9px */
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  /* padding-top: 9px; */
}

.bg_img1 {
  height: 212px;
  background-image: url('@/assets/imgs/portraitOverview/bg-qiye.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.bg_img2 {
  height: 212px;
  background-image: url('@/assets/imgs/portraitOverview/bg-zuzhi.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.bg_img3 {
  height: 212px;
  background-image: url('@/assets/imgs/portraitOverview/bg-falv.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.bg_img4 {
  height: 212px;
  background-image: url('@/assets/imgs/portraitOverview/bg-neishen.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.bg_more1 {
  width: 101px;
  background-image: url('@/assets/imgs/portraitOverview/more-qiye.png');
  background-repeat: no-repeat;
  display: flex;
  justify-content: center;
  align-items: center;
}

.bg_more2 {
  width: 101px;
  background-image: url('@/assets/imgs/portraitOverview/more-zuzhi.png');
  background-repeat: no-repeat;
  display: flex;
  justify-content: center;
  align-items: center;
}

.bg_more3 {
  width: 101px;
  background-image: url('@/assets/imgs/portraitOverview/more-falv.png');
  background-repeat: no-repeat;
  display: flex;
  justify-content: center;
  align-items: center;
}

.bg_more4 {
  width: 101px;
  background-image: url('@/assets/imgs/portraitOverview/more-neishen.png');
  background-repeat: no-repeat;
  display: flex;
  justify-content: center;
  align-items: center;
}

.info_more {
  width: 100%;
  height: 27px;
  font-family:
    Source Han Sans,
    Source Han Sans;
  font-weight: 400;
  font-size: 14px;
  color: #ffffff;
  line-height: 27px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  display: flex;
  justify-content: flex-end;
  margin-top: 9px;
}

.info_title {
  height: 26px;
  font-family:
    Source Han Sans,
    Source Han Sans;
  font-weight: 500;
  font-size: 18px;
  color: #fff;
  line-height: 26px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  display: flex;
  align-items: center;
}

.info_top {
  font-family:
    Source Han Sans,
    Source Han Sans;
  font-weight: 700;
  font-size: 22px;
  color: #333333;
  line-height: 32px;
  font-style: normal;
  text-transform: none;
}

.info_bot {
  font-family:
    Source Han Sans,
    Source Han Sans;
  font-weight: 400;
  font-size: 15px;
  color: #889aa1;
  line-height: 22px;
  font-style: normal;
  text-transform: none;
}

.tb_title {
  height: 26px;
  font-family:
    Source Han Sans,
    Source Han Sans;
  font-weight: 700;
  font-size: 18px;
  color: #333333;
  line-height: 26px;
  font-style: normal;
  text-transform: none;
}

.bottom_tag {
  height: 90px;
  background: #f9fafc;
  border-radius: 8px 8px 8px 8px;
}
.info_top_span {
  font-weight: 700;
}
.info_top_color {
  color: #fff;
}
</style>
