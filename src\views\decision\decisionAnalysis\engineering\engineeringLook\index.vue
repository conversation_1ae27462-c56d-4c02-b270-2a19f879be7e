<!-- 工程看板 -->
<template>
	<div class="header">
		<div class="header_title">
			<div class="header_title_size">
				工程审计情况统计
			</div>
			<div class="flex items-center">
				<div class="header_date mr-12px">
					<div class="pr-14px pl-14px">报审日期</div>
					<el-date-picker v-model="queryParams.reportDate" type="daterange" unlink-panels range-separator="至"
						start-placeholder="开始" end-placeholder="结束" class="!w-200px" value-format="YYYY-MM-DD" @change="handleQuery" />
				</div>
				<div class="header_date">
					<div class="pr-14px pl-14px">审定日期</div>
					<el-date-picker v-model="queryParams.auditDate" type="daterange" unlink-panels range-separator="至"
						start-placeholder="开始" end-placeholder="结束" class="!w-90px" value-format="YYYY-MM-DD" @change="handleQuery" />
				</div>
			</div>
		</div>
		<div class="flex items-center pt-12px pr-20px pl-20px">
			<div class="header_card1">
				<div class="header_card_title">
					工程总览
				</div>
				<el-row class="flex items-center pl-20px pt-10px">
					<el-col :span="3" :xs="24">
						<el-image style="width: 64px; height: 64px" :src="url1" fit="cover" />
					</el-col>
					<el-col :span="7" :xs="24">
						<div class="card_number text-center">
							{{homeNumRow.projectNum}}
						</div>
						<div class="card_text text-center">
							开展项目数量
						</div>
					</el-col>
					<el-col :span="7" :xs="24">
						<div class="card_number text-center">
							{{homeNumRow.contractNum}}
						</div>
						<div class="card_text text-center">
							合同
						</div>
					</el-col>
					<el-col :span="7" :xs="24">
						<div class="card_number text-center">
							{{homeNumRow.enNum}}
						</div>
						<div class="card_text text-center">
							工程
						</div>
					</el-col>
				</el-row>
			</div>
			<div class="header_card2">
				<div class="header_card_title">
					工程结算审计
				</div>
				<el-row class="flex items-center pl-20px pt-10px">
					<el-col :span="7" :xs="24">
						<el-image style="width: 64px; height: 64px" :src="url2" fit="cover" />
					</el-col>
					<el-col :span="9" :xs="24">
						<div class="card_number text-center">
							{{homeNumRow.settleEnNum}}
						</div>
						<div class="card_text text-center">
							已报审工程量
						</div>
					</el-col>
					<el-col :span="8" :xs="24">
						<div class="card_number text-center">
							{{homeNumRow.settleAuditedNum}}
						</div>
						<div class="card_text text-center">
							结算审计完成量
						</div>
					</el-col>
				</el-row>
			</div>
			<div class="header_card3">
				<div class="header_card_title">
					招标控制价审计
				</div>
				<el-row class="flex items-center pl-20px pt-10px">
					<el-col :span="7" :xs="24">
						<el-image style="width: 64px; height: 64px" :src="url3" fit="cover" />
					</el-col>
					<el-col :span="9" :xs="24">
						<div class="card_number text-center">
							{{homeNumRow.controlEnNum}}
						</div>
						<div class="card_text text-center">
							已报审工程量
						</div>
					</el-col>
					<el-col :span="8" :xs="24">
						<div class="card_number text-center">
							{{homeNumRow.controlAuditedNum}}
						</div>
						<div class="card_text text-center">
							控制价审计完成量
						</div>
					</el-col>
				</el-row>
			</div>
		</div>
	</div>
	<el-tabs v-model="activeName" class="demo-tabs" @tab-change="handleClick">
		<el-tab-pane v-for="item in editableTabs" :key="item.name" :label="item.title" :name="item.name" />
	</el-tabs>
	<el-row :gutter="8" class="pt-10px">
		<el-col :span="6" :xs="24">
			<ContentWrap>
				<div class="flex_title">
					<div class="title-left"></div>
					<div class="pl-8px">{{activeName == '0'?'结算审计':'控制价审计'}}-审减率TOP5</div>
				</div>
				<el-tabs v-model="activeName2" class="demo-tabs" @tab-change="handleClick2">
					<el-tab-pane v-for="item in filteredTabs" :key="item.name" :label="item.title" :name="item.name" />
				</el-tabs>
				<div :class="oneChart" ref="oneChartRef" style="height:245px;width:100%;"></div>
			</ContentWrap>
		</el-col>
		<el-col :span="6" :xs="24">
			<ContentWrap>
				<div class="flex_title">
					<div class="title-left"></div>
					<div class="pl-8px">{{activeName == '0'?'结算审计':'控制价审计'}}-审计效率TOP5</div>
				</div>
				<el-tabs v-model="activeName3" class="demo-tabs" @tab-change="handleClick3">
					<el-tab-pane v-for="item in editableTabs3" :key="item.name" :label="item.title" :name="item.name" />
				</el-tabs>
				<div :class="twoChart" ref="twoChartRef" style="height:245px;width:100%;"></div>
			</ContentWrap>
		</el-col>
		<el-col :span="6" :xs="24">
			<ContentWrap>
				<div class="flex_title">
					<div class="title-left"></div>
					<div class="pl-8px">{{activeName == '0'?'结算审计':'控制价审计'}}-审减原因统计</div>
				</div>
				<div :class="threeChart" ref="threeChartRef" style="height:288px;width:100%;"></div>
			</ContentWrap>
		</el-col>
		<el-col :span="6" :xs="24">
			<ContentWrap>
				<div class="flex_title">
					<div class="title-left"></div>
					<div class="pl-8px">{{activeName == '0'?'结算审计':'控制价审计'}}-中介完成率TOP5</div>
				</div>
				<div :class="fourChart" ref="fourChartRef" style="height:288px;width:100%;"></div>
			</ContentWrap>
		</el-col>
	</el-row>
	<ContentWrap>
		<div class="flex_title">
			<div class="title-left"></div>
			<div class="pl-8px">{{activeName == '0'?'工程结算':'招标控制价'}}审计报表</div>
		</div>
		<el-row :gutter="8" class="pt-20px">
			<el-col :span="8" :xs="24" >
				<el-card>
					<div class="flex items-center">
						<div class="foot_card_img"><el-image style="width: 69px; height: 69px" :src="url4" fit="cover" /></div>
						<div class="pl-17px pr-18px">
							<div class="foot_card_title">{{activeName == '0'?'工程':'项目'}}总览</div>
							<div class="foot_card_main">前往查看工程项目相关内容，如工程名称、投资单位、施工单位等</div>
						</div>
						<div><el-button type="primary" @click="getView('总览')">前往查看</el-button></div>	
					</div>
				</el-card>
			</el-col>
			<el-col :span="8" :xs="24">
				<el-card>
					<div class="flex items-center">
						<div class="foot_card_img"><el-image style="width: 69px; height: 69px" :src="url5" fit="cover" /></div>
						<div class="pl-17px pr-18px">
							<div class="foot_card_title">{{activeName == '0'?'结算':'控制价'}}审计费</div>
							<div class="foot_card_main">前往查看结算审计费相关内容，如投资单位、中介机构、管理单位等</div>
						</div>
						<div><el-button type="primary" @click="getView(activeName == '0'?'结算审计费':'控制价审计费')">前往查看</el-button></div>		
					</div>
				</el-card>
			</el-col>
			<el-col :span="8" :xs="24">
				<el-card>
					<div class="flex items-center">
						<div class="foot_card_img"><el-image style="width: 69px; height: 69px" :src="url6" fit="cover" /></div>
						<div class="pl-17px pr-18px">
							<div class="foot_card_title">审减分析</div>
							<div class="foot_card_main">前往查看结算审减相关内容，如发现问题、问题项目、问题工程量等</div>
						</div>
						<div><el-button type="primary" @click="getView(activeName == '0'?'结算审减分析':'控制价审减分析')">前往查看</el-button></div>			
					</div>
				</el-card>
			</el-col>
		</el-row>
	</ContentWrap>
</template>

<script lang="ts" setup>
import { EngineeringLookApi, EngineeringLookVO } from '@/api/decision/engineering/engineeringLook'
import * as echarts from 'echarts';
import demoviewIcon1 from '@/assets/imgs/decision/icon-jihua.png';
import demoviewIcon2 from '@/assets/imgs/decision/icon-zhixingzhong.png';
import demoviewIcon3 from '@/assets/imgs/decision/icon-yiwancheng.png'

import demoviewIcon4 from '@/assets/imgs/decision/Group15.png'
import demoviewIcon5 from '@/assets/imgs/decision/Group16.png'
import demoviewIcon6 from '@/assets/imgs/decision/Group17.png'
defineOptions({
	name: 'EngineeringLook'
})
const queryParams = reactive({
	reportDate: undefined,
	auditDate: undefined,	
	top: undefined,
})
const loading = ref(true) // 列表的加载中
const homeNumRow = ref({} as EngineeringLookVO)
const settleMinusBoard = ref({
	proOwnersOrg:{},
	org:{},
	buildOrg:{},
}) // 审计项目-审减
const settleEfficiencyBoard = ref({
	initAudit:{},
	lastAudit:{},
	auditReport:{},
}) // 结算-审计效率
const settleQuestionBoard = ref({}) // 结算-审减原因统计
const settleFinishNumBoard = ref({}) // 结算-中介审计完成率
const controlMinusBoard = ref({
	proOwnersOrg:{},
	org:{},
	buildOrg:{},
}) // 控制价设计-审减
const controlEfficiencyBoard = ref({
	initAudit:{},
	lastAudit:{},
	auditReport:{},
}) // 招标控制价设计-审计效率
const controlQuestionBoard = ref({}) // 招标控制价设计-审减原因统计
const controlFinishNumBoard = ref({}) // 招标控制价设计-审计项目完成项目数

const url1 = ref(demoviewIcon1)
const url2 = ref(demoviewIcon2)
const url3 = ref(demoviewIcon3)
const url4 = ref(demoviewIcon4)
const url5 = ref(demoviewIcon5)
const url6 = ref(demoviewIcon6)

const getAllApi = async () => {
	homeNumRow.value = await EngineeringLookApi.getHomeNum(queryParams)
	// 工程结算审计
	settleMinusBoard.value = await EngineeringLookApi.getSettleMinusBoard(queryParams)
	settleEfficiencyBoard.value = await EngineeringLookApi.getSettleEfficiencyBoard(queryParams)
	settleQuestionBoard.value = await EngineeringLookApi.getSettleQuestionBoard(queryParams)
	settleFinishNumBoard.value = await EngineeringLookApi.getSettleFinishNumBoard(queryParams)
	// 招标控制价审计
	controlMinusBoard.value = await EngineeringLookApi.getControlMinusBoard(queryParams)
	controlEfficiencyBoard.value = await EngineeringLookApi.getControlEfficiencyBoard(queryParams)
	controlQuestionBoard.value = await EngineeringLookApi.getControlQuestionBoard(queryParams)
	controlFinishNumBoard.value = await EngineeringLookApi.getControlFinishNumBoard(queryParams)
	await Promise.all([getOneChart(settleMinusBoard.value.proOwnersOrg)])
	await Promise.all([getTwoChart(settleEfficiencyBoard.value.initAudit)])
	await Promise.all([getThreeChart(settleQuestionBoard.value)])
	await Promise.all([getFourChart(settleFinishNumBoard.value)])
	loading.value = false
}

/** 搜索按钮操作 */
const handleQuery = () => {
	console.log('000')
	getAllApi()
}

const activeName = ref('0')
const editableTabs = ref([
	{
		title: '工程结算审计',
		name: '0',
	},
	{
		title: '招标控制价审计',
		name: '1',
	}
])
const handleClick = async (type) => {
	activeName.value = type
	activeName2.value = '0'
	activeName3.value = '0'
	switch (type) {
		case '0':
			await Promise.all([getOneChart(settleMinusBoard.value.proOwnersOrg)])
			await Promise.all([getTwoChart(settleEfficiencyBoard.value.initAudit)])
			await Promise.all([getThreeChart(settleQuestionBoard.value)])
			await Promise.all([getFourChart(settleFinishNumBoard.value)])
		break
		case '1':
			await Promise.all([getOneChart(controlMinusBoard.value.proOwnersOrg)])
			await Promise.all([getTwoChart(controlEfficiencyBoard.value.initAudit)])
			await Promise.all([getThreeChart(controlQuestionBoard.value)])
			await Promise.all([getFourChart(controlFinishNumBoard.value)])
		break
		default:
		//这里是没有找到对应的值处理
		break
	}
}
// 计算属性：根据 activeName 过滤标签
const filteredTabs = computed(() => {
  if (activeName.value === '1') {
    return editableTabs2.value.filter(item => item.name !== '2');
  }
  return editableTabs2.value;
});

const activeName2 = ref('0')
const editableTabs2 = ref([
	{
		title: '投资单位（甲方）',
		name: '0',
	},
	{
		title: '中介机构',
		name: '1',
	},
	{
		title: '施工单位（乙方）',
		name: '2',
	}
])
const handleClick2 = async (type) => {
	if(activeName.value == '0'){
		switch (type) {
			case '0':
				await Promise.all([getOneChart(settleMinusBoard.value.proOwnersOrg)])
			break
			case '1':
				await Promise.all([getOneChart(settleMinusBoard.value.org)])
			break
			case '2':
				await Promise.all([getOneChart(settleMinusBoard.value.buildOrg)])
			break
			default:
			//这里是没有找到对应的值处理
			break
		}
	}else{
		switch (type) {
			case '0':
				await Promise.all([getOneChart(controlMinusBoard.value.proOwnersOrg)])
			break
			case '1':
				await Promise.all([getOneChart(controlMinusBoard.value.org)])
			break
			case '2':
				await Promise.all([getOneChart(controlMinusBoard.value.buildOrg)])
			break
			default:
			//这里是没有找到对应的值处理
			break
		}
	}
}

const activeName3 = ref('0')
const editableTabs3 = ref([
	{
		title: '初审效率',
		name: '0',
	},
	{
		title: '终审效率',
		name: '1',
	},
	{
		title: '审计报告效率',
		name: '2',
	}
])
const handleClick3 = async (type) => {
	if(activeName.value == '0'){
		switch (type) {
			case '0':
				await Promise.all([getTwoChart(settleEfficiencyBoard.value.initAudit)])
			break
			case '1':
				await Promise.all([getTwoChart(settleEfficiencyBoard.value.lastAudit)])
			break
			case '2':
				await Promise.all([getTwoChart(settleEfficiencyBoard.value.auditReport)])
			break
			default:
			//这里是没有找到对应的值处理
			break
		}
	}else{
		switch (type) {
			case '0':
				await Promise.all([getTwoChart(controlEfficiencyBoard.value.initAudit)])
			break
			case '1':
				await Promise.all([getTwoChart(controlEfficiencyBoard.value.lastAudit)])
			break
			case '2':
				await Promise.all([getTwoChart(controlEfficiencyBoard.value.auditReport)])
			break
			default:
			//这里是没有找到对应的值处理
			break
		}
	}
}
const color = ['#2A71C0', '#FF9C01', '#AC3C52', '#C96F37', '#95C2ED', '#E6E8F4', '#ABEDD8']

// 第一个echarts
let oneChart: echarts.ECharts | null = null;
const oneChartRef = ref()
const getOneChart = async (row) => {
	console.log(row,'-----------');
	const settleAmount = row.list.map((item) => item.settleAmount)
	const jtsjJsmoney = row.list.map((item) => item.jtsjJsmoney)
	const zjjgSjzj = row.list.map((item) => item.zjjgSjzj)
	const zjjgSjl = row.list.map((item) => item.zjjgSjl)
	oneChart = echarts.init(oneChartRef.value)
	oneChart.setOption({
		color: color,
		grid: {
			top: "5%",
		},
		tooltip: {
			trigger: 'axis',
			axisPointer: {
				type: 'cross',
				crossStyle: {
					color: '#999'
				}
			}
		},
		legend: {
			data: ['报审金额(万元)', '审定金额(万元)', '审减金额(万元)', '审减率'],
			itemWidth: 14,
			top: 'bottom',
			itemHeight: 14,
            textStyle: {
                lineHeight: 14
            }
		},
		xAxis: [
			{
				type: 'category',
				data: row.name,
				axisPointer: {
					type: 'shadow'
				},
				axisLabel: {
					interval: 0,
					formatter: function (value) {
                        // 返回简短版本的标签文本
                        return value.substring(0, 1) + '...'; // 截取前8个字符并添加省略号
                    }
				}
			}
		],
		yAxis: [
			{
				type: 'value',
			},
			{
				type: 'value',
				splitLine: {
					show: false,
				}
			}
		],
		series: [
			{
				name: '报审金额(万元)',
				type: 'bar',
				data: settleAmount,
				barMaxWidth: 40,
				itemStyle: {
                    borderRadius: [10, 10, 0, 0]
                }
			},
			{
				name: '审定金额(万元)',
				type: 'bar',
				data: jtsjJsmoney,
				barMaxWidth: 40,
				itemStyle: {
                    borderRadius: [10, 10, 0, 0]
                }
			},
			{
				name: '审减金额(万元)',
				type: 'bar',
				data: zjjgSjzj,
				barMaxWidth: 40,
				itemStyle: {
                    borderRadius: [10, 10, 0, 0]
                }
			},
			{
				name: '审减率',
				type: 'line',
				yAxisIndex: 1,
				data: zjjgSjl
			}
		]
	})
}

// 第二个echarts
let twoChart: echarts.ECharts | null = null;
const twoChartRef = ref()
const getTwoChart = async (row) => {
	console.log(row,'-----------2');
	twoChart = echarts.init(twoChartRef.value)
	twoChart.setOption({
		color: color,
		grid: {
			top: "5%",
			right: 0
		},
		tooltip: {
			trigger: 'axis',
			axisPointer: {
				type: 'cross',
				crossStyle: {
					color: '#999'
				}
			}
		},
		legend: {
			data: ['初审天数', '平均初审天数'],
			itemWidth: 14,
			top: 'bottom',
			itemHeight: 14,
            textStyle: {
                lineHeight: 14
            }
		},
		xAxis: [
			{
				type: 'category',
				data: row.name,
				axisPointer: {
					type: 'shadow'
				},
				axisLabel: {
					interval: 0,
					formatter: function (value) {
                        // 返回简短版本的标签文本
                        return value.substring(0, 1) + '...'; // 截取前8个字符并添加省略号
                    }
				}
			}
		],
		yAxis: [
			{
				type: 'value',
			}
		],
		series: [
			{
				name: '初审天数',
				type: 'line',
				data: row.totalDays
			},
			{
				name: '平均初审天数',
				type: 'line',
				data: row.avgDays
			}
		]
	})
}

// 第三个echarts
let threeChart: echarts.ECharts | null = null;
const threeChartRef = ref()
const getThreeChart = async (row) => {
	console.log(row,'-----------3');
	threeChart = echarts.init(threeChartRef.value)
	row.forEach(element => {
		element.value = element.rate
		element.name = element.question
	});
	threeChart.setOption({
		color: color,
		// tooltip: {
		//     trigger: 'item',
		// },
		legend: {
			top: 'bottom',
			itemWidth: 14,
			data: row,
			itemHeight: 14,
            textStyle: {
                lineHeight: 14
            },
			formatter: function (value) {
				// 返回简短版本的标签文本
				return value.substring(0, 1) + '...'; // 截取前8个字符并添加省略号
			}
		},
		series: [
			{
				name: '姓名',
				type: 'pie',
				radius: ['50%', '60%'],
				center: ['50%', '40%'],
				avoidLabelOverlap: false,
				padAngle: 5,
				itemStyle: {
					borderRadius: 10
				},
				label: {
					show: false,
					position: 'center',
				},
				emphasis: {
					label: {
						show: true,
						fontSize: 18,
						fontWeight: 500,
					}
				},
				labelLine: {
					show: false
				},
				data: row
			}
		]
	})
}

// 第四个echarts
let fourChart: echarts.ECharts | null = null;
const fourChartRef = ref()
const getFourChart = async (row) => {
	console.log(row,'-----------4');
	let name = '审计完成率'
	let data = row.rate
	if(activeName.value == '0'){
		name = '审计完成率'
		data = row.rate
	}else{
		name = '审计项目完成数量'
		data = row.count
	}
	fourChart = echarts.init(fourChartRef.value)
	fourChart.setOption({
		color: color,
		grid: {
			top: "10%",
			right: 0,
			left: "10%",
			bottom: "10%"
		},
		tooltip: {
			trigger: 'axis',
			axisPointer: {
				type: 'cross',
				crossStyle: {
					color: '#999'
				}
			}
		},
		xAxis: [
			{
				type: 'category',
				data: row.name,
				axisPointer: {
					type: 'shadow'
				},
				axisLabel: {
					interval: 0,
					formatter: function (value) {
                        // 返回简短版本的标签文本
                        return value.substring(0, 1) + '...'; // 截取前8个字符并添加省略号
                    }
				}
			}
		],
		yAxis: [
			{
				type: 'value',
			}
		],
		series: [
			{
				name: name,
				type: 'bar',
				data: data,
				barMaxWidth: 40,
				itemStyle: {
                    borderRadius: [10, 10, 0, 0]
                }
			}
		]
	})
}
const { push } = useRouter() // 路由跳转
// 前往查看
const getView = async (title) => {
	switch (title) {
		case "总览":
			push({path:'/decision/decisionAnalysis/engineering/projectOverview'});
			break
		case "结算审计费":
			push({path:'/decision/decisionAnalysis/engineering/projectSettlement/auditFee'});
			break
		case "控制价审计费":
			push({path:'/decision/decisionAnalysis/engineering/biddingControlPrice/auditFees'});
			break
		case "结算审减分析":
			push({path:'/decision/decisionAnalysis/engineering/projectSettlement/reason'});
			break
		case "控制价审减分析":
			push({path:'/decision/decisionAnalysis/engineering/biddingControlPrice/reasons'});
			break
		default:
			//这里是没有找到对应的值处理
			break
	}
}
/** 初始化 **/
onMounted(() => {
	getAllApi()
})
</script>

<style scoped>
/* 去除输入框边框 */
::v-deep .el-date-editor {
	--el-input-border-color: transparent !important;
	--el-input-bg-color: transparent;
	--el-input-hover-border-color: #fff;
}
::v-deep.demo-tabs .el-tabs__nav-wrap::after {
	/* 去掉下划线 */
	position: static !important;
}
::v-deep.demo-tabs .el-tabs__header{
	margin: 2px !important;
}
.flex_title {
	display: flex;
	align-items: center;
	line-height: 33px;
	font-family: Source Han Sans, Source Han Sans;
	font-weight: 700;
	font-size: 16px;
	color: #333333;
	border-bottom: 1px solid #EEEFF0;
}

.title-left {
	width: 5px;
	height: 15px;
	background: #2F4CAD;
	border-radius: 0px 0px 0px 0px;
}

.header {
	height: 199px;
	border: 2px solid rgba(255, 255, 255, 1);
    background: #F7F7F7;
	border-radius: 8px;
}

.header_title {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 12px 20px 0;
}

.header_title_size {
	height: 26px;
	font-family: Source Han Sans, Source Han Sans;
	font-weight: 500;
	font-size: 18px;
	color: #333333;
	line-height: 26px;
}

.header_date {
	width: 343px;
	height: 34px;
	background: rgba(255, 255, 255, 0.6);
	border-radius: 8px 8px 8px 8px;
	border: 2px solid #FFFFFF;
	font-family: Source Han Sans, Source Han Sans;
	font-weight: 400;
	font-size: 15px;
	color: #999;
	line-height: 22px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.header_card1 {
	flex: 1.5;
	height: 125px;
	background: linear-gradient(160.24deg, #A0BDF0 13.03%, #07397E 86.79%);
	border-radius: 8px 8px 8px 8px;
	border: 2px solid #FFFFFF;
}

.header_card2 {
	flex: 1;
	height: 125px;
	background: linear-gradient(160.24deg, #A0BDF0 13.03%, #07397E 86.79%);
	border-radius: 8px 8px 8px 8px;
	border: 2px solid #FFFFFF;
	margin-left: 6px;
}

.header_card3 {
	flex: 1;
	height: 125px;
	background: linear-gradient(160.24deg, #FCC535 13.03%, #FF7F00 86.79%);
	border-radius: 8px 8px 8px 8px;
	border: 2px solid #FFFFFF;
	margin-left: 6px;
}

.header_card_title {
	height: 23px;
	font-family: Source Han Sans, Source Han Sans;
	font-weight: 400;
	font-size: 16px;
	color: #fff;
	line-height: 23px;
	padding-top: 14px;
	padding-left: 20px;
}

.header_card_item {
	flex: 1;
}

.card_number {
	height: 41px;
	font-family: Source Han Sans, Source Han Sans;
	font-weight: 700;
	font-size: 26px;
	color: #fff;
	line-height: 41px;
}

.card_text {
	height: 22px;
	font-family: Source Han Sans, Source Han Sans;
	font-weight: 400;
	font-size: 15px;
	color: #fff;
	line-height: 22px;
}

.foot_card_title{
	height: 20px;
	font-family: Source Han Sans, Source Han Sans;
	font-weight: 400;
	font-size: 14px;
	color: #333333;
	line-height: 20px;
}

.foot_card_main{
	height: 34px;
	font-family: Source Han Sans, Source Han Sans;
	font-weight: 400;
	font-size: 12px;
	color: #999999;
	line-height: 17px;
	padding-top: 7px;
}
</style>