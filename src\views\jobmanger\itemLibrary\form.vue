<!--
* @Author: lijunliang
* @Date: 2024-09-11 16:31:32
* @Description: 审计事项库表单=>
-->
<template>
	<Dialog :title="dialogTitle" :scroll="true" v-model="dialogVisible" width="70%">
		<el-form class="common-submit-form" ref="formRef" :model="formData" :rules="formRules" v-loading="formLoading">
			<el-row>
				<!-- <el-col :span="8"> -->
					<el-form-item label="风险名称" prop="matterName">
						<el-input v-model="formData.matterName" placeholder="请输入风险名称" class="!w-240px" />
					</el-form-item>
				<!-- </el-col> -->
				<!-- <el-col :span="8"> -->
					<el-form-item v-if="formData.id" label="事项编码" prop="matterCode">
						<el-input v-model="formData.matterCode" disabled placeholder="请输入事项编码" class="!w-240px" />
					</el-form-item>
				<!-- </el-col> -->
				<!-- <el-col :span="8"> -->
					<el-form-item label="所属事项" prop="mattersId">
						<el-tree-select v-model="formData.mattersId" :data="parentListTree" :props="defaultProps"
							check-strictly default-expand-all filterable placeholder="请选择父级编号" class="!w-240px">
							<template #default="{ node }">
								<span class="custom-tree-node">
									<Icon icon="f7:doc" :size="12" />
									{{ node.label }}
								</span>
							</template>
						</el-tree-select>
					</el-form-item>
				<!-- </el-col> -->
				<!-- <el-col :span="8"> -->
					<!-- <el-form-item label="事项类型" prop="matterType">
						<el-select v-model="formData.matterType" placeholder="请选择事项类型" @change="changeType" class="!w-240px">
							<el-option v-for="dict in getIntDictOptions('tank_matters_type')" :key="dict.value"
								:label="dict.label" :value="dict.value"
								:disabled="formData.mattersId==0&&dict.value==1?true:false" />
						</el-select>
					</el-form-item> -->
				<!-- </el-col> -->
				<!-- <el-col :span="8"> -->
					<el-form-item label="风险等级" prop="riskLevelId">
						<el-select v-model="formData.riskLevelId" placeholder="请选择风险等级" class="!w-240px">
							<el-option v-for="dict in getStrDictOptions('tank_matters_risk_level')" :key="dict.value"
								:label="dict.label" :value="dict.value" />
						</el-select>
					</el-form-item>
				<!-- </el-col> -->
			</el-row>
			<el-row>
				<el-col :span="24">
					<el-form-item label="事项描述" prop="mattersDesc" class="del_hover" label-width="110px">
						<!-- <Editor v-model="formData.mattersDesc" width="100%" height="150px" /> -->
						<el-input type="textarea" :autosize="{minRows: 3,maxRows: 3}" v-model="formData.mattersDesc" placeholder="请输入" />
					</el-form-item>
				</el-col>
				<el-col :span="24">
					<el-form-item label="控制措施" prop="controlMeasures"  class="del_hover" label-width="110px">
						<!-- <Editor v-model="formData.controlMeasures" height="150px" /> -->
						<el-input type="textarea" :autosize="{minRows: 3,maxRows: 3}" v-model="formData.controlMeasures" placeholder="请输入" />
					</el-form-item>
				</el-col>
				<el-col :span="24">
					<el-form-item label="审计步骤及方法" prop="stepMethod" class="del_hover" label-width="110px">
						<!-- <Editor v-model="formData.stepMethod" height="150px" /> -->
						<el-input type="textarea" :autosize="{minRows: 3,maxRows: 3}" v-model="formData.stepMethod" placeholder="请输入" />
					</el-form-item>
				</el-col>
				<el-col :span="24">
					<el-form-item label="主要问题表现" prop="mainQues" class="del_hover" label-width="110px">
						<!-- <Editor v-model="formData.mainQues" height="150px" /> -->
						<el-input type="textarea" :autosize="{minRows: 3,maxRows: 3}" v-model="formData.mainQues" placeholder="请输入" />
					</el-form-item>
				</el-col>
				<el-col :span="24">
					<el-form-item label="审计建议" prop="auditSugg" class="del_hover" label-width="110px">
						<!-- <Editor v-model="formData.auditSugg" height="150px" /> -->
						<el-input type="textarea" :autosize="{minRows: 3,maxRows: 3}" v-model="formData.auditSugg" placeholder="请输入" />
					</el-form-item>
				</el-col>
			</el-row>
			<el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
				<el-tab-pane label="外部法律法规" name="0">
					<div>
						<el-button type="primary" plain @click="openList(0)">
							<Icon icon="ep:plus" class="mr-5px" />新增
						</el-button>
					</div>
					<el-table :data="formData.lawsAndRegulations" border :stripe="true" :show-overflow-tooltip="true">
						<el-table-column type="index" label="序号" width="60" align="center"/>
						<el-table-column label="文号" align="left" prop="docNum" min-width="120" />
						<el-table-column label="发布主体" align="center" prop="publishMain" min-width="100" >
							<template #default="scope">
								<dict-tag :type="DICT_TYPE.EXTERNAL_LAWS_REGULATIONS_SUBJECT"
									:value="scope.row.publishMain" />
							</template>
						</el-table-column>
						<el-table-column label="法律法规名称" align="left" prop="lawName" min-width="180" />
						<el-table-column label="生效日期" align="center" prop="effeDate" min-width="100">
							<template #default="scope">
								<span>{{ formatTime(scope.row.effeDate, 'yyyy-MM-dd') }}</span>
							</template>
						</el-table-column>
						<el-table-column label="规定应用描述" align="left" prop="name" min-width="180"/>
						<el-table-column label="法规状态" align="center" prop="lawStatus" min-width="100" >
							<template #default="scope">
								<dict-tag :type="DICT_TYPE.EXTERNAL_LAWS_REGULATIONS_STATE"
									:value="scope.row.lawStatus" />
							</template>
						</el-table-column>
						<el-table-column label="操作" align="center" width="150">
							<template #default="scope">
								<el-button link type="danger"
									@click="handleDelete('lawsAndRegulations',scope.$index)">删除</el-button>
								<!-- <el-button link type="primary" @click="handleView(scope.row.fileId)">预览附件</el-button> -->
								<el-button link type="primary" @click="handleViewDateil(scope.row.id)">查看</el-button>
							</template>
						</el-table-column>
					</el-table>
				</el-tab-pane>
				<el-tab-pane label="内部规章制度" name="1">
					<div>
						<el-button type="primary" plain @click="openList(1)">
							<Icon icon="ep:plus" class="mr-5px" />新增
						</el-button>
					</div>
					<el-table :data="formData.internalRegulations" border :stripe="true" :show-overflow-tooltip="true">
						<el-table-column type="index" label="序号" width="60" align="center"/>
						<el-table-column label="标准编号" align="left" prop="standarNum" min-width="180" />
						<el-table-column label="标准名称" align="left" prop="standarName" min-width="180"/>
						<el-table-column label="制定/修正" align="center" prop="amend">
							<template #default="scope">
								<dict-tag type="internalre_vision" :value="scope.row.amend" />
							</template>
						</el-table-column>
						<!-- <el-table-column label="规定应用描述" align="center" prop="name" /> -->
						<el-table-column label="标准状态" align="center" prop="status">
							<template #default="scope">
								<dict-tag type="Internalst_andard_state" :value="scope.row.status" />
							</template>
						</el-table-column>
						<el-table-column label="归口部门" align="left" prop="deptName" min-width="180"/>
						<el-table-column label="操作" align="center" width="150">
							<template #default="scope">
								<el-button link type="danger" @click="handleDelete('internalRegulations',scope.$index)">删除</el-button>
								<!-- <el-button link type="primary" @click="handleView(scope.row.fileId)">预览附件</el-button> -->
								<el-button link type="primary" @click="GethandleView(scope.row.id)">查看</el-button>
							</template>
						</el-table-column>
					</el-table>
				</el-tab-pane>
				<el-tab-pane label="审计资料" name="2">
					<div>
						<el-button type="primary" plain @click="openList(2)">
							<Icon icon="ep:plus" class="mr-5px" />新增
						</el-button>
					</div>
					<el-table :data="formData.auditData" border :stripe="true" :show-overflow-tooltip="true">
						<el-table-column type="index" label="序号" width="60" align="center" />
						<el-table-column label="资料名称" align="left" prop="materialName" min-width="120" />
						<el-table-column label="事项名称" align="left" prop="mattersName" min-width="180"/>
						<el-table-column label="是否有模版" key="templateFlag" align="center" min-width="100">
							<template #default="{row}">
								<dict-tag :type="'audit_data_list_template'" :value="row.templateFlag" />
							</template>
						</el-table-column>
						<el-table-column label="资料说明" align="left" prop="materialDesc" min-width="180">
							<template #default="{row}">
								<span v-html="row.materialDesc"></span>
							</template>
						</el-table-column>
						<el-table-column label="操作" align="center" width="150">
							<template #default="scope">
								<el-button link type="danger"
									@click="handleDelete('auditData',scope.$index)">删除</el-button>
								<el-button link type="primary" @click="handleView(scope.row.fileId)">预览附件</el-button>
							</template>
						</el-table-column>
					</el-table>
				</el-tab-pane>
				<el-tab-pane v-if="false" label="审计模型" name="3">
					<div>
						<el-button type="primary" plain @click="openList(3)">
							<Icon icon="ep:plus" class="mr-5px" />新增
						</el-button>
					</div>
					<el-table :data="formData.auditModel" border :stripe="true" :show-overflow-tooltip="true">
						<el-table-column type="index" label="序号" width="60" />
						<el-table-column label="模型名称" align="center" prop="name" min-width="120" />
						<el-table-column label="模型编码" align="center" prop="name" />
						<el-table-column label="模型描述" align="center" prop="name" />
						<el-table-column label="模型状态" align="center" prop="name" />
						<el-table-column label="操作" align="center" width="150">
							<template #default="scope">
								<el-button link type="danger"
									@click="handleDelete('auditModel',scope.$indexs)">删除</el-button>
								<el-button link type="primary" @click="handleMxView(scope.row)">查看模型</el-button>
							</template>
						</el-table-column>
					</el-table>
				</el-tab-pane>
			</el-tabs>
		</el-form>
		<template #footer>
			<el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
			<el-button @click="dialogVisible = false">取 消</el-button>
		</template>

	</Dialog>

	<!-- <Dialog :title="dialogListTitle" v-model="dialogListVisible" width="70%">

	</Dialog> -->
	<!-- 文件上传 -->
	<!-- <FileForm ref="formImgRef" @success="handleUploadSuccess" :type="fileType" :limit="fileLimit" /> -->
	<CreateLibraryModal ref='createLibraryModal' @success_receive="receivedData" />
	<!-- 预览附件 -->
	<DialogFlie ref="DialogFlieRef" />
	<!-- 外部法律法规-查看 -->
	<ItemDetail ref="ItemDetailRef" />
	<!-- 查看内部规章制度 -->
	<DetailInterior ref="DetailRef" />
</template>
<script setup lang="ts">
	import { getIntDictOptions, getStrDictOptions, DICT_TYPE } from '@/utils/dict'
	import { ItemLibraryApi, ItemLibraryVO } from '@/api/jobmanger/itemLibrary'
	import FileForm from '@/views/infra/file/FileForm.vue'
	import CreateLibraryModal from './CreateLibraryModal.vue'
	import { defaultProps, handleTree } from '@/utils/tree'
	import * as JobmangerLeftTreeApi from '@/api/jobmanger/jobmangerLeftTree'
	import { formatTime } from '@/utils'
	import DetailInterior from '@/views/jobmanger/regulations/innerregulations/Detail.vue'
	import ItemDetail from '@/views/jobmanger/regulations/outregulations/Detail.vue'
	/** 审计角色 表单 */
	defineOptions({ name: 'ItemForm' })

	const { t } = useI18n() // 国际化
	const message = useMessage() // 消息弹窗

	const dialogVisible = ref(false) // 弹窗的是否展示
	const dialogTitle = ref('') // 弹窗的标题
	const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
	const formType = ref('') // 表单的类型：create - 新增；update - 修改
	const formData = ref({
		id: undefined,
		matterName: undefined,
		mattersId: undefined,
		matterType: 1,
		riskLevelId: undefined,
		mattersDesc: undefined,
		controlMeasures: undefined,
		stepMethod: undefined,
		mainQues: undefined,
		auditSugg: undefined,
		lawsAndRegulations: [] as matterVo[],
		internalRegulations: [],
		auditData: [],
		auditModel: [],
	})
	const formRules = reactive({
		matterName: [{ required: true, message: '事项风险不能为空', trigger: 'blur' }],
		matterType: [{ required: true, message: '事项类型不能为空', trigger: 'change' }],
		mattersId: [{ required: true, message: '不能为空', trigger: 'change' }],
		mattersDesc: [{ required: true, message: '不能为空', trigger: 'blur' }],
		controlMeasures: [{ required: true, message: '不能为空', trigger: 'blur' }],
		stepMethod: [{ required: true, message: '不能为空', trigger: 'blur' }],
		mainQues: [{ required: true, message: '不能为空', trigger: 'blur' }],
		auditSugg: [{ required: true, message: '不能为空', trigger: 'blur' }],
		riskLevelId: [],
	})
	const formRef = ref() // 表单 Ref

	const parentListTree = ref() // 树形结构


	const activeName = ref('0')
	const handleClick = () => {

	}

	// const formImgRef = ref()
	// const fileType = ref('img')
	// const fileLimit = ref(1)
	// // 上传方法
	// const handleImport = (type : string) => {
	// 	fileType.value = type
	// 	fileLimit.value = type === 'file' ? 5 : 1
	// 	formImgRef.value.open()
	// }
	// const handleUploadSuccess = (fileList) => {
	// 	let fileArr =
	// 		fileList && fileList.length > 0
	// 			? fileList.map((item) => {
	// 				return {
	// 					url: item.response.data,
	// 					name: item.name
	// 				}
	// 			})
	// 			: []
	// 	if (fileType.value === 'file') {
	// 		formData.value.attachments = formData.value.attachments.concat(...fileArr)
	// 	} else if (fileType.value === 'img') {
	// 		formData.value.showImg = fileArr
	// 		formRef.value.validateField('showImg')
	// 	}
	// }
	const handleDelete = async (type : string, index : number = 0) => {
		await message.delConfirm()
		formData.value[type].splice(index, 1)
		await message.success(t('common.delSuccess'))
	}

	/** 打开弹窗 */
	const currentId = ref()
	const open = async (type : string, matterId : number, id ?: number) => {
		dialogVisible.value = true
		dialogTitle.value = t('action.' + type)
		formType.value = type
		resetForm()
		// const data = await JobmangerLeftTreeApi.getJobLeftTreeList('/audit/tank-matters-info/get-matterslist')
		const data = await JobmangerLeftTreeApi.getJobLeftTreeList('/audit/tank-trees-type/get?type=4')
		const root : Tree = { id: 0, name: '无父级', children: [] }
		root.children = handleTree(data, 'id', 'parentId')
		parentListTree.value = []
		parentListTree.value = handleTree(data, 'id', 'parentId')
		// 事项类型默认值id要在事项类型数据获取之后赋值，防止新增的时候打开弹框id查询不到对应的名称显示id
		formData.value.mattersId = matterId || undefined
		// 修改时，设置数据
		if (id) {
			currentId.value = id
			formLoading.value = true
			try {
				formData.value = await ItemLibraryApi.getItemLibrary(id)
			} finally {
				formLoading.value = false
			}
		}
	}
	defineExpose({ open }) // 提供 open 方法，用于打开弹窗


	// const dialogListTitle = ref('') // 选择弹窗的标题
	// const dialogListVisible = ref(false) // 选择弹窗的是否展示
	/*
	* @Author: lijunliang
	* @Date: 2024-09-12 10:26:36
	* @Description: 外部法律法规新增=>
	*/
	// 附件预览
	const DialogFlieRef = ref()
	const handleView = async (id : number) => {
		await DialogFlieRef.value.open('预览', 'VIEW', id)
	}

	// 外部管理制度-查看
	const ItemDetailRef = ref()
	const handleViewDateil = async (id : number) => {
		await ItemDetailRef.value.open(id)
	}

	// 内部管理制度-查看
	const DetailRef = ref()
	const GethandleView = async (id : number) => {
		await DetailRef.value.open(id)
	}

	const handleMxView = () => { }
	const createLibraryModal = ref()
	const openList = (typeId : number) => {
		createLibraryModal.value.open(typeId)
	}
	const receivedData = (type : number, data) => {
		if (type === 0) {
			formData.value.lawsAndRegulations = formData.value.lawsAndRegulations??[]
			formData.value.lawsAndRegulations = data.concat(formData.value.lawsAndRegulations)
		} else if (type === 1) {
			formData.value.internalRegulations = formData.value.internalRegulations??[]
			formData.value.internalRegulations = data.concat(formData.value.internalRegulations)
		} else if (type === 2) {
			formData.value.auditData = formData.value.auditData??[]
			formData.value.auditData = data.concat(formData.value.auditData)
		} else if (type === 3) {
			formData.value.auditModel = formData.value.auditModel??[]
			formData.value.auditModel = data.concat(formData.value.auditModel)
		}
	}

	// 事项类型为风险的时候，风险等级为必填
	const validateUsername = (rule, value, callback) =>{
		console.log(rule, value, callback)
	}
	const changeType = (val) =>{
		if(val == 1){
			formRules.riskLevelId = [
				{ required: true, message: '风险等级不能为空', trigger: 'change' },
			]
		}else {
			formRules.riskLevelId = []
		}
	}

	/** 提交表单 */
	const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
	const submitForm = async () => {
		// 校验表单
		await formRef.value.validate()
		// 提交请求
		formLoading.value = true
		try {
			if (formData.value.lawsAndRegulations) {
				formData.value.lawsAndRegulations = formData.value.lawsAndRegulations.map((item) => item.id)
			}
			if (formData.value.internalRegulations) {
				formData.value.internalRegulations = formData.value.internalRegulations.map((item) => item.id)
			}
			if (formData.value.auditData) {
				formData.value.auditData = formData.value.auditData.map((item) => item.id)
			}
			const data = formData.value as unknown as ItemLibraryVO
			if (formType.value === 'create') {
				await ItemLibraryApi.createItemLibrary(data)
				message.success(t('common.createSuccess'))
			} else {
				await ItemLibraryApi.updateItemLibrary(data)
				message.success(t('common.updateSuccess'))
			}
			dialogVisible.value = false
			// 发送操作成功的事件
			emit('success')
		} finally {
			formLoading.value = false
		}
	}

	/** 重置表单 */
	const resetForm = () => {
		formData.value = {
			matterName: undefined,
			mattersId: undefined,
			matterType: 1,
			riskLevelId: undefined,
			mattersDesc: undefined,
			controlMeasures: undefined,
			stepMethod: undefined,
			mainQues: undefined,
			auditSugg: undefined,
			lawsAndRegulations: [] as matterVo[],
			internalRegulations: [],
			auditData: [],
			auditModel: [],
		}
		formRef.value?.resetFields()
	}
</script>
<style scoped>
/* 富文本输入框宽度 */
::v-deep .el-form-item__content {
	display: block ;
}
</style>
