<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-10-26 14:01:07
 * @Description: 选择审计事项
-->
<template>
    <Dialog :title="dialogTitle" v-model="dialogVisible" width="30%" :scroll="true">
		<JobmangerLeftTree ref="JobmangerLeftTreeRef" @node-click="handleDeptNodeClick" :searchUrl="searchUrl" :showEdit='false' :checkbox="true" @checkedGet="checkedGet" />
		
		<template #footer>
			<el-button :disabled="multiple" type="primary" @click="submitForm">确 定</el-button>
			<el-button @click="dialogVisible = false">取 消</el-button>
		</template>
	</Dialog>
</template>

<script setup lang="ts">
	import { getIntDictOptions } from '@/utils/dict'
	import download from '@/utils/download'
	import { ItemLibraryApi, ItemLibraryVO, ItemLibraryDetailVO } from '@/api/jobmanger/itemLibrary'
	import JobmangerLeftTree from '@/views/jobmanger/itemLibrary/components/JobmangerLeftTree.vue'
	defineOptions({ name: 'AuditMatters' })

	const message = useMessage() // 消息弹窗
	const { t } = useI18n() // 国际化
    const dialogVisible = ref(false) // 弹窗的是否展示
    const dialogTitle = ref('') // 弹窗的标题

	const loading = ref(true) // 列表的加载中
	const list = ref<ItemLibraryVO[]>([]) // 列表的数据
	const queryParams = reactive({
		id: undefined,
		pageSize: 10,
		pageNo: 1,
        matterType: 0,
        matterTypeId: '',
        status: 1,
		matterName: undefined,
		matterCode: undefined,
		mattersDesc: undefined,
		controlMeasures: undefined,
		stepMethod: undefined,
		auditForm: undefined,
		mainQues: undefined,
		auditSugg: undefined,
		auditAccord: undefined,
	})
	const queryFormRef = ref() // 搜索的表单
	const exportLoading = ref(false) // 导出的加载中
	const total = ref(0)
	// 事项树接口
	const JobmangerLeftTreeRef = ref()
	var searchUrl = ref('/audit/tank-trees-type/get?type=4')
	const activeName = ref('1')
	const handleClick = async (type) => {
		console.log(type);
		activeName.value = type
		if (type == '1') {
			// await (searchUrl.value = '/audit/tank-matters-info/get-matterslist')
			await (searchUrl.value = '/audit/tank-trees-type/get?type=4')
			JobmangerLeftTreeRef.value.getTree()
		} else {
			await (searchUrl.value = '/system/audit-type/getAll')
			JobmangerLeftTreeRef.value.getTree()
		}
		queryParams.matterId = 0
		queryParams.matterType = 0
		queryParams.matterTypeId = 0
		await getList()
	}

	/** 查询列表 */
	const getList = async () => {
		loading.value = true
		try {
			let data = {}
			data = await ItemLibraryApi.getItemLibraryTypeList(queryParams)
			list.value = data.list
			total.value = data.total
		} finally {
			loading.value = false
		}
	}

	const detailRef = ref()
	const openDetailForm = (data : ItemLibraryDetailVO) => {
		detailRef.value.open(activeName.value, data)
	}

	/** 搜索按钮操作 */
	const handleQuery = () => {
		queryParams.pageNo = 1
		getList()
	}

	/** 重置按钮操作 */
	const resetQuery = () => {
		queryFormRef.value.resetFields()
		handleQuery()
	}

	const DispositionRef = ref()
	const addForm = () => {
		DispositionRef.value.open()
	}

    /** 打开弹窗 */
    // const matterTypeId = ref('')
    const open = async (auditType: string) => {
        dialogVisible.value = true
        queryParams.matterTypeId = auditType
        dialogTitle.value = t('请选择')
        getList()
    }
    defineExpose({ open }) // 提供 open 方法，用于打开弹窗

    // 项目资料清单多选
	const multipleTableRef = ref<TableInstance>()
	const multipleSelection = ref([])
	const ids = ref([])
	const multiple = ref(true)
	const handleSelectionChange = (val : configuraList[]) => {
		multipleSelection.value = val
		ids.value = val.map((item) => item.id)
		multiple.value = !val.length
	}

	// 获取选择数据
	const checkedGet = (data) => {
		multipleSelection.value = data
		ids.value = data.map((item) => item.id)
		multiple.value = !data.length
	}

	// 确定按钮
    const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
    const submitForm = async () => {
		multiple.value = true
		try {
			message.success(t('操作成功！'))
			emit('success', multipleSelection.value)
			// resetForm()
			dialogVisible.value = false
		} finally {
			multiple.value = false
		}
	}

	/** 初始化 **/
	onMounted(() => {
		getList()
	})
</script>

