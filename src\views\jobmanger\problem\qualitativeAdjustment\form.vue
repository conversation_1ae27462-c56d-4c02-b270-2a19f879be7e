<!--
* @Author: lijunliang
* @Date: 2024-09-11 16:31:32
* @Description: 问题定性库表单=>
-->
<template>
	<Dialog :title="dialogTitle" :scroll="true" v-model="dialogVisible" width="70%">
		<el-form ref="formRef" :model="formData" :rules="formRules" class="common-submit-form" v-loading="formLoading">
			<el-row>
				<!-- <el-col :span="8"> -->
					<el-form-item label="问题定性" prop="quesName">
						<el-input v-model="formData.quesName" placeholder="请输入问题定性" class="!w-240px" />
					</el-form-item>
				<!-- </el-col> -->
					<el-form-item label="审计事项" prop="auditMattersId">
						<el-tree-select v-model="formData.auditMattersId" :data="parentListTree" :props="defaultProps" filterable
							check-strictly default-expand-all placeholder="请选择审计事项" class="!w-240px">
							<template #default="{ node }">
								<span class="custom-tree-node">
									<Icon icon="f7:doc" :size="12" />
									{{ node.label }}
								</span>
							</template>
						</el-tree-select>
					</el-form-item>
				<!-- <el-col :span="8"> -->
					<el-form-item label="定性分类" prop="typeId">
						<el-tree-select v-model="formData.typeId" :data="problemListTree" :props="defaultProps" filterable
							check-strictly default-expand-all placeholder="请选择定性分类" class="!w-240px">
							<template #default="{ node }">
								<span class="custom-tree-node">
									<Icon icon="f7:doc" :size="12" />
									{{ node.label }}
								</span>
							</template>
						</el-tree-select>
					</el-form-item>
				<!-- </el-col> -->
				<!-- <el-col :span="8">
					<el-form-item label="创建人员" prop="auditRoleName">
						<el-input v-model="formData.auditRoleName" placeholder="自动带出" readonly class="!w-240px" />
					</el-form-item>
				</el-col> -->
			</el-row>
			<!-- <el-form-item label="问题定性" prop="quesQualitation">
				<el-input v-model="formData.quesQualitation" type="textarea" :autosize="{minRows: 3,maxRows: 3}" placeholder="请输入" />
			</el-form-item> -->
			<el-form-item label="定性描述" prop="quesDesc">
				<el-input v-model="formData.quesDesc" type="textarea" :autosize="{minRows: 3,maxRows: 3}" placeholder="请输入" />
			</el-form-item>
			<el-form-item label="审计建议" prop="auditSugg">
				<el-input v-model="formData.auditSugg" type="textarea" :autosize="{minRows: 3,maxRows: 3}" placeholder="请输入" />
			</el-form-item>
			<el-form-item label="备注" prop="remark">
				<el-input v-model="formData.remark" type="textarea" :autosize="{minRows: 3,maxRows: 3}" placeholder="请输入" />
			</el-form-item>
			<el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
				<el-tab-pane label="外部法律法规" name="0">
					<div>
						<el-button type="primary" plain @click="openList(0)">
							<Icon icon="ep:plus" class="mr-5px" />新增
						</el-button>
					</div>
					<el-table :data="formData.lawsAndRegulations" border :stripe="true" :show-overflow-tooltip="true">
						<el-table-column type="index" label="序号" width="60" align="center"/>
						<el-table-column label="文号" align="left" prop="docNum" min-width="120" />
						<el-table-column label="发布主体" align="center" prop="publishMain">
							<template #default="scope">
								<dict-tag :type="DICT_TYPE.EXTERNAL_LAWS_REGULATIONS_SUBJECT"
									:value="scope.row.publishMain" />
							</template>
						</el-table-column>
						<el-table-column label="法律法规名称" align="left" prop="lawName" min-width="180"/>
						<el-table-column label="生效日期" align="center" prop="effeDate">
							<template #default="scope">
								<span>{{ formatTime(scope.row.effeDate, 'yyyy-MM-dd') }}</span>
							</template>
						</el-table-column>
						<!-- <el-table-column label="规定应用描述" align="center" prop="name" /> -->
						<el-table-column label="法规状态" align="center" prop="lawStatus">
							<template #default="scope">
								<dict-tag :type="DICT_TYPE.EXTERNAL_LAWS_REGULATIONS_STATE"
									:value="scope.row.lawStatus" />
							</template>
						</el-table-column>
						<el-table-column label="操作" align="center" width="150">
							<template #default="scope">
								<el-button link type="danger" @click="handleDelete('lawsAndRegulations',scope.$index)">删除</el-button>
								<!-- <el-button link type="primary" @click="handleView(scope.row.fileId)">预览附件</el-button> -->
								<el-button link type="primary" @click="handleView(scope.row.id)">查看</el-button>
							</template>
						</el-table-column>
					</el-table>
				</el-tab-pane>
				<el-tab-pane label="内部规章制度" name="1">
					<div>
						<el-button type="primary" plain @click="openList(1)">
							<Icon icon="ep:plus" class="mr-5px" />新增
						</el-button>
					</div>
					<el-table :data="formData.internalRegulations" border :stripe="true" :show-overflow-tooltip="true">
						<el-table-column type="index" label="序号" width="60" align="center"/>
						<el-table-column label="标准编号" align="left" prop="standarNum" min-width="180" />
						<el-table-column label="标准名称" align="left" prop="standarName" min-width="180"/>
						<el-table-column label="制定/修正" align="center" prop="amend" min-width="100">
							<template #default="scope">
								<dict-tag type="internalre_vision" :value="scope.row.amend" />
							</template>
						</el-table-column>
						<!-- <el-table-column label="规定应用描述" align="center" prop="name" /> -->
						<el-table-column label="标准状态" align="center" prop="status" min-width="100">
							<template #default="scope">
								<dict-tag type="Internalst_andard_state" :value="scope.row.status" />
							</template>
						</el-table-column>
						<el-table-column label="归口部门" align="left" prop="deptName" min-width="180"/>
						<el-table-column label="操作" align="center" width="150">
							<template #default="scope">
								<el-button link type="danger" @click="handleDelete('internalRegulations',scope.$index)">删除</el-button>
								<!-- <el-button link type="primary" @click="handleView(scope.row.fileId)">预览附件</el-button> -->
								<el-button link type="primary" @click="GethandleView(scope.row.id)">查看</el-button>
							</template>
						</el-table-column>
					</el-table>
				</el-tab-pane>
				<el-tab-pane v-if="false" label="审计模型" name="2">
					<div>
						<el-button type="primary" plain @click="openList(2)">
							<Icon icon="ep:plus" class="mr-5px" />新增
						</el-button>
					</div>
					<el-table :data="formData.auditModel" border :stripe="true" :show-overflow-tooltip="true">
						<el-table-column type="index" label="序号" width="60" />
						<el-table-column label="模型名称" align="center" prop="name" min-width="120" />
						<el-table-column label="模型编码" align="center" prop="name" />
						<el-table-column label="模型描述" align="center" prop="name" />
						<el-table-column label="模型状态" align="center" prop="name" />
						<el-table-column label="操作" align="center" width="150">
							<template #default="scope">
								<el-button link type="danger" @click="handleDelete('auditModel',scope.$indexs)">删除</el-button>
								<el-button link type="primary" @click="handleMxView(scope.row)">查看模型</el-button>
							</template>
						</el-table-column>
					</el-table>
				</el-tab-pane>
			</el-tabs>
		</el-form>
		<template #footer>
			<el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
			<el-button @click="dialogVisible = false">取 消</el-button>
		</template>

	</Dialog>
	<!-- 预览附件 -->
	<DialogFlie ref="DialogFlieRef" />
	<CreateLibraryModal ref='createLibraryModal' @success_receive="receivedData" />
	<!-- 外部法律法规-查看 -->
	<ItemDetail ref="ItemDetailRef" />
	<!-- 内部规章制度-查看 -->
	<DetailInterior ref="DetailRef" />
</template>
<script setup lang="ts">
	import { getIntDictOptions, getStrDictOptions, DICT_TYPE } from '@/utils/dict'
	import { QualitativeAnalysisApi, QualitativeAnalysisVO } from '@/api/jobmanger/problem/qualitativeAnalysis'
	import { QualitativeAdjustmentApi } from '@/api/jobmanger/problem/qualitativeAdjustment'
	import * as JobmangerLeftTreeApi from '@/api/jobmanger/jobmangerLeftTree'
	import CreateLibraryModal from '@/views/jobmanger/itemLibrary/CreateLibraryModal.vue'
	import { defaultProps, handleTree } from '@/utils/tree'
	import { formatTime } from '@/utils'
	import { CACHE_KEY, useCache } from '@/hooks/web/useCache'
	// import FileForm from '@/views/infra/file/FileForm.vue'
	import DetailInterior from '@/views/jobmanger/regulations/innerregulations/Detail.vue'
	import ItemDetail from '@/views/jobmanger/regulations/outregulations/Detail.vue'

	/** 审计角色 表单 */
	defineOptions({ name: 'QualitativeAnalysisForm' })

	const { t } = useI18n() // 国际化
	const message = useMessage() // 消息弹窗
	const { wsCache } = useCache()
	const dialogVisible = ref(false) // 弹窗的是否展示
	const dialogTitle = ref('') // 弹窗的标题
	const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
	const formType = ref('') // 表单的类型：create - 新增；update - 修改
	const formData = ref({
		id: undefined,
		quesName: undefined,
		typeId: undefined,
		quesDesc: undefined,
		auditSugg: undefined,
		remark: undefined,
		creator: undefined,
		status: 1,
		lawsAndRegulations: [],
		internalRegulations: [],
		auditModel: [],
		auditMattersId: undefined,
	})
	const formRules = reactive({
		quesName: [{ required: true, message: '问题定性不能为空', trigger: 'blur' }],
		quesDesc: [{ required: true, message: '定性描述不能为空', trigger: 'blur' }],
		auditSugg: [{ required: true, message: '审计建议不能为空', trigger: 'blur' }],
		typeId:[{ required: true, message: '定性分类不能为空', trigger: 'change' }],
		auditMattersId: [{ required: true, message: '审计事项不能为空', trigger: 'change' }],
	})
	const formRef = ref() // 表单 Ref

	const parentListTree = ref() // 审计事项树形结构
	const problemListTree = ref()//问题分类树形结构
	const activeName = ref('0')
	const handleClick = () => {

	}
	const handleDelete = async (type : string, index : number = 0) => {
		await message.delConfirm()
		formData.value[type].splice(index, 1)
		await message.success(t('common.delSuccess'))
	}

	/** 打开弹窗 */
	const quesId = ref()
	const open = async (type : string, row: any) => {
		dialogVisible.value = true
		dialogTitle.value = t('action.' + type)
		formType.value = type
		resetForm()
		parentListTree.value = []
		problemListTree.value = []
		const data1 = await JobmangerLeftTreeApi.getJobLeftTreeList('/audit/tank-trees-type/get?type=4')
		parentListTree.value = handleTree(data1, 'id', 'parentId')
		const data2 = await JobmangerLeftTreeApi.getJobLeftTreeList('/audit/tank-trees-type/get?type=5')
		problemListTree.value = handleTree(data2, 'id', 'parentId')

		quesId.value = row.id
		formData.value.quesName = row.questionName
		// formData.value.quesName = row.questionQualitative
		formData.value.quesDesc = row.quesDigest
		formData.value.auditSugg = row.auditSugg
		formData.value.auditMattersId = row.auditMatterId
	}
	defineExpose({ open }) // 提供 open 方法，用于打开弹窗

	// 外部管理制度-查看
	const ItemDetailRef = ref()
	const handleView = async (id : number) => {
		await ItemDetailRef.value.open(id)
	}

	// 内部管理制度-查看
	const DetailRef = ref()
	const GethandleView = async (id : number) => {
		await DetailRef.value.open(id)
	}

	const handleMxView = () => { }

	const createLibraryModal = ref()
	const openList = (typeId : number) => {
		createLibraryModal.value.open(typeId)
	}
	const receivedData = (type : number, data) => {
		if (type === 0) {
			formData.value.lawsAndRegulations = formData.value.lawsAndRegulations??[]
			formData.value.lawsAndRegulations = data.concat(formData.value.lawsAndRegulations)
		} else if (type === 1) {
			formData.value.internalRegulations = formData.value.internalRegulations??[]
			formData.value.internalRegulations = data.concat(formData.value.internalRegulations)
		} else if (type === 2) {
			formData.value.auditData = formData.value.auditData??[]
			formData.value.auditData = data.concat(formData.value.auditData)
		} else if (type === 3) {
			formData.value.auditModel = formData.value.auditModel??[]
			formData.value.auditModel = data.concat(formData.value.auditModel)
		}
	}

	/** 提交表单 */
	const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
	const submitForm = async () => {
		// 校验表单
		await formRef.value.validate()
		// 提交请求
		formLoading.value = true
		try {
			if (formData.value.lawsAndRegulations) {
				formData.value.lawsAndRegulations = formData.value.lawsAndRegulations.map((item) => item.id)
			}
			if (formData.value.internalRegulations) {
				formData.value.internalRegulations = formData.value.internalRegulations.map((item) => item.id)
			}
			const data = formData.value as unknown as QualitativeAnalysisVO
			console.log(data.quesName)
			if (formType.value === 'create') {
				// 新增问题定性
				const questData =  await QualitativeAnalysisApi.createQualitativeAnalysis(data)
				if(questData){
					const pardata = {
						questionQualitative: data.quesName,
						id: quesId.value,
						questionStatus: 3,
					}
					// 修改问题定性归纳状态
					await QualitativeAdjustmentApi.updateQualitative(pardata)
					message.success(t('common.createSuccess'))
				}
				
			} else {
				await QualitativeAnalysisApi.updateQualitativeAnalysis(data)
				message.success(t('common.updateSuccess'))
			}
			dialogVisible.value = false
			// 发送操作成功的事件
			emit('success')
		} finally {
			formLoading.value = false
		}
	}

	/** 重置表单 */
	const resetForm = () => {
		formData.value = {
			id: undefined,
			quesName: undefined,
			typeId: undefined,
			quesDesc: undefined,
			auditSugg: undefined,
			remark: undefined,
			status: 1,
			creator: wsCache.get(CACHE_KEY.USER).user.id,
			lawsAndRegulations: [],
			internalRegulations: [],
			auditModel: [],
		}
		formRef.value?.resetFields()
	}
</script>
