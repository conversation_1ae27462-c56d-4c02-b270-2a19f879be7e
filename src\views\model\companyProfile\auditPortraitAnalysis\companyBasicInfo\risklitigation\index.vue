<!--
* @Author: lijunliang
* @Date: 2024-10-24 17:10:50
* @Description: 诉讼风险=>
-->
<template>
	<ContentWrap class="common-card-search">
		<!-- 搜索工作栏 -->
		<el-form class="-mb-15px common-search-form" :model="queryParams" ref="queryFormRef" :inline="true" label-width="60px">
			<el-form-item label="单位" prop="entName">
				<el-tree-select v-model="queryParams.entName" ref="treeRef" placeholder="请选择所属单位"
					:data="deptList" check-strictly :expand-on-click-node="false" :check-on-click-node="true"
					:default-expand-all="false" highlight-current node-key="name"
					:load="loadNode" @change="getList" :default-expanded-keys="defaultExpandedKeys"
					:filter-node-method="filterNode" lazy class="!w-200px">
					<template #default="{ data: { name } }">{{ name }}</template>
				</el-tree-select>
			</el-form-item>
			<el-form-item label="年份" prop="auditYear">
				<el-date-picker v-model="queryParams.auditYear" type="year" value-format="YYYY" class="!w-200px"
					placeholder="请选择年份" />
			</el-form-item>
		</el-form>
		<div class="right-search-btn">
			<el-button  type="primary"  @click="handleQuery">
					<Icon icon="ep:search" class="mr-5px" />搜索
				</el-button>
				<el-button @click="resetQuery">
					<Icon icon="ep:refresh" class="mr-5px" />重置
				</el-button>
		</div>
	</ContentWrap>

	<!-- 列表 -->
	<ContentWrap>
		<el-tabs v-model="queryParams.code" class="demo-tabs" @tab-change="handleClick">
			<el-tab-pane label="诉讼信息" name="zhimeng10" />
			<el-tab-pane label="行政处罚" name="zhimeng20" />
			<el-tab-pane label="动产抵押" name="zhimeng26" />
		</el-tabs>
		<!-- 诉讼信息 -->
		<el-table border v-show="queryParams.code == 'zhimeng10'" v-loading="loading" :data="list" :stripe="true"
			:show-overflow-tooltip="true">
			<el-table-column label="序号" width="60" align="center">
				<template #default="{ $index }">{{ (queryParams.pageNum - 1) * queryParams.pageSize + $index + 1
					}}</template>
			</el-table-column>
			<el-table-column label="案件名称" align="left" prop="litigant" min-width="180" />
			<el-table-column label="案号" align="left" prop="caseNo" min-width="180"/>
			<el-table-column label="案由" align="center" prop="caseReason" min-width="150"/>
			<el-table-column label="立案时间" align="center" prop="startDate" min-width="120">
				<template #default="scope">
					<span>{{ formatTime(scope.row.startDate, 'yyyy-MM-dd') }}</span>
				</template>
			</el-table-column>
			<el-table-column label="法院" align="left" prop="court" min-width="180"/>
			<el-table-column label="当事人" align="left" prop="litigantName" min-width="180"/>
			<el-table-column label="案件状态" align="center" prop="caseStatus" />
		</el-table>
		<!-- 行政处罚 -->
		<el-table border v-show="queryParams.code == 'zhimeng20'" v-loading="loading" :data="list" :stripe="true"
			:show-overflow-tooltip="true">
			<el-table-column label="序号" width="60" align="center">
				<template #default="{ $index }">{{ (queryParams.pageNum - 1) * queryParams.pageSize + $index + 1
					}}</template>
			</el-table-column>
			<el-table-column label="处罚日期" align="center" prop="punishDate" min-width="120"/>
			<el-table-column label="文号" align="left" prop="docNo" min-width="180"/>
			<el-table-column label="处罚事由" align="left" prop="punishContent" min-width="180"/>
			<el-table-column label="处罚结果" align="left" prop="punishBase" min-width="180"/>
			<el-table-column label="处罚单位" align="left" prop="punishOrg" min-width="180"/>
		</el-table>
		<!-- 动产抵押 -->
		<el-table border v-show="queryParams.code == 'zhimeng26'" v-loading="loading" :data="list" :stripe="true"
			:show-overflow-tooltip="true">
			<el-table-column label="序号" width="60" align="center">
				<template #default="{ $index }">{{ (queryParams.pageNum - 1) * queryParams.pageSize + $index + 1
					}}</template>
			</el-table-column>
			<el-table-column label="登记编号" align="center" prop="mortNo" />
			<el-table-column label="登记日期" align="center" prop="mortDate" />
			<el-table-column label="被担保债权种类" align="center" prop="debtType" />
			<el-table-column label="被担保债权数额" align="center" prop="debtAmount" />
			<el-table-column label="债务人履行债务的期限" align="center" prop="debtTerm" />
		</el-table>
		<!-- 分页 -->
		<Pagination :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize"
			@pagination="getList" />
	</ContentWrap>
</template>

<script setup lang="ts">
import { CompanyBasicInfoApi, RisklitigationVO } from '@/api/model/companyProfile/auditPortraitAnalysis/companyBasicInfo'
import { handleLazyTree } from '@/utils/tree'
import * as DeptApi from '@/api/system/dept'
import { formatTime } from '@/utils'
defineOptions({ name: 'Risklitigation' })

const loading = ref(true) // 列表的加载中
const list = ref<RisklitigationVO[]>([]) // 列表的数据
const queryParams = reactive({
	entName: "中国工商银行股份有限公司",
	creditNo: "",
	pageNum: 1,
	pageSize: 10,
	code: "zhimeng10",
	auditYear: '',
})
const queryFormRef = ref() // 搜索的表单
const total = ref(0)
const handleClick = async (type) => {
	console.log(type);
	queryParams.code = type
	await getList()
}
/** 查询列表 */
const getList = async () => {
	loading.value = true
	try {
		const data = await CompanyBasicInfoApi.requestCompanyBasicInfo(queryParams)
		list.value = data.rows
		total.value = data.total
	} finally {
		loading.value = false
	}
}
// 获取单位
interface DeptNode {
	id: number
	masterOrgId?: number | string
	name: string
	parentId: number | string
	children?: DeptNode[]
	isLeaf?: boolean
}
const deptList = ref<DeptNode[]>([]) // 树形结构
const defaultExpandedKeys = ref<(string | number)[]>([])
const getTree = async (id) => {
	deptList.value = []
	const res = await DeptApi.getSimpleDeptList(id)
	deptList.value.push(...handleLazyTree(res, 'id', 'parentId'))
	defaultExpandedKeys.value = deptList.value.map((node) => node.id)
}
const filterNode = (name: string, data: DeptNode) => {
	if (!name) return true
	return data.name.includes(name)
}
const loadNode = async (node: Node, resolve: (data: any[]) => void) => {
	try {
		const nodeId = node.data.id
		if (nodeId == undefined || nodeId == null) {
			return
		}
		const res = await DeptApi.getSimpleDeptList(nodeId)
		const children = handleLazyTree(res, 'id', 'parentId', 'children')
		resolve(children)
	} catch (error) {
		resolve([])
	}
}
/** 搜索按钮操作 */
const handleQuery = () => {
	queryParams.pageNo = 1
	getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
	queryFormRef.value.resetFields()
	handleQuery()
}

/** 初始化 **/
onMounted(() => {
	getList()
	getTree(0)
})
</script>
