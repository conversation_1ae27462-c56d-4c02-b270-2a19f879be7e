<!--
* @Author: li<PERSON>liang
* @Date: 2024-10-24 17:10:50
* @Description: 内审外查=>
-->
<template>
	<ContentWrap class="common-card-search">
		<!-- 搜索工作栏 -->
		<el-form class="-mb-15px common-search-form" :model="queryParams" ref="queryFormRef" :inline="true" label-width="60px">
			<el-form-item label="单位" prop="deptId">
				<el-tree-select v-model="queryParams.deptId" ref="treeRef" clearable placeholder="请选择所属单位"
					:data="deptList" check-strictly :expand-on-click-node="false" :check-on-click-node="true"
					:default-expand-all="false" highlight-current node-key="id"
					:load="loadNode" @change="getList" :default-expanded-keys="defaultExpandedKeys"
					:filter-node-method="filterNode" lazy class="!w-200px">
					<template #default="{ data: { name } }">{{ name }}</template>
				</el-tree-select>
			</el-form-item>
			<el-form-item label="年份" prop="auditYear">
				<el-date-picker v-model="queryParams.auditYear" type="year" value-format="YYYY" class="!w-200px"
					placeholder="请选择年份" />
			</el-form-item>
		</el-form>
		<div class="right-search-btn">
			<el-button  type="primary"  @click="handleQuery">
					<Icon icon="ep:search" class="mr-5px" />搜索
				</el-button>
				<el-button @click="resetQuery">
					<Icon icon="ep:refresh" class="mr-5px" />重置
				</el-button>
		</div>
	</ContentWrap>

	<!-- 列表 -->
	<ContentWrap>
		<el-tabs v-model="activeName" class="demo-tabs" @tab-change="handleClick">
			<el-tab-pane label="项目台账" name="1" />
			<el-tab-pane label="问题台账" name="2" />
			<el-tab-pane label="整改台账" name="3" />
		</el-tabs>
		<!-- 项目台账 -->
		<el-table border v-show="activeName == '1'" v-loading="loading" :data="list" :stripe="true"
			:show-overflow-tooltip="true">
			<el-table-column label="序号" width="60" align="center">
				<template #default="{ $index }">{{ (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1
					}}</template>
			</el-table-column>
			<el-table-column label="项目编号1" align="left" prop="projectNo" min-width="180" />
			<el-table-column label="项目名称" align="left" prop="projectName" min-width="260"/>
			<el-table-column label="审计类型" align="center" prop="auditTypeDesc" min-width="120"/>
			<el-table-column label="实施单位" align="left" prop="implementDeptName" min-width="180"/>
			<el-table-column label="时间计划" align="center" prop="timeSchedule" min-width="120"/>
			<el-table-column label="项目进度" align="center" min-width="160">
                <template #default="{row}">
                  <el-progress  :percentage="row?.processPercent ?? 0" />
                </template>
              </el-table-column>
		</el-table>
		<!-- 问题台账 -->
		<el-table border v-show="activeName == '2'" v-loading="loading" :data="list" :stripe="true"
			:show-overflow-tooltip="true">
			<el-table-column label="序号" width="60" align="center">
				<template #default="{ $index }">{{ (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1
					}}</template>
			</el-table-column>
			<el-table-column label="审计年度" align="center" prop="auditYear" min-width="100"/>
			<el-table-column label="问题编号" align="left" prop="questionCode" min-width="180"/>
			<el-table-column label="问题描述" align="left" prop="questionName" min-width="180"/>
			<el-table-column label="问题类型" align="center" prop="quesTypeName" />
			<el-table-column label="问题等级" align="center" prop="questionTitle" />
			<el-table-column label="项目编号" align="left" prop="projectNo" min-width="180"/>
			<el-table-column label="项目名称" align="left" prop="projectName" min-width="260"/>
			<el-table-column label="整改状态" align="center" prop="correctionStatus" min-width="100" />
		</el-table>
		<!-- 整改台账 -->
		<el-table border v-show="activeName == '3'" v-loading="loading" :data="list" :stripe="true"
			:show-overflow-tooltip="true">
			<el-table-column label="序号" width="60" align="center">
				<template #default="{ $index }">{{ (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1
					}}</template>
			</el-table-column>
			<el-table-column label="审计年度" align="center" prop="projectYear" min-width="100"/>
			<el-table-column label="问题编号" align="left" prop="questionCode" min-width="180"/>
			<el-table-column label="问题描述" align="left" prop="questionName" min-width="180"/>
			<el-table-column label="问题类型" align="center" prop="quesTypeName" />
			<el-table-column label="整改措施" align="left" prop="rectificStep" min-width="180"/>
			<el-table-column label="整改数据统计" align="center" prop="quesRectificTypeDesc" min-width="120"/>
			<el-table-column label="整改状态" align="center" prop="checkDesc" min-width="100"/>
			<el-table-column label="销号状态" align="center" prop="destructStatus" min-width="100"/>
		</el-table>
		<!-- 分页 -->
		<Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
			@pagination="getList" />
	</ContentWrap>
</template>

<script setup lang="ts">
import { RegulationsVO } from '@/api/jobmanger/regulations/outregulations'
import { ProjectInitiationApi } from '@/api/auditManagement/projectAssignment/PreTrialPreparation/projectInitiation'
import { ProblemStandingBookApi } from '@/api/auditManagement/statisticAnalysis/problemStandingBook'
import { reformingStandingBookApi } from '@/api/auditManagement/auditRectifica/reformingStandingBook'
import { handleLazyTree } from '@/utils/tree'
import * as DeptApi from '@/api/system/dept'
import { formatTime } from '@/utils'
defineOptions({ name: 'InternalAuditExternalInvestigation' })

const loading = ref(true) // 列表的加载中
const list = ref<RegulationsVO[]>([]) // 列表的数据
const list1 = ref<RegulationsVO[]>([]) // 列表的数据
const list2 = ref<RegulationsVO[]>([]) // 列表的数据
const queryParams = reactive({
	pageNo: 1,
	pageSize: 10,
	deptId: undefined,
	auditYear: undefined,
})
const queryFormRef = ref() // 搜索的表单
const total = ref(0)
const activeName = ref('1')
const handleClick = async (type) => {
	console.log(type);
	activeName.value = type
	getList()
}
/** 查询列表 */
const getList = async () => {
	loading.value = true
	try {
		let data = {
			list: [],
			total: 0
		}
		if(activeName.value == '1'){
			data = await ProjectInitiationApi.getProjPlanProjectList(queryParams)
		}else if(activeName.value == '2'){
			data = await ProblemStandingBookApi.getProblemList(queryParams)
		}else{
			data = await reformingStandingBookApi.getRectificationLedgerList(queryParams)
		}
		list.value = []
		list.value = data.list
		total.value = data.total
	} finally {
		loading.value = false
	}
}
const getProgress = (row: any) => {
  if(row.programCount === 0 || row.allProgramCount===0 || isNaN(row.programCount) || isNaN(row.allProgramCount)) {
    return 0
  }else{
    return ((row.programCount/row.allProgramCount) * 100 ).toFixed(2)
  }
}
// 获取单位
interface DeptNode {
	id: number
	masterOrgId?: number | string
	name: string
	parentId: number | string
	children?: DeptNode[]
	isLeaf?: boolean
}
const deptList = ref<DeptNode[]>([]) // 树形结构
const defaultExpandedKeys = ref<(string | number)[]>([])
const getTree = async (id) => {
	deptList.value = []
	const res = await DeptApi.getSimpleDeptList(id)
	deptList.value.push(...handleLazyTree(res, 'id', 'parentId'))
	defaultExpandedKeys.value = deptList.value.map((node) => node.id)
}
const handleNodeClick = (node) => {
	// 如果是父节点，选中它
	queryParams.deptId = node.id
	getList()
}
const filterNode = (name: string, data: DeptNode) => {
	if (!name) return true
	return data.name.includes(name)
}
const loadNode = async (node: Node, resolve: (data: any[]) => void) => {
	try {
		const nodeId = node.data.id
		if (nodeId == undefined || nodeId == null) {
			return
		}
		const res = await DeptApi.getSimpleDeptList(nodeId)
		const children = handleLazyTree(res, 'id', 'parentId', 'children')
		resolve(children)
	} catch (error) {
		resolve([])
	}
}
/** 搜索按钮操作 */
const handleQuery = () => {
	queryParams.pageNo = 1
	getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
	queryFormRef.value.resetFields()
	handleQuery()
}

/** 初始化 **/
onMounted(() => {
	getList()
	getTree(0)
})
</script>
