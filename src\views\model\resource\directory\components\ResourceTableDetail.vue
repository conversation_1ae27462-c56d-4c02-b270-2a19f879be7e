<template>
  <el-dialog v-model="dialogVisible">
    <el-tabs v-model="activeName" class="demo-tabs" @tab-click="selTab">
      <el-tab-pane label="基本信息" name="first">
        <el-form :model="formData" ref="resourceTableForm" class="common-submit-form" label-width="110px">
          <el-row :gutter="5">
            <el-col :span="12">
              <el-form-item label="中文表名">
                <el-input v-model="formData.tableNameCn" placeholder="中文表名" readonly />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="英文表名">
                <el-input v-model="formData.tableNameEn" placeholder="英文表名" readonly />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="5">
            <el-col :span="12">
              <el-form-item label="归属数据资源">
                <el-input v-model="formData.catalogName" placeholder="归属数据资源" readonly />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="来源系统">
                <el-select v-model="formData.sourceSystem" class="!w-100%" placeholder="请选择来源系统" disabled>
                  <el-option v-for="dict in getStrDictOptions(DICT_TYPE.MODEL_SOURCE_SYSTEM)" :key="dict.value" :value="dict.value" :label="dict.label" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="5">
            <el-col :span="12">
              <el-form-item label="取数方式" prop="extractMethod">
                <el-select v-model="formData.extractMethod" class="!w-100%" placeholder="请选择取数方式" disabled>
                  <el-option v-for="dict in getStrDictOptions(DICT_TYPE.MODEL_DATA_EXTRACT_METHOD)" :key="dict.value" :value="dict.value" :label="dict.label" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="表类型 " prop="tableType">
                <el-select v-model="formData.tableType" class="!w-100%" placeholder="请选择表类型" disabled>
                  <el-option v-for="dict in getStrDictOptions(DICT_TYPE.MODEL_TABLE_TYPE)" :key="dict.value" :value="dict.value" :label="dict.label" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="5">
            <el-col :span="12">
              <el-form-item label="开始日期" prop="startDate">
                <el-date-picker v-model="formData.startDate" type="date" class="!w-100%" format="YYYY-MM-DD" value-format="YYYY-MM-DD" placeholder="请选择开始日期" disabled />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="最新账期 " prop="latestMonthId">
                <el-date-picker v-model="formData.latestMonthId" type="month" class="!w-100%" format="YYYYMM" value-format="YYYYMM" placeholder="请选择最新账期" disabled />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="5">
            <el-col :span="12">
              <el-form-item label="最新数据量" prop="newlyWrittenDataVolume">
                <el-input v-model="formData.newlyWrittenDataVolume" placeholder="请输入最新数据量" readonly />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="累计数据量" prop="totalDataVolume">
                <el-input v-model="formData.totalDataVolume" placeholder="请输入累计数据量" readonly />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="5">
            <el-col :span="12">
              <el-form-item label="对应组织字段">
                <el-input v-model="formData.scopeField" placeholder="对应组织字段" readonly />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="需求负责人">
                <el-input v-model="formData.proposerUserName" placeholder="需求负责人" readonly />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="加工逻辑">
                <el-input
                  v-model="formData.processLogic"
                  type="textarea"
                  :rows="3"
                  placeholder="加工逻辑"
                  readonly
                />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="备注">
                <el-input
                  v-model="formData.remark"
                  type="textarea"
                  :rows="3"
                  placeholder="备注"
                  readonly
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <el-table :data="tableFieldList" ref="resourceTableFieldTable" height="300px" stripe show-overflow-tooltip border>
          <el-table-column label="序号" type="index" width="60" align="center" />
          <el-table-column prop="fieldName" label="字段名" width="160" align="left" />
          <el-table-column prop="fieldTypeName" label="字段类型" width="120" align="center" />
          <el-table-column prop="fieldComment" label="字段注释" min-width="120" align="left" />
          <el-table-column prop="isKeyWord" label="是否关键字" width="100" align="center">
            <template #default="{ row }">
              <dict-tag :type="DICT_TYPE.COMMON_IS_OR_STATUS" :value="row.isKeyWord" />
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="详细信息" name="second">
        <div class="w-100% bg-#fcf5f5" style="border: 1px solid #f4dddf">
          <div :class="bottomChart" ref="bottomChartRef" style="height: 450px; width: 860px; overflow-scrolling: auto"></div>
        </div>
        <el-table :data="tableDataList" height="300px" stripe show-overflow-tooltip style="margin-top: 5px;">
          <el-table-column label="序号" type="index" width="60" align="center" />
          <el-table-column
            :key="item.fieldName"
            :prop="item.fieldName"
            :label="item.fieldComment"
            width="180"
            align="left"
            v-for="item in tableHeader"
          />
        </el-table>
      </el-tab-pane>
    </el-tabs>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import {DICT_TYPE, getStrDictOptions} from "@/utils/dict"
import { ResourceTableDetailAPI } from '@/api/model/resource/ResourceTableDetail'

import * as echarts from 'echarts'
defineOptions({ name: 'ResourceTableDetail' })

const dialogVisible = ref(false)
const activeName = ref('first')
const formData = ref({
  tableNameCn: '',
  tableNameEn: '',
  catalogName: '',
  sourceSystem: '',
  startDate: '',
  latestMonthId: '',
  newlyWrittenDataVolume: '',
  totalDataVolume: '',
  scopeField: '',
  proposerUserName: '',
  processLogic: '',
  remark: ''
})
const tableFieldList = ref([])
const tableRelationTreeData = ref([])
const tableHeader = ref([])
const tableDataList = ref([])
const tableId = ref('')
const tableName = ref('')

const getTableDetail = async () => {
  formData.value = await ResourceTableDetailAPI.resourceTableInfoData(tableId.value)
}
const selTab = (tab) => {
  if (tab.index == 1) {
    getTableRelationTree()
  } else {
    bottomChart = null
  }
}
// 图表
let bottomChart = null
const bottomChartRef = ref()
const getBottomChart = async () => {
  bottomChart = echarts.init(bottomChartRef.value)
  bottomChart.setOption({
    tooltip: {
      trigger: 'item',
      triggerOn: 'mousemove',
      formatter: '{c}'
    },
    series: [
      {
        type: 'tree',
        edgeShape: 'polyline', //链接线是折现还是曲线
        orient: 'RL',
        top: '10%',
        left: '15%',
        bottom: '12%',
        right: '15%',
        data: tableRelationTreeData.value,
        symbolSize: 10,
        symbol: 'triangle', // 三角形作为箭
        initialTreeDepth: 10,
        avoidLabelOverlap: true, //防止标签重叠
        roam: true, //移动+缩放  'scale' 或 'zoom'：只能够缩放。 'move' 或 'pan'：只能够平移。
        scaleLimit: {
          //缩放比例
          min: 0.7, //最小的缩放值
          max: 4 //最大的缩放值
        },
        layout: 'orthogonal', //树图布局，orthogonal水平垂直方向，radial径向布局 是指以根节点为圆心，每一层节点为环，一层层向外
        expandAndCollapse: true,
        animationDuration: 550,
        animationDurationUpdate: 750,
        lineStyle: {
          color: '#f95f67',
          curveness: 0.8,
          width: 3
        }
      }
    ]
  })
  // bottomChart.resize()
}
const getTableFieldData = async () => {
  const params = {
    id: tableId.value,
    tableNameEn: tableName.value
  }
  const res = await ResourceTableDetailAPI.targetTableFieldData(params)
  if (res) {
    tableFieldList.value = res
  }
}

const baseLabel = {
  fontSize: 14,
  color: '#333',
  lineHeight: 21,
  borderWidth: 1,
  padding: 10,
  borderRadius: 5,
  align: 'center',
  overflow: 'breakAll',
  backgroundColor: '#fff',
  borderColor: '#ccc'
}

const lineLabel = {
  fontSize: 14,
  color: '#333',
  lineHeight: 21,
  borderWidth: 1,
  padding: 10,
  borderRadius: 5,
  align: 'center',
  overflow: 'breakAll',
  backgroundColor: '#fff',
  borderColor: '#ccc'
}

const getTableRelationTree = async () => {
  let datas = []
  datas = await ResourceTableDetailAPI.resourceTableRelationTree(tableId.value)
  // 应用样式到整个树
  applyStylesToTree(datas[0],()=>{
    tableRelationTreeData.value = datas
    getBottomChart()
  });


}

const applyStylesToTree = (node, callback)=> {
  // 应用父节点的样式（如果需要）
  if (node.parentId == 0) {
    // node.label = lineLabel; // 确保lineLabel已定义
    if(Array.isArray(node.children)){
      node.symbol = 'triangle';
    node.symbolSize = 20;
    node.symbolRotate = -90;
    node.itemStyle = { color: '#f95f67' };
    node.label = {
        position: 'right',
        distance: 0,
        formatter: (params) => `{label|${params.name}}{percent|}`,
        rich: {
          label: { ...baseLabel },
          percent: { width: 50, padding: [20, 0, 0, 0] }
        }
      };
    }else{
      node.label = lineLabel; 
    }
  
  }

  let allChildrenProcessed = true;

  if (Array.isArray(node.children)) {
    node.symbol = 'triangle';
    node.symbolSize = 20;
    node.symbolRotate = -90;
    node.itemStyle = { color: '#f95f67' };
    node.label = {
        position: 'right',
        distance: 0,
        formatter: (params) => `{label|${params.name}}{percent|}`,
        rich: {
          label: { ...baseLabel },
          percent: { width: 50, padding: [20, 0, 0, 0] }
        }
      };
    node.children.forEach((childNode) => {
    
      childNode.itemStyle = { color: '#f95f67' };
      childNode.label = {
      
        formatter: (params) => `{label|${params.name}}{percent|}`,
        rich: {
          label: { ...baseLabel },
          percent: { width: 50, padding: [20, 0, 0, 0] }
        }
      };

      // 如果有子节点，则递归处理
      if (Array.isArray(childNode.children) && childNode.children.length > 0) {
        allChildrenProcessed = false;
        applyStylesToTree(childNode, () => {
          allChildrenProcessed = true;
          checkIfDone();
        });
      } else {
        checkIfDone();
      }
    });
  }

  function checkIfDone() {
    if (allChildrenProcessed && typeof callback === 'function') {
      callback();
    }
  }

  // 如果没有子节点或所有子节点都处理完了，则立即调用回调
  checkIfDone();
}





const getTableSampleData = async () => {
  const data = await ResourceTableDetailAPI.targetTableSampleData(tableId.value)
  tableHeader.value = data.tableHeader
  tableDataList.value = data.tableData
}

const open = async (data: object) => {
  tableFieldList.value = []
  dialogVisible.value = true
  tableId.value = data.id
  tableName.value = data.tableNameEn
  getTableDetail()
  getTableFieldData()
  getTableRelationTree()
  getTableSampleData()
}

defineExpose({ open })
onMounted(() => {
  window.addEventListener('resize', () => {
    setTimeout(function () {
      bottomChart?.resize()
    }, 300)
  })
})
onUnmounted(() => {
  window.removeEventListener('resize', () => {
    setTimeout(function () {
      bottomChart?.resize()
    }, 300)
  })
})
</script>

<style scoped>
::v-deep(.el-table .el-table__header .el-table__cell) {
  text-align: center;
}
</style>
