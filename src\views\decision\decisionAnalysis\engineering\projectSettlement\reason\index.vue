<!--
* @Author: li<PERSON><PERSON>g
* @Date: 2024-10-22 17:10:50
* @Description: 工程结算-审减原因=>
-->
<template>
	<ContentWrap>
		<!-- 搜索工作栏 -->
		<el-form class="-mb-15px" :model="queryParams" ref="queryFormRef" :inline="true" label-width="100px">
			<el-form-item label="投资单位" prop="investmentUnit">
				<el-input v-model="queryParams.investmentUnit" placeholder="请输入投资单位" clearable @keyup.enter="handleQuery"
					class="!w-200px" />
			</el-form-item>
			<el-form-item label="管理单位" prop="managementUnit">
				<el-input v-model="queryParams.managementUnit" placeholder="请输入管理单位" clearable @keyup.enter="handleQuery"
					class="!w-200px" />
			</el-form-item>
			<el-form-item label="施工单位" prop="constructionUnit">
				<el-input v-model="queryParams.constructionUnit" placeholder="请输入施工单位" clearable @keyup.enter="handleQuery"
					class="!w-200px" />
			</el-form-item>
			<el-form-item label="审计单位" prop="auditUnit">
				<el-input v-model="queryParams.auditUnit" placeholder="请输入审计单位" clearable @keyup.enter="handleQuery"
					class="!w-200px" />
			</el-form-item>
			<el-form-item label="合同名称" prop="contractName">
				<el-input v-model="queryParams.contractName" placeholder="请输入合同名称" clearable @keyup.enter="handleQuery"
					class="!w-200px" />
			</el-form-item>
			<el-form-item label="审计序列" prop="auditSequence">
				<el-select v-model="queryParams.auditSequence" placeholder="请选择审计序列" clearable class="!w-200px">
					<el-option v-for="dict in getIntDictOptions('audit_sequences')" :key="dict.value"
						:label="dict.label" :value="dict.value" />
				</el-select>
			</el-form-item>
			<el-form-item label="日期" prop="publishDate">
				<el-date-picker v-model="queryParams.publishDate" type="daterange" unlink-panels range-separator="-"
					start-placeholder="开始" end-placeholder="结束" value-format="YYYY-MM-DD" clearable @change="changeDate" />
			</el-form-item>
			<el-form-item>
				<el-button type="primary" @click="handleQuery">
					<Icon icon="ep:search" class="mr-5px" />搜索
				</el-button>
				<el-button @click="resetQuery">
					<Icon icon="ep:refresh" class="mr-5px" />重置
				</el-button>
			</el-form-item>
		</el-form>
	</ContentWrap>

	<!-- 列表 -->
	<ContentWrap>
		<el-button :loading="exportLoading" plain @click="handleExport">
			<Icon class="mr-5px" icon="ep:download" />
			导出
		</el-button>
		<el-table border v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
			<el-table-column label="序号" width="60" align="center">
				<template #default="{ $index }">{{ (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1
					}}</template>
			</el-table-column>
			<el-table-column label="结算审计发现问题" align="left" prop="issueDescription" min-width="180" />
			<el-table-column label="出现该问题的工程结算数量" align="center" prop="projectSettlementCount" />
			<el-table-column label="操作" align="center" width="100" fixed="right">
				<template #default="scope">
					<el-button type="primary" link @click="handleDetail(scope.row.id, scope.row.issueDescription)">查看</el-button>
				</template>
			</el-table-column>
		</el-table>
		<!-- 分页 -->
		<Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
			@pagination="getList" />
	</ContentWrap>
	<!-- 单位详情弹窗 -->
	<Detail ref="detailRef" />
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { AuditEfficiencyDetailsVO, ReasonDeductioneApi } from '@/api/decision/engineering/projectSettlement/reason'
import Detail from './Detail.vue'
import download from '@/utils/download'
defineOptions({ name: 'Reason' })
const message = useMessage() // 消息弹窗
const loading = ref(true) // 列表的加载中
const list = ref<AuditEfficiencyDetailsVO[]>([]) // 列表的数据
const queryParams = reactive({
	pageSize: 10,
	pageNo: 1,
	investmentUnit: undefined,
	managementUnit: undefined,
	constructionUnit: undefined,
	auditUnit: undefined,
	contractName: undefined,
	auditSequence: undefined,
	publishDate: undefined,
	startDate: undefined,
	endDate: undefined,
})
const queryFormRef = ref() // 搜索的表单
const total = ref(0)
/** 查询列表 */
const getList = async () => {
	loading.value = true
	try {
		const data = await ReasonDeductioneApi.getReasonDeductioneList(queryParams)
		list.value = data.list
		total.value = data.total
	} finally {
		loading.value = false
	}
}

// 时间选择触发事件
const changeDate = async(data) => {
	queryParams.startDate = data[0]
	queryParams.endDate = data[1]
}

/** 搜索按钮操作 */
const handleQuery = () => {
	queryParams.pageNo = 1
	getList()
}
/** 重置按钮操作 */
const resetQuery = () => {
	queryFormRef.value.resetFields()
	queryParams.startDate = undefined,
	queryParams.endDate = undefined,
	handleQuery()
}
const exportLoading = ref(false) // 导出的加载中
/** 导出按钮操作 */
const handleExport = async () => {
	try {
		// 导出的二次确认
		await message.exportConfirm()
		// 发起导出
		exportLoading.value = true
		const data = await ReasonDeductioneApi.exportReasonDeductione(queryParams)
		download.excel(data, '工程结算-审减原因.xls')
	} catch {
	} finally {
		exportLoading.value = false
	}
}
/** 问题项目 */
const detailRef = ref()
const handleDetail = (id?: number, qusName: string) => {
	detailRef.value.open(id, qusName)
}
/** 初始化 **/
onMounted(() => {
	getList()
})
</script>
