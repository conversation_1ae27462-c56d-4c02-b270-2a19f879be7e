<!--
* @Author: lijunliang
* @Date: 2024-09-11 14:41:58
* @Description: 审计案例库详情=>
-->
<template>
	<Dialog v-model="dialogVisible" :scroll="true" title="详情" width="70%">
		<el-form ref="formRef" :model="detailData" label-width="110px" v-loading="detailLoading">
			<el-row>
				<el-col :span="8">
					<el-form-item label="案例名称：">
						{{ detailData.caseName }}
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item label="填报人：">
						{{ detailData.writeBy }}
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item label="填报单位：">
						{{ detailData.writeDept }}
					</el-form-item>
				</el-col>
				<el-col :span="24">
					<el-form-item label="适用审计事项:">
						{{ detailData.auditScope }}
					</el-form-item>
				</el-col>
				<!-- <el-col :span="8">
					<el-form-item label="关键字：">
						{{ detailData.auditRoleCode }}
					</el-form-item>
				</el-col> -->
			</el-row>
			<el-form-item label="案例概述：">
				<!-- <el-input v-model="detailData.illustrate" type="textarea" placeholder="请输入" readonly
					class="!w-885px" /> -->
				<div v-html="detailData.illustrate" class="!w-885px"></div>
			</el-form-item>
			<el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
				<el-tab-pane label="案例附件" name="0">
					<el-table :data="detailData.fileIds" border :stripe="true" :show-overflow-tooltip="true">
						<el-table-column type="index" label="序号" width="60" align="center"/>
						<el-table-column label="文档名称" align="left" prop="fileName" min-width="120" />
						<el-table-column label="创建人" align="center" prop="creatorName" />
						<el-table-column label="创建时间" align="center" prop="createTime">
							<template #default="scope">
								{{ formatDate(scope.row.createTime) }}
							</template>
						</el-table-column>
						<el-table-column label="操作" align="center" width="150">
							<template #default="scope">
								<el-button link type="primary" @click="listDownload(scope.row?.fileUrl, scope.row?.fileName)">下载</el-button>
							</template>
						</el-table-column>
					</el-table>
				</el-tab-pane>
			</el-tabs>
		</el-form>
	</Dialog>
</template>

<script lang="ts" setup>
	import { DICT_TYPE, NumberDictDataType, getIntDictOptions } from '@/utils/dict'
	import { formatTime } from '@/utils'
	import { AuditRoleDetailVO } from '@/api/basicData/auditRole'
	import { CasebaseApiNew, CasebaseNew } from '@/api/jobmanger/casebase'
	import { formatDate } from '@/utils/formatTime'
	import { downFile } from '@/utils/fileName'
	defineOptions({ name: 'Detail' })

	const formRef = ref() // 表单 Ref
	const dialogVisible = ref(false) // 弹窗的是否展示
	const detailLoading = ref(false) // 表单地加载中
	const message = useMessage() // 消息弹窗
	// const detailData = ref({} as AuditRoleDetailVO) // 详情数据
	const detailData = ref({} as CasebaseNew)  // 详情数据
	/** 打开弹窗 */
	const open = async (id : number) => {
		dialogVisible.value = true
		// 设置数据
		detailLoading.value = true
		try {
			// 获取审计案例库详情
			detailData.value = await CasebaseApiNew.getCasebase(id)
		} finally {
			detailLoading.value = false
		}
	}
	defineExpose({ open }) // 提供 open 方法，用于打开弹窗

	const activeName = ref('0')
	const handleClick = () => {

	}

	/*
	* @Author: lijunliang
	* @Date: 2024-09-12 16:40:36
	* @Description: 下载=>
	*/
	const listDownload = async(url: string, name: string) => {
		// 下载的二次确认
    	await message.confirm('是否确认下载？')
		downFile(url, name)
	 }
</script>
