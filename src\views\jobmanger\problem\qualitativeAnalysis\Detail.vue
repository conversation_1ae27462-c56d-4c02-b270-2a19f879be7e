<!--
* @Author: li<PERSON>liang
* @Date: 2024-09-11 16:38:58
* @Description: 问题定性库详情=>
-->
<template>
	<Dialog v-model="dialogVisible" :scroll="true" title="详情" width="70%">
		<div v-loading="detailLoading">
			<el-descriptions :column="3" border>
				<el-descriptions-item label="问题定性">{{ detailData.quesName }}</el-descriptions-item>
				<el-descriptions-item label="定性分类">{{ detailData.typeName }}</el-descriptions-item>
				<el-descriptions-item label="创建人员">{{ detailData.createrName}}</el-descriptions-item>
				<el-descriptions-item :span="3" label="定性描述">{{ detailData.quesDesc}}</el-descriptions-item>
				<el-descriptions-item :span="3" label="审计建议">{{ detailData.auditSugg}}</el-descriptions-item>
				<el-descriptions-item :span="3" label="备注">{{ detailData.remark}}</el-descriptions-item>
			</el-descriptions>
			<el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
				<el-tab-pane label="外部法律法规" name="0">
					<el-table :data="detailData.lawsAndRegulations" border :stripe="true" :show-overflow-tooltip="true">
						<el-table-column type="index" label="序号" width="60" align="center"/>
						<el-table-column label="文号" align="left" prop="docNum" min-width="120" />
						<el-table-column label="发布主体" align="center" prop="publishMain">
							<template #default="scope">
								<dict-tag :type="DICT_TYPE.EXTERNAL_LAWS_REGULATIONS_SUBJECT"
									:value="scope.row.publishMain" />
							</template>
						</el-table-column>
						<el-table-column label="法律法规名称" align="left" prop="lawName" min-width="180"/>
						<el-table-column label="生效日期" align="center" prop="effeDate">
							<template #default="scope">
								<span>{{ formatTime(scope.row.effeDate, 'yyyy-MM-dd') }}</span>
							</template>
						</el-table-column>
						<el-table-column label="法规状态" align="center" prop="lawStatus">
							<template #default="scope">
								<dict-tag :type="DICT_TYPE.EXTERNAL_LAWS_REGULATIONS_STATE" :value="scope.row.lawStatus" />
							</template>
						</el-table-column>
						<el-table-column label="操作" align="center" width="100">
							<template #default="scope">
								<el-button link type="primary" @click="handleWbView(scope.row.id)">查看</el-button>
							</template>
						</el-table-column>
					</el-table>
				</el-tab-pane>
				<el-tab-pane label="内部规章制度" name="1">
					<el-table :data="detailData.internalRegulations" border  :stripe="true" :show-overflow-tooltip="true">
						<el-table-column type="index" label="序号" width="60" align="center"/>
						<el-table-column label="标准编号" align="left" prop="standarNum" min-width="180" />
						<el-table-column label="标准名称" align="left" prop="standarName" min-width="180"/>
						<el-table-column label="制定/修正" align="center" prop="amend">
							<template #default="scope">
								<dict-tag type="internalre_vision" :value="scope.row.amend" />
							</template>
						</el-table-column>
						<el-table-column label="标准状态" align="center" prop="status">
							<template #default="scope">
								<dict-tag type="Internalst_andard_state" :value="scope.row.status" />
							</template>
						</el-table-column>
						<el-table-column label="归口部门" align="left" prop="deptName" min-width="180"/>
						<el-table-column label="操作" align="center" width="100">
							<template #default="scope">
								<el-button link type="primary" @click="handleView(scope.row.id)">查看</el-button>
							</template>
						</el-table-column>
					</el-table>
				</el-tab-pane>
				<el-tab-pane label="关联项目" name="2">
					<el-table :data="detailData.projectByQuesionId" border :stripe="true" :show-overflow-tooltip="true">
						<el-table-column type="index" label="序号" width="60" align="center"/>
						<el-table-column label="项目名称" align="left" prop="projectName" min-width="240" />
						<el-table-column label="项目编号" align="left" prop="projectNo" min-width="180"/>
						<el-table-column label="项目描述" align="left" prop="projectGist" min-width="240"/>
						<el-table-column label="项目状态" align="center" prop="projStage">
							<template #default="scope">
								<dict-tag :type="'proj_parent_project_stage'" :value="scope.row.projStage" />
							</template>
						</el-table-column>
						<el-table-column label="操作" align="center" width="100">
							<template #default="scope">
								<el-button link type="primary" @click="handleNbView(scope.row.id)">查看</el-button>
							</template>
						</el-table-column>
					</el-table>
				</el-tab-pane>
				<el-tab-pane label="关联问题" name="3">
					<el-table :data="detailData.questionByQuesionId" border :stripe="true" :show-overflow-tooltip="true">
						<el-table-column type="index" label="序号" width="60" align="center"/>
						<el-table-column label="问题编码" align="left" prop="questionCode" min-width="180" />
						<el-table-column label="问题标题" align="left" prop="questionName" min-width="180" />
						<el-table-column label="问题描述" align="left" prop="quesDigest" min-width="180"/>
						<el-table-column label="审计建议" align="left" prop="auditSugg" min-width="240"/>
						<el-table-column label="关联项目" align="left" prop="projectName" min-width="180"/>
					</el-table>
				</el-tab-pane>
				<el-tab-pane v-if="false" label="审计模型" name="4">
					<el-table :data="detailData.auditModel" border :stripe="true" :show-overflow-tooltip="true">
						<el-table-column type="index" label="序号" width="60" align="center"/>
						<el-table-column label="模型名称" align="left" prop="name" min-width="180" />
						<el-table-column label="模型编码" align="left" prop="name" min-width="180"/>
						<el-table-column label="模型描述" align="left" prop="name" min-width="180"/>
						<el-table-column label="模型状态" align="center" prop="name" />
						<el-table-column label="操作" align="center" width="100">
							<template #default="scope">
								<el-button link type="primary" @click="handleMxView(scope.row)">查看</el-button>
							</template>
						</el-table-column>
					</el-table>
				</el-tab-pane>
			</el-tabs>
		</div>
	</Dialog>
	<!-- 预览附件 -->
	<DialogFlie ref="DialogFlieRef" />
	<!-- 外部法律法规-查看 -->
	<ItemDetail ref="ItemDetailRef" />
	<!-- 内部法律法规-查看 -->
	<OutDetail ref="OutDetailRef" />
	<!-- 关联项目-查看 -->
	<DetailMessage ref="detailMessageRef" />
</template>

<script lang="ts" setup>
	import { DICT_TYPE, NumberDictDataType, getIntDictOptions } from '@/utils/dict'
	import { formatTime } from '@/utils'
	import { QualitativeAnalysisApi, QualitativeAnalysisDetailVO } from '@/api/jobmanger/problem/qualitativeAnalysis'
	import ItemDetail from '@/views/jobmanger/regulations/outregulations/Detail.vue'
	import OutDetail from '@/views/jobmanger/regulations/innerregulations/Detail.vue'
	import DetailMessage from '@/views/auditManagement/projectAssignment/PreTrialPreparation/ProjectInitiation/message/DetailMessage.vue'
	defineOptions({ name: 'Detail' })

	const dialogVisible = ref(false) // 弹窗的是否展示
	const detailLoading = ref(false) // 表单地加载中
	const detailData = ref({} as QualitativeAnalysisDetailVO) // 详情数据
	const queryParams = reactive({
		id: undefined,
		auditRoleName: undefined,
		pageSize: 10,
		pageNo: 1,
		projectPhase: undefined,
		date: undefined,
		status: undefined,
	})
	const queryFormRef = ref() // 搜索的表单
	const total = ref(100)
	/** 打开弹窗 */
	const open = async (activeName : number, id:number, quesId : number) => {
		dialogVisible.value = true
		// 设置数据
		detailLoading.value = true
		try {
			detailData.value = {}
			detailData.value = await QualitativeAnalysisApi.getQualitativeAnalysis(id)
		} finally {
			detailLoading.value = false
		}
	}
	defineExpose({ open }) // 提供 open 方法，用于打开弹窗

	const activeName = ref('0')
	const handleClick = () => {

	}

	// 外部法律法规-查看
	const ItemDetailRef = ref()
	const handleWbView = async (id: number) => {
		await ItemDetailRef.value.open(id)
	}

	// 内部法律法规-查看
	const OutDetailRef = ref()
	const handleView = async (id : number) => {
		await OutDetailRef.value.open(id)
	}

	// 关联项目
	const detailMessageRef = ref()
	const handleNbView = (id: number) => {
		detailMessageRef.value.open(id)
	}
	const handleZlView = () => { }
	const handleMxView = () => { }
</script>
