<!--
* @Author: wangk
* @Date: 2024-10-26 17:10:50
* @Description: 领导班子业绩考核=>
-->
<template>
	<ContentWrap class="common-card-search">
		<!-- 搜索工作栏 -->
		<el-form class="-mb-15px common-search-form" :model="queryParams" ref="queryFormRef" :inline="true" label-width="60px">
			<el-form-item label="单位" prop="deptId">
				<el-tree-select v-model="queryParams.deptId" ref="treeRef" clearable placeholder="请选择所属单位"
					:data="deptList" check-strictly :expand-on-click-node="false" :check-on-click-node="true"
					:default-expand-all="false" highlight-current node-key="id" @node-click="handleNodeClick"
					:load="loadNode" @change="getList" :default-expanded-keys="defaultExpandedKeys"
					:filter-node-method="filterNode" lazy class="!w-200px">
					<template #default="{ data: { name } }">{{ name }}</template>
				</el-tree-select>
			</el-form-item>
			<el-form-item label="年份" prop="auditYear">
				<el-date-picker v-model="queryParams.auditYear" type="year" value-format="YYYY" class="!w-200px"
					placeholder="请选择年份" />
			</el-form-item>
		</el-form>
		<div class="right-search-btn">
			<el-button  type="primary"  @click="handleQuery">
					<Icon icon="ep:search" class="mr-5px" />搜索
				</el-button>
				<el-button @click="resetQuery">
					<Icon icon="ep:refresh" class="mr-5px" />重置
				</el-button>
		</div>
	</ContentWrap>

	<!-- 列表 -->
	<ContentWrap>
		<el-tabs v-model="activeName" class="demo-tabs" @tab-change="handleClick">
			<el-tab-pane label="考评指标" name="1" >

		<el-tabs v-model="activeName2" class="demo-tabs" @tab-change="handleClick2">
			<el-tab-pane label="山东港口集团对青岛港考评指标" name="1" />
			<el-tab-pane label="青岛港集团对基层单位考评指标" name="2" />
		</el-tabs>
      </el-tab-pane>
			<el-tab-pane label="预算完成" name="2" />
			<el-tab-pane label="履职费用" name="3" />
		</el-tabs>
		<!-- 考评指标 -->
		<!-- 山东港口集团对青岛港考评指标 -->
		<el-table border v-show="activeName == '1'&&activeName2 == '1'" v-loading="loading" :data="list" :stripe="true"
			:show-overflow-tooltip="true">
			<el-table-column label="序号" width="60" align="center">
				<template #default="{ $index }">{{ (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1
					}}</template>
			</el-table-column>
			<el-table-column label="指标名称" align="center" prop="questionCode" min-width="120" />
			<el-table-column label="实际值" align="center" prop="questionCode" min-width="120" />
			<el-table-column label="指标完成率(%)" align="center" prop="questionCode" min-width="120" />
			<el-table-column label="目标值" align="center">

        <el-table-column label="确保值" align="center" prop="questionCode" min-width="120" />
        <el-table-column label="奋斗值" align="center" prop="questionCode" min-width="120" />
      </el-table-column>
			<!-- <el-table-column label="立案时间" align="center" prop="planEndTime">
				<template #default="scope">
					<span>{{ formatTime(scope.row.planEndTime, 'yyyy-MM-dd') }}</span>
				</template>
			</el-table-column> -->
		</el-table>
		<!-- 青岛港集团对基层单位考评指标 -->
		<el-table border v-show="activeName == '1'&&activeName2 == '2'" v-loading="loading" :data="list" :stripe="true"
			:show-overflow-tooltip="true">
			<el-table-column label="序号" width="60" align="center">
				<template #default="{ $index }">{{ (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1
					}}</template>
			</el-table-column>
			<el-table-column label="指标名称" align="center" prop="questionCode" min-width="120" />
			<el-table-column label="实际值" align="center" prop="questionCode" min-width="120" />
			<el-table-column label="指标完成率(%)" align="center" prop="questionCode" min-width="120" />
			<el-table-column label="目标值" align="center">

        <el-table-column label="确保值" align="center" prop="questionCode" min-width="120" />
        <el-table-column label="奋斗值" align="center" prop="questionCode" min-width="120" />
      </el-table-column>
		</el-table>
		<!-- 预算完成 -->
		<el-table border v-show="activeName == '2'" v-loading="loading" :data="list1" :stripe="true"
			:show-overflow-tooltip="true">
			<el-table-column label="序号" width="60" align="center">
				<template #default="{ $index }">{{ (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1
					}}</template>
			</el-table-column>
			<el-table-column label="指标名称" align="center" prop="questionType" />
			<el-table-column label="数据来源" align="center" prop="questionTitle" />
			<el-table-column label="2022年" align="center" prop="questionTitle" />
			<el-table-column label="2023年" align="center" prop="questionTitle" />
			<el-table-column label="2024年" align="center" prop="questionTitle" />
		</el-table>
		<!-- 履职费用 -->
		<el-table border v-show="activeName == '3'" v-loading="loading" :data="list2" :stripe="true"
			:show-overflow-tooltip="true">
			<el-table-column label="序号" width="60" align="center">
				<template #default="{ $index }">{{ (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1
					}}</template>
			</el-table-column>
			<el-table-column label="领导名称" align="center" prop="quesDigest" />
			<el-table-column label="指标名称" align="center" prop="questionType" />
			<el-table-column label="数据来源" align="center" prop="questionTitle" />
			<el-table-column label="2022年" align="center" prop="questionTitle" />
			<el-table-column label="2023年" align="center" prop="questionTitle" />
			<el-table-column label="2024年" align="center" prop="questionTitle" />
		</el-table>
		<!-- 分页 -->
		<Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
			@pagination="getList" />
	</ContentWrap>
</template>

<script setup lang="ts">
import { RegulationsVO } from '@/api/jobmanger/regulations/outregulations'
import { reformingStandingBookApi } from '@/api/auditManagement/auditRectifica/reformingStandingBook'
import { handleLazyTree } from '@/utils/tree'
import * as DeptApi from '@/api/system/dept'
import { formatTime } from '@/utils'
defineOptions({ name: 'LeadershipPerformanceAssessment' })

const loading = ref(true) // 列表的加载中
const list = ref<RegulationsVO[]>([]) // 列表的数据
const list1 = ref<RegulationsVO[]>([]) // 列表的数据
const list2 = ref<RegulationsVO[]>([]) // 列表的数据
const queryParams = reactive({
	pageSize: 10,
	pageNo: 1,
	auditYear: undefined,
	deptId: undefined,
})
const queryFormRef = ref() // 搜索的表单
const total = ref(0)
const activeName = ref('1')
const activeName2 = ref('1')
const handleClick = async (type) => {
	console.log(type);
	activeName.value = type
}
const handleClick2 = async (type) => {
	console.log(type);
	activeName2.value = type
}
/** 查询列表 */
const getList = async () => {
	loading.value = true
	try {
		const data = await reformingStandingBookApi.getRectificationLedgerList(queryParams)
		list.value = data.list
		total.value = data.total
	} finally {
		loading.value = false
	}
}
// 获取单位
interface DeptNode {
	id: number
	masterOrgId?: number | string
	name: string
	parentId: number | string
	children?: DeptNode[]
	isLeaf?: boolean
}
const deptList = ref<DeptNode[]>([]) // 树形结构
const defaultExpandedKeys = ref<(string | number)[]>([])
const getTree = async (id) => {
	deptList.value = []
	const res = await DeptApi.getSimpleDeptList(id)
	deptList.value.push(...handleLazyTree(res, 'id', 'parentId'))
	defaultExpandedKeys.value = deptList.value.map((node) => node.id)
}
const handleNodeClick = (node) => {
	// 如果是父节点，选中它
	queryParams.deptId = node.id
	getList()
}
const filterNode = (name: string, data: DeptNode) => {
	if (!name) return true
	return data.name.includes(name)
}
const loadNode = async (node: Node, resolve: (data: any[]) => void) => {
	try {
		const nodeId = node.data.id
		if (nodeId == undefined || nodeId == null) {
			return
		}
		const res = await DeptApi.getSimpleDeptList(nodeId)
		const children = handleLazyTree(res, 'id', 'parentId', 'children')
		resolve(children)
	} catch (error) {
		resolve([])
	}
}
/** 搜索按钮操作 */
const handleQuery = () => {
	queryParams.pageNo = 1
	getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
	queryFormRef.value.resetFields()
	handleQuery()
}

/** 初始化 **/
onMounted(() => {
	getList()
	getTree(0)
})
</script>
