<!-- 人员看板 -->
<template>
    <div class="header">
        <div class="header_title">
            <div class="header_title_size">
                人员情况统计
            </div>
            <div class="header_date">
                <div class="pr-14px">计划年度</div>
                <el-date-picker v-model="queryParams.publishDate" type="year" placeholder="开始" class="!w-90px"
                    @change="handleQuery"/>
            </div>
        </div>
        <div class="flex items-center scroll-container pt-12px pr-20px pl-20px">
            <el-button class="arrow left-arrow" @click="scrollContents('left')" circle>
                <Icon icon="ep:arrow-left" />
            </el-button>
            <div class="flex items-center scroll-content">
                <div class="header_card w-264px">
                    <div class="header_card_title">
                        审计人员
                    </div>
                    <el-row class="flex items-center pl-20px pt-10px">
                        <el-col :span="10" :xs="24">
                            <el-image style="width: 64px; height: 64px" :src="url1" fit="cover" />
                        </el-col>
                        <el-col :span="14" :xs="24">
                            <div class="card_number text-center">
                                350
                            </div>
                            <div class="card_text text-center">
                                审计人员人数
                            </div>
                        </el-col>
                    </el-row>
                </div>
                <div class="header_card ml-6px w-360px">
                    <div class="header_card_title">
                        执业资格证书及占比
                    </div>
                    <el-row class="flex items-center pl-20px pt-10px">
                        <el-col :span="6" :xs="24">
                            <el-image style="width: 64px; height: 64px" :src="url2" fit="cover" />
                        </el-col>
                        <el-col :span="9" :xs="24">
                            <div class="card_number text-center">
                                350
                            </div>
                            <div class="card_text text-center">
                                执业资格证书
                            </div>
                        </el-col>
                        <el-col :span="9" :xs="24">
                            <div class="card_number text-center">
                                43.75%
                            </div>
                            <div class="card_text text-center">
                                占比
                            </div>
                        </el-col>
                    </el-row>
                </div>
                <div class="header_card ml-6px w-360px">
                    <div class="header_card_title">
                        职称人数及占比
                    </div>
                    <el-row class="flex items-center pl-20px pt-10px">
                        <el-col :span="6" :xs="24">
                            <el-image style="width: 64px; height: 64px" :src="url3" fit="cover" />
                        </el-col>
                        <el-col :span="9" :xs="24">
                            <div class="card_number text-center">
                                350
                            </div>
                            <div class="card_text text-center">
                                持有职称人数
                            </div>
                        </el-col>
                        <el-col :span="9" :xs="24">
                            <div class="card_number text-center">
                                43.75%
                            </div>
                            <div class="card_text text-center">
                                占比
                            </div>
                        </el-col>
                    </el-row>
                </div>
                <div class="header_card ml-6px w-504px">
                    <div class="header_card_title">
                        职业分布
                    </div>
                    <el-row class="flex items-center pl-20px pt-10px">
                        <el-col :span="3" :xs="24">
                            <el-image style="width: 64px; height: 64px" :src="url4" fit="cover" />
                        </el-col>
                        <el-col :span="7" :xs="24">
                            <div class="card_number text-center">
                                350
                            </div>
                            <div class="card_text text-center">
                                信息技术专业
                            </div>
                        </el-col>
                        <el-col :span="7" :xs="24">
                            <div class="card_number text-center">
                                43.75%
                            </div>
                            <div class="card_text text-center">
                                经济管理专业
                            </div>
                        </el-col>
                        <el-col :span="7" :xs="24">
                            <div class="card_number text-center">
                                43.75%
                            </div>
                            <div class="card_text text-center">
                                会计专业人数
                            </div>
                        </el-col>
                    </el-row>
                </div>
                <div class="header_card ml-6px w-360px">
                    <div class="header_card_title">
                        项目借调
                    </div>
                    <el-row class="flex items-center pl-20px pt-10px">
                        <el-col :span="6" :xs="24">
                            <el-image style="width: 64px; height: 64px" :src="url5" fit="cover" />
                        </el-col>
                        <el-col :span="9" :xs="24">
                            <div class="card_number text-center">
                                350
                            </div>
                            <div class="card_text text-center">
                                项目借调人数
                            </div>
                        </el-col>
                        <el-col :span="9" :xs="24">
                            <div class="card_number text-center">
                                43.75%
                            </div>
                            <div class="card_text text-center">
                                借调人次
                            </div>
                        </el-col>
                    </el-row>
                </div>
            </div>
            <el-button class="arrow right-arrow" @click="scrollContents('right')" circle>
                <Icon icon="ep:arrow-right" />
            </el-button>
        </div>
    </div>
    <el-row :gutter="8" class="pt-16px">
        <el-col :span="6" :xs="24">
            <ContentWrap>
                <div class="flex_title">
                    <div class="title-left"></div>
                    <div class="pl-8px">学历分布</div>
                </div>
                <div :class="oneChart" ref="oneChartRef" style="height:288px;width:100%;"></div>
            </ContentWrap>
        </el-col>
        <el-col :span="6" :xs="24">
            <ContentWrap>
                <div class="flex_title">
                    <div class="title-left"></div>
                    <div class="pl-8px">岗位分布</div>
                </div>
                <div :class="twoChart" ref="twoChartRef" style="height:288px;width:100%;"></div>
            </ContentWrap>
        </el-col>
        <el-col :span="6" :xs="24">
            <ContentWrap>
                <div class="flex_title">
                    <div class="title-left"></div>
                    <div class="pl-8px">执业资格分布</div>
                </div>
                <div :class="threeChart" ref="threeChartRef" style="height:288px;width:100%;"></div>
            </ContentWrap>
        </el-col>
        <el-col :span="6" :xs="24">
            <ContentWrap>
                <div class="flex_title">
                    <div class="title-left"></div>
                    <div class="pl-8px">年龄分布</div>
                </div>
                <div :class="fourChart" ref="fourChartRef" style="height:288px;width:100%;"></div>
            </ContentWrap>
        </el-col>
    </el-row>
    <el-row :gutter="8">
        <el-col :span="6" :xs="24">
            <ContentWrap>
                <div class="flex_title">
                    <div class="title-left"></div>
                    <div class="pl-8px">专业分布</div>
                </div>
                <div :class="fiveChart" ref="fiveChartRef" style="height:288px;width:100%;"></div>
            </ContentWrap>
        </el-col>
        <el-col :span="6" :xs="24">
            <ContentWrap>
                <div class="flex_title">
                    <div class="title-left"></div>
                    <div class="pl-8px">职称分布</div>
                </div>
                <div :class="sixChart" ref="sixChartRef" style="height:288px;width:100%;"></div>
            </ContentWrap>
        </el-col>
        <el-col :span="6" :xs="24">
            <ContentWrap>
                <div class="flex_title">
                    <div class="title-left"></div>
                    <div class="pl-8px">从事审计工作年限</div>
                </div>
                <div :class="sevenChart" ref="sevenChartRef" style="height:288px;width:100%;"></div>
            </ContentWrap>
        </el-col>
        <el-col :span="6" :xs="24">
            <ContentWrap>
                <div class="flex_title">
                    <div class="title-left"></div>
                    <div class="pl-8px">参与集团项目人次TOP5</div>
                </div>
                <div style="height:288px;width:100%;">
                    <div class="flex items-center eightChart pt-4px" v-for="(item,index) in tableData" :key="index"
                    :class="item.rank === 1 ? 'img_color1' : item.rank === 2 ? 'img_color2' : item.rank === 3?'img_color3':'img_color4'">
                        <div class="eightChart_img">
                            <el-image v-if="item.rank == 1" style="width: 32px; height: 26px" :src="rankurl1"
                                fit="cover" />
                            <el-image v-else-if="item.rank == 2" style="width: 32px; height: 26px" :src="rankurl2"
                                fit="cover" />
                            <el-image v-else-if="item.rank == 3" style="width: 32px; height: 26px" :src="rankurl3"
                                fit="cover" />
                            <span v-else>{{ item.rank }}</span>
                        </div>
                        <div class="header_card_item">{{ item.companyName }}</div>
                        <div class="eightChart_number">{{ item.totalProblems }}</div>
                    </div>
                </div>
            </ContentWrap>
        </el-col>
    </el-row>
</template>

<script lang="ts" setup>
import * as echarts from 'echarts';
import demoviewIcon1 from '@/assets/imgs/decision/icon-shenji.png';
import demoviewIcon2 from '@/assets/imgs/decision/icon-zhiye.png';
import demoviewIcon3 from '@/assets/imgs/decision/icon-zhicheng.png';
import demoviewIcon4 from '@/assets/imgs/decision/icon-fenbu.png';
import demoviewIcon5 from '@/assets/imgs/decision/icon-jiediao.png';
// 排名
import demoviewIcon24 from '@/assets/imgs/decision/icon-top1.png';
import demoviewIcon25 from '@/assets/imgs/decision/icon-top2.png';
import demoviewIcon26 from '@/assets/imgs/decision/icon-top3.png';
import { colorList } from '../../index'
defineOptions({
    name: 'PersonnelSignage'
})
const queryParams = reactive({
    publishDate: undefined,
})
const loading = ref(true) // 列表的加载中

const url1 = ref(demoviewIcon1)
const url2 = ref(demoviewIcon2)
const url3 = ref(demoviewIcon3)
const url4 = ref(demoviewIcon4)
const url5 = ref(demoviewIcon5)

const rankurl1 = ref(demoviewIcon24)
const rankurl2 = ref(demoviewIcon25)
const rankurl3 = ref(demoviewIcon26)
// 左右滑动
const scrollContents = (direction) => {
    const scrollContent = document.querySelector('.scroll-content');
    const scrollAmount = 150; // 每次滚动的距离

    if (direction === 'left') {
        scrollContent.scrollLeft -= scrollAmount;
    } else if (direction === 'right') {
        scrollContent.scrollLeft += scrollAmount;
    }
}
const color = ['#2A71C0', '#FF9C01', '#AC3C52', '#C96F37', '#95C2ED', '#E6E8F4', '#ABEDD8']
// 第一个echarts
let oneChart: echarts.ECharts | null = null;
const oneChartRef = ref()
const getOneChart = async () => {
    oneChart = echarts.init(oneChartRef.value)
    oneChart.setOption({
        color: color,
        tooltip: {
            trigger: 'axis'
        },
        grid: {
            left: 0,
            right: '5%',
            bottom: '10%',
            top: '10%',
            containLabel: true
        },
        xAxis: {
            type: 'value',
            axisLabel: {
                formatter: '{value}人'
            }
        },
        yAxis: {
            type: 'category',
            axisTick: {
                show: false
            },
            data: ['大学专科及以下', '大学本科', '硕士研究生', '博士研究生及以上']
        },
        series: [{
            type: 'bar',
            stack: 'total',
            data: [100, 140, 230, 100, 130],
            barMaxWidth: 40,
            itemStyle: {
                borderRadius: [0, 10, 10, 0]
            }
        }]
    })
}

// 第二个echarts
let twoChart: echarts.ECharts | null = null;
const twoChartRef = ref()
const getTwoChart = async () => {
    twoChart = echarts.init(twoChartRef.value)
    const data = [
        { value: 335, name: '综合审计' },
        { value: 310, name: '工程审计' },
        { value: 234, name: '经责审计' },
        { value: 135, name: '内控审计' },
        { value: 135, name: '风险管理' },
    ]
    twoChart.setOption({
        color: color,
        tooltip: {
            trigger: 'item',
        },
        legend: {
            top: 'bottom',
            itemWidth: 14,
            data: data
        },
        series: [
            {
                name: '姓名',
                type: 'pie',
                radius: [20, 100],
                center: ['50%', '40%'],
                roseType: 'area',
                data: data,
                itemStyle: {
                    borderRadius: 2,
                    normal: {
                        color: function (params) {
                            return colorList[params.dataIndex];
                        },
                    },
                },
                label: {
                    show: false
                },
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }
        ]
    })
}

// 第三个echarts
let threeChart: echarts.ECharts | null = null;
const threeChartRef = ref()
const getThreeChart = async () => {
    threeChart = echarts.init(threeChartRef.value)
    const data = [
        { value: 335, name: 'CIA' },
        { value: 310, name: 'CISA' },
        { value: 234, name: 'CPA' },
        { value: 135, name: '注册管理会计师' },
        { value: 135, name: '注册造价师' },
        { value: 135, name: '建造师' },
        { value: 135, name: '高级审计师' },
    ]
    threeChart.setOption({
        color: color,
        tooltip: {
            trigger: 'item',
        },
        legend: {
            top: 'bottom',
            itemWidth: 14,
            data: data
        },
        series: [
            {
                name: '姓名',
                type: 'pie',
                radius: '60%',
                center: ['50%', '40%'],
                data: data,
                label: {
                    show: false
                },
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                },
                selectedMode: "single",
            }
        ]
    })
}

// 第四个echarts
let fourChart: echarts.ECharts | null = null;
const fourChartRef = ref()
const getFourChart = async () => {
    fourChart = echarts.init(fourChartRef.value)
    fourChart.setOption({
        color: color,
        grid: {
            top: "10%",
            right: "10%",
            left: "10%",
            bottom: "10%",
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross',
                crossStyle: {
                    color: '#999'
                }
            }
        },
        xAxis: [
            {
                type: 'category',
                data: ['30岁以下', '31-40岁', '41-50岁', '50岁以上'],
                axisPointer: {
                    type: 'shadow'
                },
                axisLabel: {
                    interval: 0,
                }
            }
        ],
        yAxis: [
            {
                type: 'value',
            }
        ],
        series: [
            {
                type: 'bar',
                data: [2.0, 4.9, 7.0, 23.2, 25.6],
                barMaxWidth: 40,
                itemStyle: {
                    borderRadius: [10, 10, 0, 0]
                }
            }
        ]
    })
}

// 第五个echarts
let fiveChart: echarts.ECharts | null = null;
const fiveChartRef = ref()
const getFiveChart = async () => {
    fiveChart = echarts.init(fiveChartRef.value)
    const data = [
        { value: 335, name: '信息技术' },
        { value: 310, name: '经济' },
        { value: 234, name: '会计' },
        { value: 234, name: '综合管理' },
        { value: 234, name: '工程' },
        { value: 234, name: '其他' },
    ]
    fiveChart.setOption({
        color: color,
        // tooltip: {
        //     trigger: 'item',
        // },
        legend: {
            top: 'bottom',
            itemWidth: 14,
            data: data
        },
        series: [
            {
                name: '姓名',
                type: 'pie',
                radius: ['50%', '60%'],
                center: ['50%', '40%'],
                avoidLabelOverlap: false,
                padAngle: 5,
                itemStyle: {
                    borderRadius: 10
                },
                label: {
                    show: false,
                    position: 'center'
                },
                emphasis: {
                    label: {
                        show: true,
                        fontSize: 18,
                        fontWeight: 500
                    }
                },
                labelLine: {
                    show: false
                },
                data: data
            }
        ]
    })
}
// 第六个echarts
let sixChart: echarts.ECharts | null = null;
const sixChartRef = ref()
const getSixChart = async () => {
    sixChart = echarts.init(sixChartRef.value)
    const data = [
        { name: '初级职称', max: 6500 },
        { name: '中级职称', max: 16000 },
        { name: '副高级职称', max: 30000 },
        { name: '正高级职称', max: 38000 },
        { name: '无职称', max: 52000 }
    ]
    sixChart.setOption({
        color: color,
        tooltip: {
            trigger: 'item',
        },
        radar: {
            indicator: data,
            radius: ["0", "70%"]
        },
        series: [
            {
                type: 'radar',
                data: [
                    {
                        value: [4200, 3000, 20000, 35000, 50000, 18000, 1111, 2222],
                    }
                ]
            }
        ]
    })
}
// 第七个echarts
let sevenChart: echarts.ECharts | null = null;
const sevenChartRef = ref()
const getSevenChart = async () => {
    sevenChart = echarts.init(sevenChartRef.value)
    sevenChart.setOption({
        color: color,
        grid: {
            top: "10%",
            right: "10%",
            left: "10%",
            bottom: "10%",
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross',
                crossStyle: {
                    color: '#999'
                }
            }
        },
        xAxis: [
            {
                type: 'category',
                data: ['30岁以下', '31-40岁', '41-50岁', '50岁以上'],
                axisPointer: {
                    type: 'shadow'
                },
                axisLabel: {
                    interval: 0,
                }
            }
        ],
        yAxis: [
            {
                type: 'value',
            }
        ],
        series: [
            {
                type: 'bar',
                data: [2.0, 4.9, 7.0, 23.2, 25.6],
                barMaxWidth: 40,
                itemStyle: {
                    borderRadius: [10, 10, 0, 0]
                }
            }
        ]
    })
}
// 第八个echarts
const tableData = [
    { rank: 1, companyName: '山东港集团审计部', totalProblems: 1991},
    { rank: 2, companyName: '青岛国际某某分公司', totalProblems: 1990 },
    { rank: 3, companyName: '青岛港集团审计部', totalProblems: 1989 },
    { rank: 4, companyName: '贸易集团审计部', totalProblems: 1988 },
    { rank: 5, companyName: '邮轮发展集团审计部', totalProblems: 1987 }
]

const getAllApi = async () => {
    await Promise.all([getOneChart()])
    await Promise.all([getTwoChart()])
    await Promise.all([getThreeChart()])
    await Promise.all([getFourChart()])
    await Promise.all([getFiveChart()])
    await Promise.all([getSixChart()])
    await Promise.all([getSevenChart()])
    loading.value = false
}

/** 搜索按钮操作 */
const handleQuery = () => {
    console.log('000')
    getAllApi()
}

/** 初始化 **/
onMounted(() => {
    getAllApi()
})
</script>

<style scoped>
/* 去除输入框边框 */
::v-deep .el-date-editor {
	--el-input-border-color: transparent !important;
	--el-input-bg-color: transparent;
    --el-input-hover-border-color: #fff;
}
.flex_title {
    display: flex;
    align-items: center;
    line-height: 33px;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 700;
    font-size: 16px;
    color: #333333;
    border-bottom: 1px solid #EEEFF0;
}

.title-left {
    width: 5px;
    height: 15px;
    background: #2F4CAD;
    border-radius: 0px 0px 0px 0px;
}

.header {
    height: 199px;
    background-image: url('@/assets/imgs/decision/bg.png');
    background-repeat: no-repeat;
    background-size: cover;
}

.header_title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 20px 0;
}

.header_title_size {
    height: 26px;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 500;
    font-size: 18px;
    color: #333333;
    line-height: 26px;
}

.header_date {
    width: 174px;
    height: 34px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 8px 8px 8px 8px;
    border: 2px solid #FFFFFF;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    font-size: 15px;
    color: #999;
    line-height: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.scroll-container {
    width: 97%;
    overflow: hidden;
    position: relative;
}

.arrow {
    min-width: auto !important;
    width: 45px;
    height: 45px;
    cursor: pointer;
    position: absolute;
    z-index: 1;
    opacity: 0.7;
}

.right-arrow {
    right: 20px;
}

.scroll-content {
    scroll-snap-type: x mandatory;
    scroll-behavior: smooth;
    overflow-x: scroll;
    overflow: hidden;
    white-space: nowrap;
    width: 100%;
}

.header_card {
    flex-shrink: 0;
    height: 125px;
    background: rgba(246, 247, 254, 0.7);
    border-radius: 8px 8px 8px 8px;
    border: 2px solid #FFFFFF;
}

.header_card_flex1 {
    flex: 1;
}

.header_card_flex2 {
    flex: 2;
}

.header_card_flex3 {
    flex: 3;
}

.header_card_title {
    height: 23px;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    font-size: 16px;
    color: #333333;
    line-height: 23px;
    padding-top: 14px;
    padding-left: 20px;
}

.header_card_item {
    flex: 1;
}

.card_number {
    height: 41px;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 700;
    font-size: 26px;
    color: #333333;
    line-height: 41px;
}

.card_text {
    height: 22px;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    font-size: 15px;
    color: #999999;
    line-height: 22px;
}

.eightChart{
    height: 48px;
    border-radius: 8px 8px 8px 8px;
    margin-top: 6px;
}
.eightChart_img{
    width: 50px;
    display: flex;
    justify-content: center;
}
.img_color1{
    background: linear-gradient( 90deg, #FFF1DA 20%,  rgba(255,242,219,0) 100%);
}
.img_color2{
    background: linear-gradient( 90deg, #DFECFF 20%,  rgba(220,234,255,0) 100%);
}
.img_color3{
    background: linear-gradient( 90deg, #FFE4E5 20%,  rgba(255,220,222,0) 100%);
}
.eightChart_number{
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 700;
    font-size: 18px;
    color: #333333;
    line-height: 26px;
}
</style>