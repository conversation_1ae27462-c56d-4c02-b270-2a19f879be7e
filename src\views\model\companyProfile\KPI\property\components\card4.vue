<!--
* @Author: wangk
* @Date: 2024-10-26 17:10:50
* @Description: 领导班子业绩考核=>
-->
<template>
  <div>
   <ContentWrap :title="'资产指标'" :shut="true">
     <!-- 客户指标表格 -->
     <el-table
       v-loading="loading"
       :data="list"
       :stripe="true"
       :show-overflow-tooltip="true"
     >
       <el-table-column label="序号" width="60" align="center">
         <template #default="{ $index }">{{
           (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1
         }}</template>
       </el-table-column>
       <el-table-column label="指标名称" align="center" prop="questionType" />
       <el-table-column label="上档基础分" align="center" prop="questionTitle" />
       <el-table-column label="本档基础分" align="center" prop="questionTitle" />
       <el-table-column label="调整分" align="center" prop="questionTitle" />
       <el-table-column label="指标得分" align="center" prop="questionTitle" />
     </el-table>
     <!-- 分页 -->
     <Pagination
       :total="total"
       v-model:page="queryParams.pageNo"
       v-model:limit="queryParams.pageSize"
       @pagination="props.getList()"
     />
   </ContentWrap>
  </div>
</template>

<script setup lang="ts">
import { RegulationsVO } from '@/api/jobmanger/regulations/outregulations'
// import { reformingStandingBookApi } from '@/api/auditManagement/auditRectifica/reformingStandingBook'
import { handleLazyTree } from '@/utils/tree'
import * as DeptApi from '@/api/system/dept'
import * as echarts from 'echarts'
import { propTypes } from '@/utils/propTypes'
// import { formatTime } from '@/utils'
defineOptions({ name: 'Card4' })

const loading = ref(false) // 列表的加载中
const list = ref() // 列表的数据
const queryParams = reactive({
  pageSize: 10,
  pageNo: 1
})
const total = ref(0)
// 定义属性
const props = defineProps({
  tableData: propTypes.object.def({}),
  getList: {
    type: Function,
    default: ()=>{}
  }
})
list.value=props.tableData
// 获取单位
interface DeptNode {
  id: number
  masterOrgId?: number | string
  name: string
  parentId: number | string
  children?: DeptNode[]
  isLeaf?: boolean
}
const deptList = ref<DeptNode[]>([]) // 树形结构
const defaultExpandedKeys = ref<(string | number)[]>([])
const getTree = async (id) => {
  deptList.value = []
  const res = await DeptApi.getSimpleDeptList(id)
  deptList.value.push(...handleLazyTree(res, 'id', 'parentId'))
  defaultExpandedKeys.value = deptList.value.map((node) => node.id)
}
// 右边图表
let bottomChart = null
const bottomChartRef = ref<InstanceType<typeof ElTree>>()
const getBottomChart = async () => {
  bottomChart = echarts.init(bottomChartRef.value)
  bottomChart.setOption({
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      data: ['合同', '采购', '财务', '工程', '其他']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        type: 'bar',
        barWidth: '45%',
        stack: 'total',
        label: {
          show: true
        },
        itemStyle: {
          normal: {
            barBorderRadius: [8, 8, 0, 0]
          },
          color: '#2A8AF7'
        },
        data: [11, 12, 13, 14, 11]
      }
    ]
  })
}


/** 初始化 **/
onMounted(async () => {
  list.value=props.tableData
  console.log(list,'11111')
  // getTree(0)
  // await getBottomChart()
})
</script>
