<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-11-13 10:39:19
 * @Description: 问题定性调整
-->
<template>
  <el-row>
    <el-col :span="24" :xs="24">
      <!-- 查询 -->
      <ContentWrap class="common-card-search">
        <el-form class="-mb-15px common-search-form" :model="queryParams" ref="queryFormRef" :inline="true" label-width="90px" >
          <el-form-item label="问题定性" prop="questionQualitative">
            <el-input v-model="queryParams.questionQualitative" placeholder="请输入" clearable @keyup.enter="handleQuery" class="!w-200px" />
          </el-form-item>
          <el-form-item label="问题编号" prop="questionCode">
            <el-input v-model="queryParams.questionCode" placeholder="请输入" clearable @keyup.enter="handleQuery" class="!w-200px" />
          </el-form-item>
          <el-form-item label="问题标题" prop="questionName">
            <el-input v-model="queryParams.questionName" placeholder="请输入" clearable @keyup.enter="handleQuery" class="!w-200px" />
          </el-form-item>
          <el-form-item label="问题描述" prop="quesDigest">
            <el-input v-model="queryParams.quesDigest" placeholder="请输入" clearable @keyup.enter="handleQuery" class="!w-200px" />
          </el-form-item>
          <el-form-item label="审计建议" prop="auditSugg">
            <el-input v-model="queryParams.auditSugg" placeholder="请输入" clearable @keyup.enter="handleQuery" class="!w-200px" />
          </el-form-item>
          <el-form-item label="被审计单位" prop="auditObject">
            <el-input v-model="queryParams.auditObject" placeholder="请输入" clearable @keyup.enter="handleQuery" class="!w-200px" />
          </el-form-item>
          <el-form-item label="发现人" prop="discoverUserName">
            <el-input v-model="queryParams.discoverUserName" placeholder="请输入" clearable @keyup.enter="handleQuery" class="!w-200px" />
          </el-form-item>
        </el-form>
        <div class="right-search-btn">
          <el-button  type="primary"  @click="handleQuery">
            <Icon icon="ep:search" class="mr-5px" />搜索
          </el-button>
          <el-button @click="resetQuery">
            <Icon icon="ep:refresh" class="mr-5px" />重置
          </el-button>
        </div>
      </ContentWrap>
      <ContentWrap>
        <div class="button_margin15">
          <!-- <el-button type="primary" plain @click="openForm('create',queryParams.questId)" >
            <Icon icon="ep:plus" class="mr-5px" />新增问题定性
          </el-button> -->
          <el-button v-hasPermi="['audit:proj-await-question:batch-insert']" type="primary" :disabled="batchShow" plain @click="handleType('more')">
            <Icon icon="ep:plus" class="mr-5px" />批量入库定性
          </el-button>
          <el-button v-hasPermi="['audit:tank-ques-qualitation:export']" :loading="exportLoading" plain @click="handleExport">
            <Icon class="mr-5px" icon="ep:download" />导出
          </el-button>
          <!-- <el-button type="primary" :disabled="batchShow" plain @click="handleType('more')" >
            <Icon icon="ep:plus" class="mr-5px" />批量调整分类
          </el-button> -->
        </div>
        <el-table border v-loading="loading" :data="list" @selection-change="handleSelectionChange" :stripe="true" :show-overflow-tooltip="true">
          <el-table-column type="selection" :selectable="selectable" width="45" align="center" fixed="left" />
          <el-table-column label="序号" type="index" :selectable="selectable" width="55" align="center" />
          <el-table-column label="问题定性" align="left" prop="" min-width="180">
            <template #default="scope">
              {{scope.row.questionQualitative?scope.row.questionQualitative:'未定性入库'}}
            </template>
          </el-table-column>
          <el-table-column label="问题标题" align="left" prop="questionName" min-width="260"/>
          <el-table-column label="问题描述" align="left" prop="quesDigest" min-width="180" />
          <el-table-column label="发现人" align="center" prop="discoverUserName" />
          <el-table-column label="审计建议" align="left" prop="auditSugg"  min-width="180"/>
          <el-table-column label="被审计单位" align="left" prop="auditObject"  min-width="180"/>
          <el-table-column label="操作" align="center" :width="120" fixed="right">
            <template #default="scope">
              <!-- <el-button type="primary" link @click="handleType('one', [scope.row.id])">调整分类</el-button> -->
              <el-button v-hasPermi="['audit:tank-ques-qualitation:storage']" v-if="scope.row.questionStatus != 3" type="primary" link @click="openForm('create',scope.row)">定性入库</el-button>
            </template>
          </el-table-column>
        </el-table>
        <Pagination
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </ContentWrap>
    </el-col>
  </el-row>
  <!-- 表单弹窗：添加/修改 -->
	<Form ref="formRef" @success="getList" />
  <!-- 调整问题定性 -->
	<Controls ref="controlsRef" @success="getList" />
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import download from '@/utils/download'
import { ListOfProblemsApi } from '@/api/auditManagement/projectAssignment/auditReport/issuesList'
import { QualitativeAdjustmentApi } from '@/api/jobmanger/problem/qualitativeAdjustment'
import Form from './form.vue'
// import Controls from '../qualitativeAnalysis/Controls.vue'
import Controls from './Controls.vue'
import { TimeSplicing } from '@/utils/formatTime'

defineOptions({ name: 'QualitativeAdjustment' })
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数
const { query } = useRoute() 
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  questionQualitative: undefined, //问题定性
  quesTypeName: undefined, //问题分类名称
  questionCode: undefined, //问题编号
  questionName: undefined, //问题标题
  quesDigest: undefined, //问题摘要
  auditSugg: undefined, //审计建议
  auditObject: undefined, //被审计单位
  discoverUserName: undefined, //发现人
  // questionStatus: 1, //默认查询未定性数据
})
const queryFormRef = ref() // 搜索的表单
const batchShow = ref(true) //批量操作按钮禁用状态

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await QualitativeAdjustmentApi.getQualitativeAnalysisList(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  handleQuery()
}

/** 处理部门被点击 */
const handleDeptNodeClick = async (row) => {
  queryParams.deptId = row.id
  await getList()
}

/** 新增问题分类操作 */
const formRef = ref()
const openForm = (type : string, row:any) => {
  formRef.value.open(type, row)
}

//批量入库定性按钮
	const controlsRef = ref()
	const handleType = async(type: string, id: []) => {
		const data = ref([])
		if(type == 'one'){
			data.value = id
		}else if(type == 'more') {
			data.value = idList.value
		}
		controlsRef.value.open('选择定性分类', 2, data.value)
	}

/** 导出按钮操作 */
const exportLoading = ref(false)
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await QualitativeAdjustmentApi.exportQualitativeAnalysis(queryParams)
    const time = TimeSplicing(new Date())
    download.excel(data, `问题定性调整${time}.xls`)
  } catch {
  } finally {
    exportLoading.value = false
  }
}

// 列表多选触发事件
const idList = ref([])
const handleSelectionChange = async(val) => {
  // idList.value = val.map(item => {return item.id})
  idList.value = val
  if(val.length == 0){
    batchShow.value = true
  }else if(val.length > 0){
    batchShow.value = false
  }
}

/** 初始化 */
onMounted(async() => {
  getList()
})
</script>
