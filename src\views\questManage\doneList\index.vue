<!--
* @Author: li<PERSON><PERSON>g
* @Date: 2024-09-20 10:31:05
* @Description: 已办列表=>
-->
<template>
	<ContentWrap>
		<!-- 搜索工作栏 -->
		<el-form class="-mb-15px" :model="queryParams" ref="queryFormRef" :inline="true" label-width="124px">
			<el-form-item label="待办标题" prop="auditRoleName">
				<el-input v-model="queryParams.auditRoleName" placeholder="请输入待办标题" clearable @keyup.enter="handleQuery"
					class="!w-240px" />
			</el-form-item>
			<el-form-item label="流程名称" prop="auditRoleName">
				<el-input v-model="queryParams.auditRoleName" placeholder="请输入流程名称" clearable @keyup.enter="handleQuery"
					class="!w-240px" />
			</el-form-item>
			<el-form-item label="上环节处理人" prop="auditRoleName">
				<el-input v-model="queryParams.auditRoleName" placeholder="请输入上环节处理人" clearable
					@keyup.enter="handleQuery" class="!w-240px" />
			</el-form-item>
			<!-- <el-form-item label="状态" prop="status">
				<el-select v-model="queryParams.status" placeholder="请选择状态" clearable class="!w-240px">
					<el-option v-for="dict in getIntDictOptions(DICT_TYPE.AUDIT_TYPE_STATUS)" :key="dict.value"
						:label="dict.label" :value="dict.value" />
				</el-select>
			</el-form-item> -->
			<el-form-item>
				<el-button type="primary" @click="handleQuery">
					<Icon icon="ep:search" class="mr-5px" />搜索
				</el-button>
				<el-button @click="resetQuery">
					<Icon icon="ep:refresh" class="mr-5px" />重置
				</el-button>
			</el-form-item>
		</el-form>
	</ContentWrap>

	<!-- 列表 -->
	<ContentWrap>
		<el-table border v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
			<el-table-column label="序号" width="60" align="center">
				<template
					#default="{ $index }">{{ (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1 }}</template>
			</el-table-column>
			<el-table-column label="待办标题" align="center" prop="auditRoleCode" min-width="120">
				<template #default="scope">
					<el-link type="primary" @click="handleDone(scope.row)"
						:underline="false">{{scope.row.auditRoleCode}}</el-link>
				</template>
			</el-table-column>
			<el-table-column label="流程名称" align="center" prop="auditRoleName" />
			<el-table-column label="当前环节" align="center" prop="sort" />
			<el-table-column label="上环节处理人" align="center" prop="adder">
				<template #default="scope">
					<span class="flex_center">
						<Icon icon="ep:avatar" class="mr-5px" color="#4285F5" />{{ scope.row.adder }}
					</span>
				</template>
			</el-table-column>
			<el-table-column label="上环节处理时间" align="center" key="createTime">
				<template #default="scope">
					<span class="flex_center">
						<Icon icon="ep:clock" class="mr-5px" color="#81B338" />
						{{ formatTime(scope.row.createTime, 'yyyy-MM-dd') }}
					</span>
				</template>
			</el-table-column>
		</el-table>
		<!-- 分页 -->
		<Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
			@pagination="getList" />
	</ContentWrap>

	<!-- 查看详情弹窗 -->
	<!-- <AuditRoleDetail ref="detailRef" /> -->
</template>

<script setup lang="ts">
	import { getIntDictOptions, getStrDictOptions, DICT_TYPE, NumberDictDataType } from '@/utils/dict'

	import { handleTree } from '@/utils/tree'
	import download from '@/utils/download'
	import { AuditRoleApi, AuditRoleVO, AuditRoleDetailVO } from '@/api/basicData/auditRole'
	import { formatTime } from '@/utils'
	// import AuditRoleDetail from './AuditRoleDetail.vue'
	import { ElementPlusInfoType } from '@/types/elementPlus'
	/** 审计角色 列表 */
	defineOptions({ name: 'DoneList' })

	const message = useMessage() // 消息弹窗
	const { t } = useI18n() // 国际化

	const loading = ref(true) // 列表的加载中
	const list = ref<AuditRoleVO[]>([]) // 列表的数据
	const queryParams = reactive({
		id: undefined,
		auditRoleName: undefined,
		pageSize: 10,
		pageNo: 1,
		projectPhase: undefined
	})
	const queryFormRef = ref() // 搜索的表单
	const total = ref(0)
	/** 查询列表 */
	const getList = async () => {
		loading.value = true
		try {
			// const data = await AuditRoleApi.getAuditRoleList(queryParams)
			// list.value = data.list
			// total.value = data.total
			// list.value = handleTree(data, 'id', 'id')
			list.value = [{
				auditRoleCode: '测试',
				adder: '测试',
				createTime: '2024-09-20'
			}]
			total.value = list.value.length
		} finally {
			loading.value = false
		}
	}

	const detailRef = ref()
	const handleDone = (data : AuditRoleDetailVO) => {
		// detailRef.value.open(data)
		window.alert('去处理！');
	}
	/** 搜索按钮操作 */
	const handleQuery = () => {
		queryParams.pageNo = 1
		getList()
	}

	/** 重置按钮操作 */
	const resetQuery = () => {
		queryFormRef.value.resetFields()
		handleQuery()
	}

	/** 初始化 **/
	onMounted(() => {
		getList()
	})
</script>
<style scoped>
	.flex_center {
		display: flex;
		align-items: center;
		justify-content: center;
	}
</style>
