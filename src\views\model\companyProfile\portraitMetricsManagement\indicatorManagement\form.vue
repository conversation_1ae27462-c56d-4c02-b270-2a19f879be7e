<!--
* @Author: lijunliang
* @Date: 2024-09-16 16:31:32
* @Description: 整改通知表单=>
-->
<template>
  <Dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="75%"
    :scroll="true"
    maxHeight="72vh"
    :loading="loading"
  >
    <el-form ref="formRef" :model="queryParams" :rules="rules" label-width="110px">
      <el-button type="primary" plain @click="handleProfileAdd">新增</el-button>
      <el-table
        border
        v-loading="loading"
        :data="queryParams.list"
        :stripe="true"
        :show-overflow-tooltip="true"
      >
        <el-table-column label="序号" width="60" align="center">
          <template #default="{ $index }">{{ $index + 1 }}</template>
        </el-table-column>
        <el-table-column label="指标名称" align="center" min-width="120">
          <el-table-column label="标准系数" align="center" prop="indicatorId" min-width="120">
            <template #default="scope">
              <el-form-item
                :prop="'list.' + scope.$index + '.indicatorId'"
                label-width="0px"
                :rules="rules.indicatorId"
                style="margin-top: 18px"
              >
                <!-- @change="changeSelect(dict,index)" -->
                <el-select
                  v-model="scope.row.indicatorId"
                  v-for="dict in dictList"
                  :value-key="dict.id"
                  :key="dict.id"
                  placeholder="请选择"
                  style="width: 100%"
                  @change="scope.row.queryStatements = dict.queryStatements"
                >
                  <el-option :label="dict.indicatorName" :value="dict.id" />
                  <!-- <el-option label="00" value="0" />
                <el-option label="11" value="1" /> -->
                </el-select>
              </el-form-item>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="目标值" align="center" min-width="120">
          <el-table-column label="优秀" align="center" min-width="120">
            <el-table-column label="1" align="center" prop="evaluateGood" min-width="120">
              <template #default="scope">
                <el-form-item
                  :prop="'list.' + scope.$index + '.evaluateGood'"
                  label-width="0px"
                  :rules="rules.evaluate"
                  style="margin-top: 18px"
                >
                  <el-input v-model="scope.row.evaluateGood" placeholder="请输入目标值" />
                </el-form-item>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column label="良好" align="center" min-width="120">
            <el-table-column label="0.8" align="center" prop="evaluateBetter" min-width="120">
              <template #default="scope">
                <el-form-item
                  :prop="'list.' + scope.$index + '.evaluateBetter'"
                  label-width="0px"
                  :rules="rules.evaluate"
                  style="margin-top: 18px"
                >
                  <el-input v-model="scope.row.evaluateBetter" placeholder="请输入目标值" />
                </el-form-item>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column label="平均" align="center" min-width="120">
            <el-table-column label="0.6" align="center" prop="evaluateAverage" min-width="120">
              <template #default="scope">
                <el-form-item
                  :prop="'list.' + scope.$index + '.evaluateAverage'"
                  label-width="0px"
                  :rules="rules.evaluate"
                  style="margin-top: 18px"
                >
                  <el-input v-model="scope.row.evaluateAverage" placeholder="请输入目标值" />
                </el-form-item>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column label="较低" align="center" min-width="120">
            <el-table-column label="0.4" align="center" prop="evaluateLower" min-width="120">
              <template #default="scope">
                <el-form-item
                  :prop="'list.' + scope.$index + '.evaluateLower'"
                  label-width="0px"
                  :rules="rules.evaluate"
                  style="margin-top: 18px"
                >
                  <el-input v-model="scope.row.evaluateLower" placeholder="请输入目标值" />
                </el-form-item>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column label="较差" align="center" min-width="120">
            <el-table-column label="0.2" align="center" prop="evaluatePoor" min-width="120">
              <template #default="scope">
                <el-form-item
                  :prop="'list.' + scope.$index + '.evaluatePoor'"
                  label-width="0px"
                  :rules="rules.evaluate"
                  style="margin-top: 18px"
                >
                  <el-input v-model="scope.row.evaluatePoor" placeholder="请输入目标值" />
                </el-form-item>
              </template>
            </el-table-column>
          </el-table-column>
        </el-table-column>
        <el-table-column
          label="指标权重(合计必须是100)"
          align="center"
          prop="weight"
          min-width="160"
        >
          <template #default="scope">
            <el-form-item
              :prop="'list.' + scope.$index + '.weight'"
              label-width="0px"
              :rules="rules.weight"
              style="margin-top: 18px"
            >
              <el-input v-model="scope.row.weight" placeholder="请输入指标权重" />
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" :width="160" fixed="right">
          <template #default="scope">
            <el-button type="danger" link @click="handleProfileDelete(scope.$index)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="loading">保 存</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { PortraitManagementApi } from '@/api/model/companyProfile/portraitMetricsManagement/Portrait'
import { IndicatorManagementApi } from '@/api/model/companyProfile/portraitMetricsManagement/indicatorManagement'
import { List } from 'echarts';
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const dictList = ref([]) // 列表的数据
const dialogTitle = ref('') // 弹窗的标题
const dialogVisible = ref(false) // 弹窗的是否展示
const queryParams = ref({
  id: undefined,
  list: []
})
/** 查询列表 */
const open = async (id: any) => {
  resetForm()
  queryParams.value.id = id
  dialogVisible.value = true
  dialogTitle.value = '编辑'
  loading.value = true
  try {
    const data1 = await IndicatorManagementApi.getEvalStandards(id)
    const data = await PortraitManagementApi.PortraitManagementList({
      pageSize: 100,
      enabledStatus: 0,
      pageNo: 1
    })
    queryParams.value.list = data1
    dictList.value = data.list
  } finally {
    loading.value = false
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗
// 权重计算
const weightCalc = async (rule: any, value: any, callback: any) => {
  let num = 0
  queryParams.value.list.map((ele) => {
    num += Number(ele.weight || 0)
  })
  if (!value) {
    callback(new Error('指标权重不能为空'))
    return
  }
  if (num != 100) {
    callback(new Error('权重合计必须是100！'))
    message.error('权重合计必须是100！')
    return
  }
}
const formRef = ref()
const rules = reactive({
  weight: [{ validator: weightCalc, trigger: 'blur' }],
  indicatorId: [{ required: true, message: '标准系数不能为空', trigger: 'change' }],
  evaluate: [{ required: true, message: '目标值不能为空', trigger: 'blur' }]
})
const handleProfileAdd = () => {
  if (!queryParams.value.list) {
    queryParams.value.list = []
  }
  queryParams.value.list.push({})
}
// 保存
const submitForm = async () => {
  await formRef.value.validate()

  try {
    const data = {
      reqVOList:queryParams.value.list,
      id:queryParams.value.id
    }
    console.log(data);
    // return
    await IndicatorManagementApi.updateEvalStandards(data)
    message.success('修改成功')
  } finally {
    loading.value = false
  }
  console.log(queryParams.value.id, formRef.value)
}
const handleProfileDelete = async (index) => {
  // 删除的二次确认
  try {
    await message.delConfirm()
    queryParams.value.list.splice(index, 1)
    message.success(t('common.delSuccess'))
  } catch {}
}
/** 重置表单 */
const resetForm = () => {
  queryParams.value = {
    id: undefined,
    list: []
  }
  formRef.value?.resetFields()
}
</script>
