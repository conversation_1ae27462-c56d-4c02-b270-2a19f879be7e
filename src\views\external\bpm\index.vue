<template>
  <ContentWrap class="m-16px h-[calc(100%-34px)]">
    <el-row :gutter="10">
      <el-col :span="runningTasks.length>0?18:24">
        <el-tabs v-model="activeName" class="demo-tabs" lazy="false">
          <div class="inline_box">
            <el-tab-pane label="申请信息" name="0">
              <!-- 申请信息 -->
              <el-card v-loading="processInstanceLoading" class="box-card">
                <!-- <template #header>
                  <span class="el-icon-document">申请信息【{{ processInstance.name }}】</span>
                </template>-->

                <template v-if="taskPath">
                  <BusinessFormComponent
                    :saveAndSubmit="saveAndSubmit"
                    :id="processInstance.businessKey"
                    @handleWatchStatus="handleWatchStatus"
                    :formVariables="processInstance.formVariables"
                    :isApprove="isApprove"
                  />
                </template>
                <template v-else>
                  <!--               情况一：流程表单 -->
                  <el-col
                    v-if="processInstance?.processDefinition?.formType === 10"
                    :offset="6"
                    :span="16"
                  >
                    <form-create
                      v-model="detailForm.value"
                      v-model:api="fApi"
                      :option="detailForm.option"
                      :rule="detailForm.rule"
                    />
                  </el-col>
                  <!-- 情况二：业务表单 -->
                  <div v-if="processInstance?.processDefinition?.formType === 20">
                    <BusinessFormComponent
                      :id="processInstance.businessKey"
                      :saveAndSubmit="saveAndSubmit"
                      @handleWatchStatus="handleWatchStatus"
                      :formVariables="processInstance.formVariables"
                      :isApprove="isApprove"
                    />
                  </div>
                </template>
              </el-card>
            </el-tab-pane>
            <el-tab-pane label="审批记录" name="1">
              <!-- 审批记录 -->
              <ProcessInstanceTaskList
                :loading="tasksLoad"
                :process-instance="processInstance"
                :tasks="tasks"
                @refresh="getTaskList"
              />
            </el-tab-pane>
            <el-tab-pane label="流程图" name="2">
              <!-- 高亮流程图 -->
              <ProcessInstanceBpmnViewer
                :id="`${query.instanceId}`"
                v-if="activeName === '2'"
                :bpmn-xml="bpmnXml"
                :loading="processInstanceLoading"
                :process-instance="processInstance"
                :tasks="tasks"
              />
            </el-tab-pane>
          </div>
        </el-tabs>
      </el-col>
      <el-col :span="6" v-if="runningTasks.length>0">
        <!-- 审批信息 -->
        <el-card
          v-for="(item, index) in runningTasks"
          style="height: 100%;"
          :key="index"
          v-loading="processInstanceLoading"
          class="box-card h-93.2vh! is_margin"
        >
          <template #header>
            <div class="card-header">
              <template v-if="item.ext !=null && item.ext.nodeName">
                <span class="el-icon-picture-outline font-600 flex flex-col lh-20px">
                  <span class="color-[var(--el-color-primary)]">审批任务：</span>
                  {{ item.ext.nodeName }}
                </span>
              </template>
              <template v-else>
                <span class="el-icon-picture-outline font-600 flex flex-col lh-20px">
                  <span class="color-[var(--el-color-primary)]">审批任务：</span>
                  {{ item.name }}
                </span>
              </template>
              <!-- <el-button type="primary" size="small" @click="handleSave(item)" :disabled='businessStatus === 2'>提交</el-button> -->
            </div>
          </template>
          <!-- <el-col :offset="6" :span="16"> -->
          <el-form
            :ref="'form' + index"
            :model="auditForms[index]"
            :rules="auditRule"
            label-width="100px"
          >
            <!-- <el-form-item
              v-if="processInstance && processInstance.name"
              label="流程名"
              class="is_blue_col"
            >{{ processInstance.name }}</el-form-item>-->
            <el-form-item
              v-if="processInstance && processInstance.startUser"
              label="流程发起人"
              class="is_blue_col"
            >
              {{ processInstance?.startUser.nickname }}
              <el-tag size="small" type="info">{{ processInstance?.startUser.deptName }}</el-tag>
            </el-form-item>

            <!-- <el-card v-if="runningTasks[index].formId > 0" class="mb-15px !-mt-10px">
											<template #header>
												<span class="el-icon-picture-outline">
													填写表单【{{ runningTasks[index]?.formName }}】
												</span>
											</template>
											<form-create v-model="approveForms[index].value"
												v-model:api="approveFormFApis[index]" :option="approveForms[index].option"
												:rule="approveForms[index].rule" />
            </el-card>-->
            <el-form-item label="操作" class="is_blue_col">
              <el-radio-group
                v-model="radioControls"
                class="processRadio"
                @change="(e) => {
                radioChange(item,e,index)
              }"
              >
                <template v-if="item.ext !=null && item.ext.btnList !=null">
                  <template v-for="( one ,i ) in getStrDictOptions(DICT_TYPE.BPM_BUTTON_TYPE)">
                    <template v-if="item.ext.btnList.indexOf(parseInt(one.value))>=0">
                      <el-radio :value="one.value" :key="i" size="large">{{one.label}}</el-radio>
                    </template>
                  </template>
                </template>
                <template v-else>
                  <el-radio
                    v-for="( one ,i ) in getStrDictOptions(DICT_TYPE.BPM_BUTTON_TYPE)"
                    :value="one.value"
                    :key="i"
                    size="large"
                  >{{one.label}}</el-radio>
                </template>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              label="加签处理人"
              prop="signUserIds"
              class="is_blue_col"
              v-if="radioControls ==='5'"
            >
              <el-select
                v-model="auditForms[index].signUserIds"
                filterable
                multiple
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="one in item.taskNextResVO?.users ?? []"
                  :key="one.id"
                  :label="one.nickname+' ('+one.unitName+')'"
                  :value="one.id"
                />
              </el-select>
              <el-button
                link
                type="primary"
                class="font-size-12px!"
                @click="handleCheckMore(index)"
              >选择更多</el-button>
            </el-form-item>
            <el-form-item
              label="加签理由"
              prop="signReason"
              class="is_blue_col"
              v-if="radioControls ==='5'"
            >
              <el-input
                v-model="auditForms[index].signReason"
                clearable
                placeholder="请输入加签理由"
                type="textarea"
              />
            </el-form-item>
            <el-form-item
              label="退回节点"
              prop="targetTaskDefinitionKey"
              class="is_blue_col"
              v-if="radioControls ==='4'"
            >
              <el-select
                v-model="auditForms[index].targetTaskDefinitionKey"
                clearable
                no-data-text="当前没有可回退的节点"
                style="width: 100%"
              >
                <el-option
                  v-for="item in returnList"
                  :key="item.taskDefinitionKey"
                  :label="item.name"
                  :value="item.taskDefinitionKey"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="回退理由"
              prop="rejectReason"
              class="is_blue_col"
              v-if="radioControls ==='4'"
            >
              <el-input
                v-model="auditForms[index].rejectReason"
                clearable
                placeholder="请输入回退理由"
                type="textarea"
              />
            </el-form-item>
            <el-form-item
              label="新审批人"
              prop="assigneeUserId"
              class="is_blue_col"
              v-if="radioControls ==='2'"
            >
              <el-select
                v-model="auditForms[index].assigneeUserId"
                filterable
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="one in item.taskNextResVO?.users ?? []"
                  :key="one.id"
                  :label="one.nickname+' ('+one.unitName+')'"
                  :value="one.id"
                />
              </el-select>
              <el-button
                link
                type="primary"
                class="font-size-12px!"
                @click="handleCheckMore(index)"
              >选择更多</el-button>
            </el-form-item>
            <el-form-item
              label="转派理由"
              prop="transferReason"
              class="is_blue_col"
              v-if="radioControls ==='2'"
            >
              <el-input
                v-model="auditForms[index].transferReason"
                clearable
                placeholder="请输入转派理由"
                type="textarea"
              />
            </el-form-item>
            <el-form-item
              label="审批建议"
              prop="reason"
              class="is_blue_col"
              v-if="radioControls ==='0'"
            >
              <el-input v-model="auditForms[index].reason" placeholder="请输入审批建议" type="textarea" />
            </el-form-item>

            <!-- <el-form-item label="抄送人" prop="copyUserIds" class="is_blue_col" v-if="radioControls !='2'">
              <el-select
                v-model="auditForms[index].copyUserIds"
                filterable
                placeholder="请选择抄送人"
              >
                <el-option
                  v-for="item in userOptions"
                  :key="item.id"
                  :label="item.nickname"
                  :value="item.id"
                />
              </el-select>
            <el-button
              link
              type="primary"
              class="font-size-12px!"
              @click="handleCheckMore2(index)"
            >选择更多</el-button>
            </el-form-item>-->
            <template v-if=" radioControls == '0'">
              <template v-if="item.taskNextResVO">
                <el-divider />
                <el-form-item label="下一审批节点" class="is_blue_col">{{ item.taskNextResVO.name }}</el-form-item>
                <template v-if="item.taskNextResVO && item.taskNextResVO.users">
                  <el-form-item label="处理人" prop="nextUser" class="is_blue_col">
                    <el-select
                      v-model="auditForms[index].nextUser"
                      @change="selectUser(index, item.taskNextResVO)"
                      placeholder="请选择处理人"
                      filterable
                    >
                      <el-option
                        v-for="one in item.taskNextResVO?.users ?? []"
                        :key="one.id"
                        :label="one.nickname+' ('+one.unitName+')'"
                        :value="one.id"
                      />
                    </el-select>
                    <el-button
                      link
                      type="primary"
                      class="font-size-12px!"
                      @click="handleCheckMore(index)"
                    >选择更多</el-button>
                  </el-form-item>
                </template>
                <template v-else>
                  <el-form-item label="处理人" prop="nextUser" class="is_blue_col">
                    <el-select
                      v-model="auditForms[index].nextUser"
                      @change="selectUser(index, item.taskNextResVO)"
                      placeholder="请选择处理人"
                      filterable
                    >
                      <el-option
                        v-for="one in userOptions"
                        :key="one.id"
                        :label=" one.unitName !=null?one.nickname+' ('+one.unitName+')':one.nickname"
                        :value="one.id"
                      />
                    </el-select>
                  </el-form-item>
                </template>
              </template>

              <template v-if="item && item.taskCreateUserReqVO !=null">
                <el-divider />
                <el-form-item label="下一流程" class="is_blue_col">{{ item.ext.nextName }}</el-form-item>
                <el-text>审批节点： {{nextFlow.name}}</el-text>
                <template v-if="nextFlow.users">
                  <el-form-item label="处理人" prop="nextFlowUser" class="is_blue_col">
                    <el-select
                      v-model="auditForms[index].nextFlowUser"
                      @change="selectNextUser(index, nextFlow)"
                      placeholder="请选择处理人"
                      filterable
                    >
                      <el-option
                        v-for="one in nextFlow.users"
                        :key="one.id"
                        :label="one.nickname+' ('+one.unitName+')'"
                        :value="one.id"
                      />
                    </el-select>
                    <el-button
                      link
                      type="primary"
                      class="font-size-12px!"
                      @click="handleCheckMoreByNextUser(index)"
                    >选择更多</el-button>
                  </el-form-item>
                </template>
              </template>
            </template>
          </el-form>
          <div>
            <el-button
              type="primary"
              size="small"
              class="w-100%"
              @click="handleSave(item,index)"
              v-if="radioControls!=='5'"
            >提交</el-button>
            <div v-else style="display:flex">
              <el-button
                :disabled="processInstanceLoading"
                type="primary"
                class="w-50%"
                @click="submitForm('before',item)"
              >向前加签</el-button>
              <el-button
                :disabled="processInstanceLoading"
                type="primary"
                class="w-50%"
                @click="submitForm('after',item)"
              >向后加签</el-button>
            </div>
          </div>
          <!-- </el-col> -->
        </el-card>
      </el-col>
    </el-row>

    <!-- 弹窗：转派审批人 -->
    <TaskTransferForm ref="taskTransferFormRef" @success="getDetail" />
    <!-- 弹窗：回退节点 -->
    <TaskReturnForm ref="taskReturnFormRef" @success="getDetail" />
    <!-- 弹窗：委派，将任务委派给别人处理，处理完成后，会重新回到原审批人手中-->
    <TaskDelegateForm ref="taskDelegateForm" @success="getDetail" />
    <!-- 弹窗：加签，当前任务审批人为A，向前加签选了一个C，则需要C先审批，然后再是A审批，向后加签B，A审批完，需要B再审批完，才算完成这个任务节点 -->
    <TaskSignCreateForm ref="taskSignCreateFormRef" @success="getDetail" />

    <!--    选择更多-->
    <CheckOnePerson ref="checkPersonRef" @success="checkSuccess" />
  </ContentWrap>
</template>
<script lang="ts" setup>
import { setConfAndFields2 } from '@/utils/formCreate'
import type { ApiAttrs } from '@form-create/element-ui/types/config'
const { query } = useRoute() // 路由的查询
const { push } = useRouter()
const router = useRouter()
import { DICT_TYPE, getIntDictOptions, getStrDictOptions } from '@/utils/dict'
if (!query.external) {
  push('/external/oa/404')
}
let external = String(query.external)

import * as DefinitionApi from '@/api/external/definition'
import * as ProcessInstanceApi from '@/api/external/processInstance'
import * as TaskApi from '@/api/external/task'
import { CheckOnePerson } from '@/components/CheckOnePerson'
import { uniqBy } from 'lodash-es'

import ProcessInstanceBpmnViewer from '../../bpm/processInstance/detail/ProcessInstanceBpmnViewer.vue'
import ProcessInstanceTaskList from '../../bpm/processInstance/detail/ProcessInstanceTaskList.vue'
import TaskReturnForm from '../../bpm/processInstance/detail/dialog/TaskReturnForm.vue'
import TaskDelegateForm from '../../bpm/processInstance/detail/dialog/TaskDelegateForm.vue'
import TaskTransferForm from '../../bpm/processInstance/detail/dialog/TaskTransferForm.vue'
import TaskSignCreateForm from '../../bpm/processInstance/detail/dialog/TaskSignCreateForm.vue'
import { registerComponent } from '@/utils/routerHelper'
import { isEmpty } from '@/utils/is'
import * as UserApi from '@/api/external/user'
import { CommonApi } from '@/api/common'
defineOptions({ name: 'BpmProcessInstanceDetail' })

const dialogVisible = ref(false)
const checkPersonRef = ref()

let flow = ref(false)
const index = ref<Number>()
const handleCheckMore = (i: any) => {
  console.log(runningTasks, 'runningTasks', auditForms, 'auditForms')
  index.value = i
  flow.value = false
  checkPersonRef.value.open()
}
//下一流程发起
const handleCheckMoreByNextUser = (i: any) => {
  flow.value = true
  index.value = i
  checkPersonRef.value.open()
}
const checkSuccess = (row) => {
  console.log(row, auditForms.value)
  if (!flow.value) {
    let list = runningTasks.value[index.value].taskNextResVO.users
    runningTasks.value[index.value].taskNextResVO.users = list.filter(
      (item, index) => list.findIndex((i) => i.id === item.id) === index
    )
    // if (list.length !== runningTasks.value[index.value].taskNextResVO.users.length) {
    //   message.error('请勿重复选择')
    // }
    if (radioControls.value == '2') {
      auditForms.value[index.value].assigneeUserId = row.id
    } else if (radioControls.value == '5') {
      if (!auditForms.value[index.value].signUserIds) {
        auditForms.value[index.value].signUserIds = []
      }
      auditForms.value[index.value].signUserIds = [
        ...new Set([...auditForms.value[index.value].signUserIds, row.id])
      ]
    } else {
      auditForms.value[index.value].nextUser = row.id
    }
  } else {
    let filter = nextFlow.value.users.filter((item, index) => {
      return item.id === row.id
    })
    if (filter.length <= 0) {
      nextFlow.value.users.push(row)
    }
    if (radioControls.value == '2') {
      auditForms.value[index.value].assigneeUserId = row.id
    } else if (radioControls.value == '5') {
      if (!auditForms.value[index.value].signUserIds) {
        auditForms.value[index.value].signUserIds = []
      }
      auditForms.value[index.value].signUserIds = [
        ...new Set([...auditForms.value[index.value].signUserIds, row.id])
      ]
    } else {
      auditForms.value[index.value].nextFlowUser = row.id
    }
  }
}
const activeName = ref('0') // tab标签
const radioControls = ref('0') // 操作单选
const message = useMessage() // 消息弹窗
// 转办逻辑
const radioChange = async (item, row, index) => {
  console.log(row, item, 'radioControls')
  // if (row === '2') {
  //   openTaskUpdateAssigneeForm(item.id)
  // }
  if (row === '4') {
    await getRejectList(item.id)
  }
}
// 保存操作
const handleSave = async (item, index = 0) => {
  currentItem.value = item
  try {
    // 删除的二次确认
    await message.confirm('是否确认此操作')
    if (businessStatus.value === 2) {
      saveAndSubmit.value = true
      return
    }
    handleSwitch(currentItem.value, index)
  } catch {}
}

const nextFlow = ref({
  id: '',
  users: [],
  name: '',
  userTask: {},
  key: ''
})
const getNextUser = async (row) => {
  nextFlow.value = await CommonApi.getBeforeProcessPerson(row)
}

const selectNextUser = (index, item) => {
  auditForms.value[index].nextFlowKey = item.key
}

const handleSwitch = (item: any, index?: number) => {
  switch (radioControls.value) {
    case '0':
      handleAudit(item, true)
      break
    case '1':
      handleAudit(item, false)
      break
    case '2':
      // openTaskUpdateAssigneeForm(item.id)
      tastTransFerSuccess(item)
      break
    case '3':
      handleDelegate(item)
      break
    case '4':
      // handleBack(item)
      taskReturnSuccess(item)
      break
    case '5':
      handleSign(item)
      break
    default:
      //这里是没有找到对应的值处理
      break
  }
}
const taskReturnSuccess = async (item) => {
  const index = runningTasks.value.indexOf(item)
  const auditFormRef = proxy.$refs['form' + index][0]
  console.log(auditFormRef)

  const elForm = unref(auditFormRef)
  if (!elForm) return
  const valid = await elForm.validate()
  if (!valid) return
  try {
    processInstanceLoading.value = true
    let data = {
      id: item.id,
      targetTaskDefinitionKey: auditForms.value[index].targetTaskDefinitionKey,
      reason: auditForms.value[index].rejectReason
    }
    await TaskApi.returnTask(data, external)
  } catch (error) {
  } finally {
    processInstanceLoading.value = false
  }
  emits('approvalSuccess', businessStatus.value)
  await getDetail(external)
}
const { proxy } = getCurrentInstance() as any

const taskSet = ref({})
const userId = ref(0) // 当前登录的编号
const UserData = ref<UserApi.UserVO>()
const id = query.instanceId as unknown as string // 流程实例的编号
const processInstanceLoading = ref(false) // 流程实例的加载中
const processInstance = ref<any>({}) // 流程实例
const bpmnXml = ref('') // BPMN XML
const tasksLoad = ref(true) // 任务的加载中
const tasks = ref<any[]>([]) // 任务列表
// ========== 审批信息 ==========
const runningTasks = ref<any[]>([]) // 运行中的任务
//表单类型
const formType = ref(10)

const taskPath = ref('') //节点组件地址
//是否审核通过
const isApprove = ref(false)
//扩展信息

const auditForms = ref<any[]>([]) // 审批任务的表单
const auditRule = reactive({
  reason: [{ required: true, message: '审批建议不能为空', trigger: 'blur' }],
  nextUser: [{ required: true, message: '处理人不能为空', trigger: 'change' }],
  nextFlowUser: [{ required: true, message: '下一流程处理人不能为空', trigger: 'change' }],
  transferReason: [{ required: true, message: '转派理由不能为空', trigger: 'blur' }],
  assigneeUserId: [{ required: true, message: '新审批人不能为空', trigger: 'change' }]
})
const approveForms = ref<any[]>([]) // 审批通过时，额外的补充信息
const approveFormFApis = ref<ApiAttrs[]>([]) // approveForms 的 fAPi
// ========== 申请信息 ==========
const fApi = ref<ApiAttrs>() //
const detailForm = ref({
  rule: [],
  option: {},
  value: {}
}) // 流程实例的表单详情

/** 监听 approveFormFApis，实现它对应的 form-create 初始化后，隐藏掉对应的表单提交按钮 */
watch(
  () => approveFormFApis.value,
  (value) => {
    value?.forEach((api) => {
      api.btn.show(false)
      api.resetBtn.show(false)
    })
  },
  {
    deep: true
  }
)
/** 处理审批通过和不通过的操作 */
const handleAudit = async (task, pass) => {
  // 1.1 获得对应表单
  const index = runningTasks.value.indexOf(task)
  const auditFormRef = proxy.$refs['form' + index][0]
  // 1.2 校验表单
  const elForm = unref(auditFormRef)
  if (!elForm) return
  const valid = await elForm.validate()
  if (!valid) return

  // 2.1 提交审批
  const data = {
    id: task.id,
    reason: auditForms.value[index].reason,
    copyUserIds: auditForms.value[index].copyUserIds,
    nextUser: auditForms.value[index].nextUser,
    nextTaskId: auditForms.value[index].nextTaskId,
    nextFlowUser: auditForms.value[index].nextFlowUser,
    nextFlowKey: auditForms.value[index].nextFlowKey
  }
  if (pass) {
    // 审批通过，并且有额外的 approveForm 表单，需要校验 + 拼接到 data 表单里提交
    const formCreateApi = approveFormFApis.value[index]
    if (formCreateApi) {
      await formCreateApi.validate()
      data.variables = approveForms.value[index].value
    }
    processInstanceLoading.value = true
    await TaskApi.approveTask(data, external)
    message.success('审批通过成功')
    processInstanceLoading.value = false
  } else {
    processInstanceLoading.value = true
    await TaskApi.rejectTask(data, external)
    message.success('审批不通过成功')
    processInstanceLoading.value = false
  }
  // 2.2 加载最新数据
  getDetail(external)
}

/** 转派审批人 */
const taskTransferFormRef = ref()
const openTaskUpdateAssigneeForm = (id: string) => {
  taskTransferFormRef.value.open(id)
}

/** 处理审批退回的操作 */
const taskDelegateForm = ref()
const handleDelegate = async (task) => {
  taskDelegateForm.value.open(task.id)
}

/** 处理审批退回的操作 */
const taskReturnFormRef = ref()
const handleBack = async (task: any) => {
  taskReturnFormRef.value.open(task.id)
}

/** 处理审批加签的操作 */
const taskSignCreateFormRef = ref()
const handleSign = async (task: any) => {
  taskSignCreateFormRef.value.open(task.id)
}

/** 获得详情 */
const getDetail = (token) => {
  // 1. 获得流程实例相关
  getProcessInstance(token)
}
//获取用户信息
const getUser = async () => {
  let externalUser = await UserApi.getExternalUser(external)
  if (externalUser) {
    userId.value = externalUser.id
    UserData.value = externalUser
  } else {
    push('/external/oa/404')
  }
}
const emits = defineEmits(['approvalSuccess'])
/** 加载流程实例 */
const BusinessFormComponent = ref(null) // 异步组件
const businessStatus = ref(1) //2: 为业务表单需要编辑，为2时右侧提交停用,其他数字为不禁用
const saveAndSubmit = ref(false)
const currentItem = ref()
const handleWatchStatus = (status: number) => {
  businessStatus.value = status
  if (businessStatus.value === 1 && saveAndSubmit.value) {
    saveAndSubmit.value = false
    handleSwitch(currentItem.value)
  } else {
    saveAndSubmit.value = false
  }
  emits('approvalSuccess', status)
}
const getProcessInstance = async (token) => {
  try {
    processInstanceLoading.value = true
    const data = await ProcessInstanceApi.getProcessInstance(id, token)
    if (!data) {
      message.error('查询不到流程信息！')
      return
    }
    processInstance.value = data

    // 设置表单信息
    const processDefinition = data.processDefinition
    formType.value = processDefinition.formType
    if (processDefinition.formType === 10) {
      setConfAndFields2(
        detailForm,
        processDefinition.formConf,
        processDefinition.formFields,
        data.formVariables
      )
      nextTick().then(() => {
        fApi.value?.btn.show(false)
        fApi.value?.resetBtn.show(false)
        fApi.value?.disabled(true)
      })
    } else {
      // 注意：data.processDefinition.formCustomViewPath 是组件的全路径，例如说：/crm/contract/detail/index.vue
      // BusinessFormComponent.value = registerComponent(processInstance.value.processDefinition.formCustomViewPath)
    }

    // 加载流程图
    bpmnXml.value = (
      await DefinitionApi.getProcessDefinition(processDefinition.id as number, '', external)
    )?.bpmnXml
    // 2. 获得流程任务列表（审批记录）
    await getTaskList()

    if (taskSet.value.taskCreateUserReqVO) {
      await getNextUser(taskSet.value.taskCreateUserReqVO)
    }
  } finally {
    processInstanceLoading.value = false
  }
}
//组件地址
const taskFormView = ref('')
const setPath = (task) => {
  nextTick(async () => {
    if (task.path != null && task.path != '') {
      taskFormView.value = task.path
      BusinessFormComponent.value = await registerComponent(task.path)
    } else {
      taskFormView.value = processInstance.value.processDefinition.formCustomViewPath
      BusinessFormComponent.value = await registerComponent(
        processInstance.value.processDefinition.formCustomViewPath
      )
    }
  })
}
/** 加载任务列表 */
const getTaskList = async () => {
  runningTasks.value = []
  auditForms.value = []
  approveForms.value = []
  approveFormFApis.value = []
  try {
    // 获得未取消的任务
    tasksLoad.value = true
    const data = await TaskApi.getTaskListByProcessInstanceId(id, external)
    tasks.value = []
    // 1.1 移除已取消的审批
    data.forEach(async (task) => {
      //如果通过参数获取，则不在设置下面，否则设置下面
      if (task.id === query.taskId) {
        taskSet.value = task
        isApprove.value = task.status === 1 || task.status === 6
        setPath(task)
        console.log(' task isApprove.value', isApprove.value)
      } else {
        if (task.children) {
          task.children.forEach(async (child) => {
            if (child.id === query.taskId) {
              taskSet.value = child
              isApprove.value = child.status === 1 || child.status === 6
              setPath(task)
              console.log(' child isApprove.value', isApprove.value)
            }
          })
        }
      }
      if (task.status !== 4) {
        tasks.value.push(task)
      }
      let childrenAssinnee = []
      if (task.children) {
        task.children.forEach(async (child) => {
          let childUserId = child.assigneeUser.id
          childrenAssinnee.push(childUserId)
        })
      }
      //当前人
      let currentUserId = task?.assigneeUser?.id ?? ''
      //发布人
      let startUserId = task?.processInstance?.startUser?.id ?? ''
      if (query.taskId === undefined) {
        if (userId.value === currentUserId || startUserId === userId.value) {
          taskPath.value = task.path
          taskSet.value = task
          await setPath(task)
        } else {
          if (childrenAssinnee.length > 0 && childrenAssinnee.indexOf(userId) >= 0) {
            await setPath(task)
            taskSet.value = task
          }
        }
      }
    })
    // 1.2 排序，将未完成的排在前面，已完成的排在后面；
    // tasks.value.sort((a, b) => {
    //   // 有已完成的情况，按照完成时间倒序
    //   if (a.endTime && b.endTime) {
    //     return b.endTime - a.endTime
    //   } else if (a.endTime) {
    //     return 1
    //   } else if (b.endTime) {
    //     return -1
    //     // 都是未完成，按照创建时间倒序
    //   } else {
    //     return b.createTime - a.createTime
    //   }
    // })
    // 获得需要自己审批的任务
    loadRunningTask(tasks.value)
  } finally {
    tasksLoad.value = false
  }
}

const selectUser = (index, item) => {
  auditForms.value[index].nextTaskId = item.id
}

/**
 * 设置 runningTasks 中的任务
 */
const loadRunningTask = (tasks) => {
  tasks.forEach((task) => {
    if (!isEmpty(task.children)) {
      loadRunningTask(task.children)
    }

    isApprove.value =
      (task.status === 1 || task.status === 6) &&
      task.assigneeUser != null &&
      task.assigneeUser != '' &&
      task.assigneeUser.id === userId.value

    // 2.1 只有待处理才需要
    if (task.status !== 1 && task.status !== 6) {
      return
    }

    // 2.2 自己不是处理人
    if (!task.assigneeUser || task.assigneeUser.id !== userId.value) {
      return
    }

    let nextUser = undefined
    if (
      task.taskNextResVO != null &&
      task.taskNextResVO.users != null &&
      task.taskNextResVO.users.length == 1
    ) {
      nextUser = task.taskNextResVO.users[0].id
    }

    // 2.3 添加到处理任务
    if (!query.taskId) {
      runningTasks.value.push({ ...task })
    }
    if (query.taskId == task.id) {
      runningTasks.value = [task]
    }
    auditForms.value.push({
      reason: '同意',
      copyUserIds: [],
      nextUser: nextUser
    })
    // 2.4 处理 approve 表单
    if (task.formId && task.formConf) {
      const approveForm = {}
      setConfAndFields2(approveForm, task.formConf, task.formFields, task.formVariables)
      approveForms.value.push(approveForm)
    } else {
      approveForms.value.push({}) // 占位，避免为空
    }
  })
}
// 转办
const tastTransFerSuccess = async (item) => {
  const index = runningTasks.value.indexOf(item)
  const auditFormRef = proxy.$refs['form' + index][0]
  const elForm = unref(auditFormRef)
  if (!elForm) return
  const valid = await elForm.validate()
  if (!valid) return
  try {
    processInstanceLoading.value = true
    let data = {
      id: item.id,
      assigneeUserId: auditForms.value[index].assigneeUserId,
      reason: auditForms.value[index].transferReason
    }
    await TaskApi.transferTask(data, external)
  } catch (error) {
  } finally {
    processInstanceLoading.value = false
  }
  await emits('approvalSuccess', businessStatus.value)
  await getDetail(external)
}
const submitForm = async (type: string, item: any) => {
  await message.confirm('是否确认此操作')
  const index = runningTasks.value.indexOf(item)
  const auditFormRef = proxy.$refs['form' + index][0]
  console.log(auditFormRef)

  const elForm = unref(auditFormRef)
  if (!elForm) return
  const valid = await elForm.validate()
  if (!valid) return
  // 提交请求
  processInstanceLoading.value = true
  let data = {
    id: item.id,
    userIds: auditForms.value[index].signUserIds,
    type: type,
    reason: auditForms.value[index].signReason,
    path: taskFormView.value
  }
  try {
    await TaskApi.signCreateTask(data, external)
    await message.success('加签成功')
  } finally {
    processInstanceLoading.value = false
  }
  await emits('approvalSuccess', businessStatus.value)
  await getDetail(external)
}
const returnList = ref([] as any)
const getRejectList = async (id: string) => {
  returnList.value = await TaskApi.getTaskListByReturn(id, external)
}
/** 初始化 */
const userOptions = ref<UserApi.UserVO[]>([]) // 用户列表
onMounted(async () => {
  getUser()
  getDetail(external)
  // 获得用户列表
  // userOptions.value = await UserApi.getSimpleUserList(external)
})
</script>
<style scoped lang='scss'>
.inline_box {
  height: 100vh;
  overflow: auto;
  padding-bottom: 100px;
  scrollbar-width: none;
  -ms-overflow-style: none;
}
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.is_margin > :deep(.el-card__header) {
  padding: calc(var(--el-card-padding) - 4px) 0;
  margin: 0 var(--el-card-padding);
}
.is_blue_col {
  margin-bottom: 0;
  flex-direction: column;
  :deep(.el-form-item__label) {
    display: block;
    border-left: 5px solid var(--el-color-primary);
    padding: 0 5px;
    height: 14px;
    line-height: 14px;
    margin: 9px 0;
    font-weight: 600;
    width: 120px !important;
  }
}
</style>
