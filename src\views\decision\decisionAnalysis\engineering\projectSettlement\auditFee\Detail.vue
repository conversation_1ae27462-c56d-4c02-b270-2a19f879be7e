<!--
* @Author: lijunliang
* @Date: 2024-10-23 10:30:58
* @Description: 单位详情列表=>
-->
<template>
	<Dialog v-model="dialogVisible" :loading="loading" :scroll="true" title="详情列表" width="75%">
		<el-button :loading="exportLoading" plain @click="handleExport">
			<Icon class="mr-5px" icon="ep:download" />
			导出
		</el-button>
		<el-table ref="tableRef" border :data="detailData" :stripe="true" :show-overflow-tooltip="true" :span-method="arraySpanMethod" show-summary :summary-method="getSummaries">
			<el-table-column label="序号" type="index" width="60" align="center"/>
			<el-table-column label="工程名称" align="left" prop="projectName" min-width="260"/>
			<el-table-column label="工程概况" align="left" prop="projectOverview" min-width="240"/>
			<el-table-column :label="imparity" :prop="imparityData" align="left" min-width="260" />
			<el-table-column label="施工单位" align="left" prop="constructionCompany" min-width="260" />
			<el-table-column label="审计单位" align="left" prop="auditUnit" min-width="260" />
			<el-table-column label="施工单位报审值(万元)" align="center" prop="constructionCompanyAudit" min-width="180" />
			<el-table-column label="管理单位(代建单位）初审值(万元)" align="center" prop="managementUnitFirstAudit" min-width="280" />
			<el-table-column label="管理单位(代建单位）审减值(万元)" align="center" prop="managementAuditReductionValue" min-width="280" />
			<el-table-column label="单项结算审定值(万元)" align="center" prop="singleItemSettlementAudit" min-width="180" />
			<el-table-column label="单项审减值(万元)" align="center" prop="singleItemAuditReduction" min-width="150" />
			<el-table-column label="审减率" align="center" prop="auditReductionRate" min-width="150">
				<template #default="scope">
					{{scope.row.auditReductionRate ?scope.row.auditReductionRate + '%' : '0%'}}
				</template>
			</el-table-column>
			<el-table-column label="甲方应付审计费(元)" align="center" prop="partyAAuditFee" min-width="180" />
			<el-table-column label="乙方应付审计费(元)" align="center" prop="partyBAuditFee" min-width="180" />
			<el-table-column label="备注" align="left" prop="settlementAuditMainIssues" min-width="180" />
		</el-table>
		<!-- 分页 -->
		<Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
			@pagination="getList" />
	</Dialog>
</template>

<script lang="ts" setup>
import { AuditFeeDetailsVO, AuditFeeApi } from '@/api/decision/engineering/projectSettlement/auditFee'
import download from '@/utils/download'
import { h } from 'vue'
import type { VNode } from 'vue'
import type { TableColumnCtx } from 'element-plus'
import { mergeCells } from '@/utils/mergeCells'
defineOptions({ name: 'Detail' })

const queryParams = reactive({
	pageSize: 10,
	pageNo: 1,
	id: undefined,
	fessType: undefined,
})
const imparity = ref('')
const imparityData = ref('')
const dialogVisible = ref(false) // 弹窗的是否展示
const loading = ref(true) // 列表的加载中
const detailData = ref<AuditFeeDetailsVO[]>([]) // 详情数据
const total = ref(0)
const message = useMessage() // 消息弹窗

/** 打开弹窗 */
const open = async (id?: number, type: any) => {
	console.log(type)
	if(type == 1){
		imparity.value = '建设单位'
		imparityData.value = 'constructionUnit'
	}else if(type == 2){
		imparity.value = '管理单位'
		imparityData.value = 'managementUnit'
	}else if( type == 3 ){
		imparity.value = '投资单位'
		imparityData.value = 'investmentUnit'
	}else if(type == 4){
		imparity.value = '建设单位'
		imparityData.value = 'constructionUnit'
	}
	queryParams.id = id
	queryParams.fessType = type
	dialogVisible.value = true
	// 设置数据
	await getList()
	await getTotal()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

// 数据详情列表
const getList = async() => {
	// 设置数据
	loading.value = true
	try {
		const data = await AuditFeeApi.getAuditFeeDetails(queryParams)
		detailData.value = data.list
		total.value = data.total
	} finally {
		loading.value = false
	}
}

// 查询合计数据
const dataTotal = ref()
const getTotal = async () => {
	loading.value = true
	try {
		dataTotal.value = await AuditFeeApi.getAuditFeeDetailsTotal(queryParams)
	} finally {
		loading.value = false
	}
}

// 合并表尾合计行单元格的方法
const tableRef = ref()
const arraySpanMethod = ({ row, column, rowIndex, columnIndex }:any)=> {
	mergeCells(tableRef, 1, 5)
}

interface SummaryMethodProps<T = AuditFeeDetailsVO> {
  columns: TableColumnCtx<T>[]
  data: T[]
}
// 表尾合计显示
const getSummaries = (param: SummaryMethodProps) => {
  const { columns, data } = param
  const sums: (string | VNode)[] = []
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = h('div', { style: { textDecoration: 'underline' } }, [
        '',
      ])
      return
    }
    const values = data.map((item) => Number(item[column.property]))
    if (!values.every((value) => Number.isNaN(value)) &&  dataTotal.value) {
	  sums[6] = dataTotal.value.constructionCompanyAuditCount
	  sums[7] = dataTotal.value.managementFirstAuditCount
	  sums[8] = dataTotal.value. managementUnitReduction
	  sums[9] = dataTotal.value.singleItemSettlementCount
	  sums[10] = dataTotal.value.singleItemReductionCount
	  sums[11] = dataTotal.value.auditReductionRateCount?(dataTotal.value.auditReductionRateCount + '%'):"0%"
	  sums[12] = dataTotal.value.partyAAuditFeeCount
	  sums[13] = dataTotal.value.partyBAuditFeeCount
    } else {
      sums[1] = '合计:'
    }
  })
  return sums
}

/** 导出按钮操作 */
const exportLoading = ref(false) // 导出的加载中
const handleExport = async () => {
	try {
		// 导出的二次确认
		await message.exportConfirm()
		// 发起导出
		exportLoading.value = true
		const data = await AuditFeeApi.exportAuditFeeDetails(queryParams)
		download.excel(data, '一审单位详情.xls')
	} catch {
	} finally {
		exportLoading.value = false
	}
}
</script>
