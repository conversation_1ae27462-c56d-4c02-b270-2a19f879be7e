<!--
* @Author: lijunliang
* @Date: 2024-09-11 17:22:50
* @Description: 审计事项库=>
-->
<template>
	<el-row :gutter="16">
		<el-col :span="5" :xs="24">
			<ContentWrap class="h-1/1">
				<!-- <el-tabs v-model="activeName" class="demo-tabs" @tab-change="handleClick">
					<el-tab-pane label="审计事项" name="1" />
					<el-tab-pane label="审计类型" name="2" />
				</el-tabs> -->
				<div class="flex justify-left">
					<el-radio-group v-model="tabPosition" style="margin-bottom: 15px">
						<el-radio-button value="1" @click="handleRadio(1)">隐藏停用</el-radio-button>
						<el-radio-button value="2" @click="handleRadio(2)">显示停用</el-radio-button>
					</el-radio-group>
				</div>
				<JobmangerLeftTreeDraggable ref="JobmangerLeftTreeRef" @node-click="handleDeptNodeClick" :createUrl="createUrl" :editUrl="editUrl"
					:searchUrl="searchUrl" :delUrl="delUrl" :detailsUrl="detailsUrl" :type='4' :showBut='true'/>
			</ContentWrap>
		</el-col>
		<el-col :span="19" :xs="24">
			<ContentWrap class="common-card-search">
				<!-- 搜索工作栏 -->
				<el-form class="-mb-15px common-search-form" :model="queryParams" ref="queryFormRef" :inline="true" label-width="110px">
					<el-form-item label="风险名称" prop="matterName">
						<el-input v-model="queryParams.matterName" placeholder="请输入风险名称" clearable
							@keyup.enter="handleQuery" class="!w-200px" />
					</el-form-item>
					<el-form-item label="事项描述" prop="mattersDesc">
						<el-input v-model="queryParams.mattersDesc" placeholder="请输入事项描述" clearable
							@keyup.enter="handleQuery" class="!w-200px" />
					</el-form-item>
					<el-form-item label="控制措施" prop="controlMeasures">
						<el-input v-model="queryParams.controlMeasures" placeholder="请输入控制措施" clearable
							@keyup.enter="handleQuery" class="!w-200px" />
					</el-form-item>
					<el-form-item label="审计步骤及方法" prop="stepMethod">
						<el-input v-model="queryParams.stepMethod" placeholder="请输入审计步骤及方法" clearable
							@keyup.enter="handleQuery" class="!w-200px" />
					</el-form-item>
					<el-form-item label="主要问题表现" prop="mainQues">
						<el-input v-model="queryParams.mainQues" placeholder="请输入主要问题表现" clearable
							@keyup.enter="handleQuery" class="!w-200px" />
					</el-form-item>
					<el-form-item label="审计建议" prop="auditSugg">
						<el-input v-model="queryParams.auditSugg" placeholder="请输入审计建议" clearable
							@keyup.enter="handleQuery" class="!w-200px" />
					</el-form-item>
					<el-form-item label="审计依据" prop="auditAccord">
						<el-input v-model="queryParams.auditAccord" placeholder="请输入审计依据" clearable
							@keyup.enter="handleQuery" class="!w-200px" />
					</el-form-item>
				</el-form>
		<div class="right-search-btn">
			<el-button  type="primary"  @click="handleQuery">
					<Icon icon="ep:search" class="mr-5px" />搜索
				</el-button>
				<el-button @click="resetQuery">
					<Icon icon="ep:refresh" class="mr-5px" />重置
				</el-button>
		</div>
			</ContentWrap>
			<!-- 列表 -->
			<ContentWrap>

    <div class="button_margin15">
      <el-button v-hasPermi="['audit:tank-matters-info:create']" v-show="activeName==='1'" type="primary" plain
        @click="openForm('create',queryParams.matterId)">
        <Icon icon="ep:plus" class="mr-5px" />新增风险点
      </el-button>
      <el-button v-show="activeName==='2'" type="primary" plain @click="addForm">
        <Icon icon="ep:plus" class="mr-5px" />新增
      </el-button>
      <el-button v-hasPermi="['audit:tank-matters-info:export']" :loading="exportLoading" plain
        @click="handleExport">
        <Icon class="mr-5px" icon="ep:download" />
        导出
      </el-button>
	  <el-button v-hasPermi="['audit:tank-matters-info:template']" :loading="templateLoading" plain
        @click="handleExportTemplate">
        <Icon class="mr-5px" icon="ep:download" />
        导入模板下载
      </el-button>
      <el-button v-hasPermi="['audit:tank-matters-info:import']" type="primary" plain @click="handleDataImport">导入</el-button>
    </div>
				<el-table v-loading="loading" :data="list" border :stripe="true" :show-overflow-tooltip="true">
					<el-table-column label="序号" width="60" align="center">
						<template
							#default="{ $index }">{{ (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1 }}</template>
					</el-table-column>
					<el-table-column label="风险名称" align="left" prop="matterName" min-width="260" />
					<!-- <el-table-column label="事项编码" align="left" prop="matterCode" min-width="180"/> -->
					<!-- <el-table-column label="事项类型" align="center" prop="matterType" min-width="100">
						<template #default="scope">
							<dict-tag :type="'tank_matters_type'" :value="scope.row.matterType" />
						</template>
					</el-table-column> -->
					<el-table-column label="风险等级" align="center" prop="riskLevelName"  min-width="100"/>
					<el-table-column label="所属事项" align="center" prop="mattersName" min-width="120" />
					<el-table-column label="事项描述" align="left" prop="mattersDesc" min-width="180" />
						<!-- <template #default="scope">
							<span v-html="scope.row.mattersDesc"></span>
						</template>
					</el-table-column> -->
					<el-table-column label="控制措施" align="left" prop="controlMeasures" min-width="180" />
					<el-table-column label="审计步骤及方法" align="left" prop="stepMethod" min-width="180" />
					<el-table-column label="审计表单" align="left" prop="auditForm" min-width="180" />
					<el-table-column label="主要问题表现" align="left" prop="mainQues" min-width="180" />
					<el-table-column label="审计建议" align="left" prop="auditSugg" min-width="180" />
					<el-table-column label="审计依据" align="left" prop="auditAccord" min-width="180" />
					<el-table-column label="操作" align="center" fixed="right" width="250">
						<template #default="scope">
							<el-button v-hasPermi="['audit:tank-matters-info:get']" link type="primary" @click="openDetailForm(scope.row.id, scope.row.matterId)" v-show="scope.row.status == 1">查看</el-button>
							<el-button v-hasPermi="['audit:tank-matters-info:update']" link type="primary" v-show="activeName == '1'&&scope.row.status == 2"
								@click="openForm('update', queryParams.matterId,scope.row.id)">编辑</el-button>
							<el-button v-hasPermi="['audit:tank-matters-info:on-off']" link type="primary" v-show="scope.row.status == 2"
								@click="handleStatus(scope.row,1)">启用</el-button>
							<el-button v-hasPermi="['audit:tank-matters-info:on-off']" link type="danger" v-show="scope.row.status == 1"
								@click="handleStatus(scope.row,2)">停用</el-button>
							<el-button v-hasPermi="['audit:tank-matters-info:delete']" link type="danger" v-show="scope.row.status == 2 || activeName == '2'" @click="handleDelete(scope.row.id)">删除</el-button>
						</template>
					</el-table-column>
				</el-table>
				<!-- 分页 -->
				<Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
					@pagination="getList" />
			</ContentWrap>
		</el-col>
	</el-row>
	<!-- 表单弹窗：添加/修改 -->
	<Form ref="formRef" @success="dataList" />
	<!-- 查看详情弹窗 -->
	<Detail ref="detailRef" />
  <!-- 导入 -->
  <ImportForm ref="importFormRef" @success="handleData" />

	<Disposition ref="DispositionRef" @success="dataList" />
</template>

<script setup lang="ts">
	import { getIntDictOptions } from '@/utils/dict'
	import download from '@/utils/download'
  import ImportForm from './ImportForm.vue'
	import { ItemLibraryApi, ItemLibraryVO, ItemLibraryDetailVO } from '@/api/jobmanger/itemLibrary'
	import Form from './form.vue'
	import Detail from './Detail.vue'
	import Disposition from './disposition.vue'
	import JobmangerLeftTreeDraggable from '@/views/jobmanger/itemLibrary/components/JobmangerLeftTreeDraggable.vue'
	import { TimeSplicing } from '@/utils/formatTime'
	defineOptions({ name: 'ItemLibrary‌‌' })

	const message = useMessage() // 消息弹窗
	const { t } = useI18n() // 国际化
	const { query } = useRoute() //接收路由传参
	const loading = ref(true) // 列表的加载中
	const list = ref<ItemLibraryVO[]>([]) // 列表的数据
	const queryParams = reactive({
		id: undefined,
		pageSize: 10,
		pageNo: 1,
		matterId: undefined,
		// matterType: 0,
		matterType: undefined,
		matterTypeId: undefined,
		matterName: undefined,
		matterCode: undefined,
		mattersDesc: undefined,
		controlMeasures: undefined,
		stepMethod: undefined,
		auditForm: undefined,
		mainQues: undefined,
		auditSugg: undefined,
		auditAccord: undefined,
		status: undefined,
	})
	const queryFormRef = ref() // 搜索的表单
	const exportLoading = ref(false) // 导出的加载中
	const templateLoading = ref(false) // 导入模板下载
	const total = ref(0)
	const tabPosition = ref('1')
	const status = ref(1)

	// 事项树接口
	const JobmangerLeftTreeRef = ref()
	var searchUrl = ref('/audit/tank-trees-type/get?type=4')
	const createUrl = '/audit/tank-trees-type/create'
	const editUrl = '/audit/tank-trees-type/update'
	const delUrl = '/audit/tank-trees-type/delete'
	const detailsUrl = '/audit/tank-trees-type/get-tree'
	const activeName = ref('1')
	const handleClick = async (type) => {
		console.log(type);
		// activeName.value = type
		if (activeName.value == '1') {
			await (searchUrl.value = '/audit/tank-trees-type/get?type=4')
			JobmangerLeftTreeRef.value.getTree()
		} else {
			// await (searchUrl.value = '/system/audit-type/getAll')
			await (searchUrl.value = '/system/audit-type/getAllFirst')
			JobmangerLeftTreeRef.value.getTree()
		}
		queryParams.matterId = undefined
		// queryParams.matterType = 0
		queryParams.matterTypeId = undefined
		await getList()
	}

	// 显示/隐藏审计事项
	const handleRadio = async (type) => {
		status.value = type
		if(type == 1){
			await (searchUrl.value = `/audit/tank-trees-type/get?type=4&status=${type}`)
			JobmangerLeftTreeRef.value.getTree()
		}else if(type == 2){
			await (searchUrl.value = `/audit/tank-trees-type/get?type=4&status=${type}`)
			JobmangerLeftTreeRef.value.getTree()
		}
	}

	/** 查询列表 */
	const getList = async () => {
		loading.value = true
		try {
			let data = {}
			if (activeName.value == '1') {
				data = await ItemLibraryApi.getItemLibraryList(queryParams)
			} else {
				data = await ItemLibraryApi.getItemLibraryTypeList(queryParams)
			}
			list.value = data.list
			total.value = data.total
		} finally {
			loading.value = false
		}
	}

	// 刷新树结构和列表
	const dataList = async() =>{
		await getList()
	}

	const detailRef = ref()
	// const openDetailForm = (data : ItemLibraryDetailVO) => {
	// 	detailRef.value.open(activeName.value, data)
	// }
	const openDetailForm = (id:number, matterId:number) => {
		detailRef.value.open(activeName.value, id, matterId)
	}

	/** 搜索按钮操作 */
	const handleQuery = () => {
		queryParams.pageNo = 1
		getList()
	}

	/** 重置按钮操作 */
	const resetQuery = () => {
		queryFormRef.value.resetFields()
		handleQuery()
	}

	/** 处理部门被点击 */
	const handleDeptNodeClick = async (row) => {
		queryParams.matterId = row.id
		queryParams.matterTypeId = row.id
		await getList()
	}

	/** 添加/修改操作 */
	const formRef = ref()
	const openForm = (type : string, matterId : number, id ?: number) => {
		formRef.value.open(type, matterId, id)
	}
	// 启用/作废
	const handleStatus = async (row : ItemLibraryVO, statu : number) => {
		try {
			// 修改状态的二次确认-草稿0/启用1/停用2
			const text = row.status === 2 ? '启用' : '停用'
			await message.confirm('请确认' + text + '"' + row.matterName + '"，如果您不想'+ text +'此数据，请点击“取消”')
			// 发起修改状态
			console.log('111')
			await ItemLibraryApi.changeItemLibraryList({ id: row.id, status: statu })
			message.success('操作成功')
			console.log('11122')
			// 刷新列表
			await getList()
		} catch { }
	}

	/** 删除按钮操作 */
	const handleDelete = async (id : number) => {
		try {
			// 删除的二次确认
			await message.delConfirm()
			if (activeName.value == '1') {
				// 发起删除
				await ItemLibraryApi.deleteItemLibrary(id)
			} else {
				await ItemLibraryApi.deleteAuditItemLibrary(id)
			}
			message.success(t('common.delSuccess'))
			// 刷新列表
			await getList()
		} catch { }
	}

	/** 导出按钮操作 */
	const handleExport = async () => {
		try {
			// 导出的二次确认
			await message.exportConfirm()
			// 发起导出
			exportLoading.value = true
			if(activeName.value == '1'){
				const data = await ItemLibraryApi.exportItemLibrary(queryParams)
				const time = TimeSplicing(new Date())
				download.excel(data, `审计事项库${time}.xls`)
			} else {
				const data = await ItemLibraryApi.exportItemLibraryType(queryParams)
				const time = TimeSplicing(new Date())
				download.excel(data, `审计事项库${time}.xls`)
			}

		} catch {
		} finally {
			exportLoading.value = false
		}
	}

	const DispositionRef = ref()
	const addForm = () => {
		DispositionRef.value.open()
	}

	// 导入模板下载
	const handleExportTemplate = async() => {
		try {
			// 发起下载
			templateLoading.value = true
			const data = await ItemLibraryApi.exportTemplate()
			download.excel(data, `审计事项库导入模板.xls`)
		} catch {
		} finally {
			templateLoading.value = false
		}
	}

  // 数据导入
  const importFormRef = ref()
  const handleDataImport = () => {
    importFormRef.value.open()
  }
  const handleData = (val) => {
    console.log(val);
    message.success('导入成功')

  }
	//智库首页跳转-查询详情
	const projectId = ref({id: query.id as unknown as number})
	const getDateil = async() => {
		if(projectId.value.id){
			await openDetailForm(projectId.value.id)
			// 使用一次后销毁传参
			const newUrl = await window.location.href.replace(/\?.*$/, '') //获取当前路径，并去除参数
			await history.replaceState({}, '', newUrl) //更新浏览器历史记录，不触发页面重新加载
		}
	}

	/** 初始化 **/
	onMounted(() => {
		getList()
		getDateil()
	})
</script>
