<template>
  <doc-alert title="用户体系" url="https://doc.iocoder.cn/user-center/" />
  <doc-alert title="三方登陆" url="https://doc.iocoder.cn/social-user/" />
  <doc-alert title="Excel 导入导出" url="https://doc.iocoder.cn/excel-import-and-export/" />

  <el-row>
    <!-- 左侧部门树 -->
    <el-col :span="6" :xs="24">
      <ContentWrap class="h-1/1">
        <DeptTree @node-click="handleDeptNodeClick" />
      </ContentWrap>
    </el-col>
    <el-col :span="18" :xs="24">
      <!-- 查询 -->
      <ContentWrap class="common-card-search">
        <el-form
          class="-mb-15px common-search-form"
          :model="queryParams"
          ref="queryFormRef"
          :inline="true"
          label-width="76px"
        >
          <el-form-item label="账号" prop="username">
            <el-input
              v-model="queryParams.username"
              placeholder="请输入账号"
              clearable
              @keyup.enter="handleQuery"
              class="!w-200px"
            />
          </el-form-item>
          <el-form-item label="姓名" prop="nickname">
            <el-input
              v-model="queryParams.nickname"
              placeholder="请输入姓名"
              clearable
              @keyup.enter="handleQuery"
              class="!w-200px"
            />
          </el-form-item>
          <el-form-item label="手机号码" prop="mobile">
            <el-input
              v-model="queryParams.mobile"
              placeholder="请输入手机号码"
              clearable
              @keyup.enter="handleQuery"
              class="!w-200px"
            />
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select v-model="queryParams.status" placeholder="用户状态" clearable class="!w-200px">
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.SYSTEM_USER_STATUS)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <!-- <el-form-item>
            <el-button type="primary" @click="handleQuery">
              <Icon icon="ep:search" />查询
            </el-button>
            <el-button @click="resetQuery">
              <Icon icon="ep:refresh" />重置
          </el-button>-->
          <!-- <el-button
              type="warning"
              plain
              @click="handleImport"
              v-hasPermi="['system:user:import']"
            >
              <Icon icon="ep:upload" /> 导入
            </el-button>
            <el-button
              type="success"
              plain
              @click="handleExport"
              :loading="exportLoading"
              v-hasPermi="['system:user:export']"
            >
              <Icon icon="ep:download" />导出
          </el-button>-->
          <!-- </el-form-item> -->
        </el-form>
        <div class="right-search-btn">
          <el-button type="primary" @click="handleQuery">
            <Icon icon="ep:search" class="mr-5px" />搜索
          </el-button>
          <el-button @click="resetQuery">
            <Icon icon="ep:refresh" class="mr-5px" />重置
          </el-button>
        </div>
      </ContentWrap>
      <ContentWrap>
        <div class="button_margin15">
          <el-button
            type="primary"
            plain
            @click="addForm('create',0)"
            v-hasPermi="['system:user:create']"
          >
            <Icon icon="ep:plus" />员工新增
          </el-button>
          <el-button @click="addForm('create',1)" type="primary">
            <Icon icon="ep:plus" />手动新增
          </el-button>
        </div>
        <el-table border v-loading="loading" :data="list">
          <!-- <el-table-column label="SN编码" align="center" key="id" prop="snJobNo" /> -->
          <el-table-column label="序号" width="60" align="center">
            <template
              #default="{ $index }"
            >{{ (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1 }}</template>
          </el-table-column>
          <el-table-column
            label="账号"
            align="center"
            prop="username"
            :show-overflow-tooltip="true"
            min-width="120"
          />
          <el-table-column
            label="姓名"
            align="center"
            prop="nickname"
            :show-overflow-tooltip="true"
            min-width="120"
          />
          <el-table-column label="手机号码" align="center" prop="mobile" min-width="120" />
          <el-table-column
            label="所属岗位"
            align="center"
            key="deptName"
            prop="deptName"
            min-width="180"
            :show-overflow-tooltip="true"
          />
          <!-- <el-table-column label="岗位" align="center" prop="postName" width="120" /> -->
          <el-table-column label="状态" key="status" width="80">
            <template #default="scope">
              <el-switch
                style="--el-switch-on-color: #13ce66; "
                v-model="scope.row.status"
                :active-value="0"
                :inactive-value="1"
                @change="handleStatusChange(scope.row)"
              />
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" :width="300" fixed="right">
            <template #default="scope">
              <el-button
                type="primary"
                link
                @click="openForm('detail', scope.row.id)"
                v-hasPermi="['system:user:update']"
              >详情</el-button>
              <el-button
                type="danger"
                link
                @click="handleResetPwd(scope.row)"
                v-hasPermi="['system:user:update-password']"
              >重置密码</el-button>
              <el-button
                type="primary"
                link
                @click="handleRole(scope.row)"
                v-hasPermi="['system:user:update-password']"
              >分配角色</el-button>
              <el-button
                type="primary"
                link
                @click="addForm('update',2,scope.row.id)"
                v-hasPermi="['system:user:create']"
              >编辑</el-button>
              <!-- <el-button
                type="primary"
                link
                @click="handleResetPwd(scope.row)"
                v-hasPermi="['system:user:update-password']"
              >数据权限</el-button>-->
            </template>
          </el-table-column>
        </el-table>
        <Pagination
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </ContentWrap>
    </el-col>
  </el-row>
  <!-- 添加或修改用户对话框 -->
  <AddForm ref="formRef" @success="getList" />
  <!-- 详情用户对话框 -->
  <UserForm ref="openFormRef" @success="getList" />
  <!-- 用户导入对话框 -->
  <UserImportForm ref="importFormRef" @success="getList" />
  <!-- 分配角色 -->
  <UserAssignRoleForm ref="assignRoleFormRef" @success="getList" />
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { checkPermi } from '@/utils/permission'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { CommonStatusEnum } from '@/utils/constants'
import * as UserApi from '@/api/system/user'
import UserForm from './UserForm.vue'
import UserImportForm from './UserImportForm.vue'
import UserAssignRoleForm from './UserAssignRoleForm.vue'
import DeptTree from './DeptTree.vue'
import AddForm from './addform.vue'
import { ElMessageBox } from 'element-plus'
defineOptions({ name: 'SystemUser' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  username: undefined,
  mobile: undefined,
  status: undefined,
  deptId: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await UserApi.getUserPage(queryParams)

    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  handleQuery()
}

/** 处理部门被点击 */
const handleDeptNodeClick = async (row) => {
  queryParams.deptId = row.id
  await getList()
}

/** 详情操作 */
const openFormRef = ref()
const openForm = (type: string, id?: number) => {
  openFormRef.value.open(type, id)
}

/** 添加/修改操作 */
const formRef = ref()
const addForm = (type: string, status: number, id?: number) => {
  formRef.value.open(type, status, id)
}

/** 用户导入 */
const importFormRef = ref()
const handleImport = () => {
  importFormRef.value.open()
}

/** 修改用户状态 */
const handleStatusChange = async (row: UserApi.UserVO) => {
  try {
    // 修改状态的二次确认
    const text = row.status === CommonStatusEnum.ENABLE ? '启用' : '停用'
    await message.confirm('确认要"' + text + '""' + row.username + '"用户吗?')
    // 发起修改状态
    await UserApi.updateUserStatus(row.id, row.status)
    // 刷新列表
    await getList()
  } catch {
    // 取消后，进行恢复按钮
    row.status =
      row.status === CommonStatusEnum.ENABLE ? CommonStatusEnum.DISABLE : CommonStatusEnum.ENABLE
  }
}

/** 导出按钮操作 */
const exportLoading = ref(false)
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await UserApi.exportUser(queryParams)
    download.excel(data, '用户数据.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 操作分发 */
const handleCommand = (command: string, row: UserApi.UserVO) => {
  switch (command) {
    case 'handleDelete':
      handleDelete(row.id)
      break
    case 'handleResetPwd':
      handleResetPwd(row)
      break
    case 'handleRole':
      handleRole(row)
      break
    default:
      break
  }
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await UserApi.deleteUser(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 重置密码 */
const handleResetPwd = async (row: UserApi.UserVO) => {
  try {
    // 定义密码规则
    const passwordPattern = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,16}$/

    // 重置的二次确认
    const result = await ElMessageBox.prompt(
      '请输入"' + row.username + '"的新密码',
      t('common.reminder'),
      {
        confirmButtonText: t('common.ok'),
        cancelButtonText: t('common.cancel'),
        type: 'warning',
        inputPattern: passwordPattern,
        inputErrorMessage: t('密码必须包含数字、字母（大小写）和符号，且长度在8到16位之间') // 提示信息需要根据实际情况调整
      }
    )

    const password = result.value

    // 发起重置
    await UserApi.resetUserPwd(row.id, password)
    message.success('修改成功，新密码是：' + password)
  } catch (error) {
   
  }
}
/** 分配角色 */
const assignRoleFormRef = ref()
const handleRole = (row: UserApi.UserVO) => {
  assignRoleFormRef.value.open(row)
}

/** 初始化 */
onMounted(() => {
  getList()
})
</script>
