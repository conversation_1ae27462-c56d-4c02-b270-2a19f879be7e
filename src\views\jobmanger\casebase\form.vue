<!--
* @Author: lijunliang
* @Date: 2024-09-12 16:31:32
* @Description: 审计案例库表单=>
-->
<template>
	<Dialog :title="dialogTitle" v-model="dialogVisible" width="70%">
		<el-form ref="formRef" :model="formData" :rules="formRules" class="common-submit-form" v-loading="formLoading">
			<el-row>
				<!-- <el-col :span="8"> -->
					<el-form-item label="案例名称" prop="caseName" label-width="100px">
						<el-input v-model="formData.caseName" placeholder="请输入案例名称" class="!w-220px" />
					</el-form-item>
				<!-- </el-col> -->
				<!-- <el-col :span="8"> -->
					<el-form-item label="填报人员" prop="writeById" label-width="100px">
						<el-select v-model="formData.writeById" filterable placeholder="请输入填报人" class="!w-220px">
							<el-option
								v-for="item in formuserData"
								:key="item.id"
								:label="item.nickname"
								:value="item.id"
							/>
						</el-select>
					</el-form-item>
				<!-- </el-col> -->
				<!-- <el-col :span="8"> -->
					<el-form-item label="填报单位" prop="writeDeptId" label-width="100px">
						<el-tree-select
							v-model="formData.writeDeptId"
							ref="treeRef"
							clearable
							filterable
							placeholder="请选择填报单位"
							:data="deptList"
							check-strictly
							:expand-on-click-node="false"
							:check-on-click-node="true"
							:default-expand-all="false"
							highlight-current
							node-key="id"
							@node-click="handleNodeClick"
							:load="loadNode"
							:default-expanded-keys="defaultExpandedKeys"
							:filter-node-method="filterNode"
							lazy
							class="!w-240px"
							>
							<template #default="{ data: { name } }">{{ name }}</template>
						</el-tree-select>
					</el-form-item>
				<!-- </el-col> -->
				<!-- <el-col :span="8"> -->
					<el-form-item label="适用审计事项" prop="suitAudit" label-width="100px">
						<el-tree-select
							v-model="formData.suitAudit"
							:data="mattersList"
							multiple
							filterable
							check-strictly
							placeholder="请选择事项名称"
							:render-after-expand="false"
							class="!w-220px"
						/>
					</el-form-item>
				<!-- </el-col> -->
			</el-row>
			<!-- <el-form-item label="案例概述：" prop="illustrate"> -->
				<Editor style="margin: 0 25px 18px 25px;" v-model="formData.illustrate" height="300px" />
			<!-- </el-form-item> -->
			<div class="del_hover">
				<el-form-item label="案例附件" prop="fileIds">
					<el-button type="primary" plain @click="handleImport('file')">
						<Icon icon="ep:upload" class="mr-5px" /> 选择文件
					</el-button>
				</el-form-item>
			</div>
			<!-- <el-form-item>
				<div v-if="formData.attachments && formData.attachments.length > 0" class="news-attachments">
					<el-row :gutter="10">
						<el-col :span="24" v-for="(attachment, index) in formData.attachments" :key="index"
							class="mb-10px">
							<el-row>
								<el-col :span="18"><span class="attachment-file-name ellipsis"
										:title="attachment.name">{{
			              attachment.name
			            }}</span></el-col>
								<el-col :span="6">
									<el-button type="danger" class="mr-10px" size="small"
										@click="handleDelete('attachments', index)">删除</el-button>
									<el-button type="primary" size="small" @click="previewAttachment(attachment)"
			              >预览</el-button
			            >
								</el-col>
							</el-row>
						</el-col>
					</el-row>
				</div>
			</el-form-item> -->
			<el-table :data="formData.fileIds" border :stripe="true" :show-overflow-tooltip="true">
				<el-table-column type="index" label="序号" width="60" align="center"/>
				<el-table-column label="文档名称" align="left" prop="fileName" min-width="120" />
				<el-table-column label="创建人" align="center" prop="creatorName" />
				<el-table-column label="创建时间" align="center" prop="createTime">
					<template #default="scope">
						{{ formatDate(scope.row.createTime) }}
					</template>
				</el-table-column>
				<el-table-column label="操作" align="center" width="150">
					<template #default="scope">
						<el-button link type="danger" @click="listDelete('tableData', scope.$index)">删除</el-button>
						<el-button link type="primary" @click="listDownload(scope.row?.fileUrl,scope.row?.fileName)">下载</el-button>
					</template>
				</el-table-column>
			</el-table>
		</el-form>

		<template #footer>
			<el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
			<el-button @click="dialogVisible = false">取 消</el-button>
		</template>

	</Dialog>

	<!-- <Dialog :title="dialogListTitle" v-model="dialogListVisible" width="70%">

	</Dialog> -->
	<!-- 文件上传 -->
	<FileForm ref="formImgRef" @success="handleUploadSuccess" :type="fileType" :limit="fileLimit" />
</template>
<script setup lang="ts">
	import { getIntDictOptions, getStrDictOptions, DICT_TYPE } from '@/utils/dict'
	import { AuditRoleApi, AuditRoleVO } from '@/api/basicData/auditRole'
	import { ItemLibraryApi } from '@/api/jobmanger/itemLibrary'
	import { CasebaseApiNew, CasebaseNew } from '@/api/jobmanger/casebase'
	import FileForm from '@/views/infra/file/FileForm.vue'
	import { CACHE_KEY, useCache } from '@/hooks/web/useCache'
	import { handleTree } from '@/utils/tree'
	import * as DeptApi from '@/api/system/dept'
	import { defaultProps, handleLazyTree } from '@/utils/tree'
	import { formatDate } from '@/utils/formatTime'
	import { downFile } from '@/utils/fileName'
	/** 审计角色 表单 */
	defineOptions({ name: 'CasebaseForm' })

	const { t } = useI18n() // 国际化
	const message = useMessage() // 消息弹窗
	const { wsCache } = useCache()

	const dialogVisible = ref(false) // 弹窗的是否展示
	const dialogTitle = ref('') // 弹窗的标题
	const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
	const formType = ref('') // 表单的类型：create - 新增；update - 修改
	const formData = ref({
		id: undefined,
		caseName: undefined, //案例名称
		writeBy: undefined, //填报人
		writeById: undefined,//填报人ID
		writeDept: undefined,//填报单位
		writeDeptId: undefined,//填报单位ID
		suitAudit: undefined,//适用审计事项
		illustrate:undefined,
		fileIds: [], //文件信息集合
		showImg: []
	})
	const formRules = reactive({
		caseName: [{ required: true, message: '案例名称不能为空', trigger: 'blur' }],
		writeById: [{ required: true, message: '填报人员不能为空', trigger: 'blur' }],
		writeDeptId: [{ required: true, message: '填报单位不能为空', trigger: 'blur' }],
		suitAudit: [{ required: true, message: '适用审计事项不能为空', trigger: 'blur' }],
		// parentId: [{ required: true, message: '父级编号不能为空', trigger: 'change' }]
	})
	const formRef = ref() // 表单 Ref

	const formImgRef = ref()
	const fileType = ref('img')
	const fileLimit = ref(1)
	// 上传方法
	const handleImport = (type : string) => {
		fileType.value = type
		// fileLimit.value = type === 'file' ? 5 : 1
		formImgRef.value.open()
	}
	const handleUploadSuccess = (fileList) => {
		console.log(fileList)
		let fileArr =
			fileList && fileList.length > 0
				? fileList.map((item) => {
					formData.value.fileIds = formData.value.fileIds??[]
					formData.value.fileIds.unshift(item.response.data)
					return {
						url: item.response.data,
						name: item.name,
						founder: wsCache.get(CACHE_KEY.USER).user.nickname,
						creationtime: new Date().toLocaleTimeString(),

						fileName: item.response.data.fileName,
						creatorName: item.response.data.creatorName,
						createTime: item.response.data.createTime,
						busiId: item.response.data.busiId,
						configId: item.response.data.configId,
						filePath: item.response.data.filePath,
						fileSize: item.response.data.fileSize,
						fileType: item.response.data.fileType,
						fileUrl: item.response.data.fileUrl,
						id: item.response.data.id,
						updaterName: item.response.data.updaterName,
					}
				})
				: []
		if (fileType.value === 'file') {
			// formData.value.fileIds = formData.value.fileIds??[]
			// formData.value.fileIds = formData.value.fileIds.concat(...fileArr)
		} else if (fileType.value === 'img') {
			formData.value.showImg = fileArr
			formRef.value.validateField('showImg')
		}
	}
	// const handleDelete = async (type : string, index : number = 0) => {
	// 	await message.delConfirm()
	// 	formData.value[type].splice(index, 1)
	// 	await message.success(t('common.delSuccess'))
	// }

	const mattersList = ref<any[]>([])
	const formuserData = ref([])

	/** 打开弹窗 */
	const open = async (type : string, id ?: number) => {
		dialogVisible.value = true
		formLoading.value = true
		dialogTitle.value = t('action.' + type)
		mattersList.value = []
		formType.value = type
		resetForm()
		try {
			// 查询适用审计事项
			const res = await ItemLibraryApi.getMatterslist()
			mattersList.value.push(...handleTree(res, 'id', 'parentId'))
			//获取填报单位
			await getTree()
			// 获取所有人员信息（选择填报人）
			const data = await CasebaseApiNew.getUserData()
			formuserData.value = data
			if(id){
				// 获取审计案例库详情
				formData.value = await CasebaseApiNew.getCasebase(id)
			}
		} finally {
			formLoading.value = false
		}


		// Details(id)
	}
	defineExpose({ open }) // 提供 open 方法，用于打开弹窗

	/*
	* @Author: lijunliang
	* @Date: 2024-09-12 11:27:52
	* @Description: 删除=>
	*/
	const listDelete = async (type : string, index : number = 0) => {
		await message.delConfirm()
		// formData.value[type].splice(index, 1)
		formData.value.fileIds.splice(index, 1)
		await message.success(t('common.delSuccess'))
	}
	/*
	* @Author: lijunliang
	* @Date: 2024-09-12 11:26:36
	* @Description: 下载=>
	*/
	const listDownload = async(url, name) => {
		// 下载的二次确认
    	await message.confirm('是否确认下载？')
		downFile(url, name)
	 }

	// 填报单位树结构
	interface DeptNode {
		id: number
		masterOrgId?: number | string
		name: string
		parentId: number | string
		children?: DeptNode[]
		isLeaf?: boolean
	}
	const deptList = ref<DeptNode[]>([]) // 树形结构
	const defaultExpandedKeys = ref<(string | number)[]>([])
	const getTree = async (id) => {
		deptList.value = []
		const res = await DeptApi.getSimpleDeptList(id)
		deptList.value.push(...handleLazyTree(res, 'id', 'parentId'))
		defaultExpandedKeys.value = deptList.value.map((node) => node.id)
		console.log(deptList.value)
	}
	const loadNode = async (node: Node, resolve: (data: any[]) => void) => {
		try {
			const nodeId = node.data.id
			if (nodeId == undefined || nodeId == null) {
				return
			}
			const res = await DeptApi.getSimpleDeptList(nodeId)
			const children = handleLazyTree(res, 'id', 'parentId', 'children')
			resolve(children)
		} catch (error) {
			resolve([])
		}
	}

	/** 提交表单 */
	const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
	const submitForm = async () => {
		// 校验表单
		await formRef.value.validate()
		// 提交请求
		formLoading.value = true
		try {
			// const data = formData.value as unknown as AuditRoleVO
			const data = formData.value as unknown as CasebaseNew
			if (formType.value === 'create') {
				// 新增
				await CasebaseApiNew.createCasebaseNew(data)
				message.success(t('common.createSuccess'))
			} else {
				// 修改
				await CasebaseApiNew.editCasebaseNew(data)
				message.success(t('common.updateSuccess'))
			}
			dialogVisible.value = false
			// 发送操作成功的事件
			emit('success')
		} finally {
			formLoading.value = false
		}
	}

	/** 重置表单 */
	const resetForm = () => {
		formData.value = {
			id: undefined,
			caseName: undefined,
			writeBy: undefined,
			writeById: undefined,
			writeDept: undefined,
			writeDeptId: undefined,
			suitAudit: undefined,
			// illustrate: undefined,
			illustrate: `1. 审计对象：用于标识特定的审计对象。\n2. 审计目标：明确审计工作的具体目标，例如验证财务报表的真实性、评估内部控制的有效性等。\n3. 审计程序：详细记录执行审计工作所采取的具体步骤和方法。\n4. 审计亮点：记录在审计过程亮点。\n5. 审计发现与建议：针对发现的问题，提出改进建议和措施。\n6. 审计成效：列出审计成效。\n7. 政策依据：列出与审计相关的法律法规和标准，确保审计工作符合法律要求。\n8. 其他：其他补充说明。"`,
			fileIds: [],
		}
		formRef.value?.resetFields()
	}
</script>
