<!-- 项目看板 -->
<template>
    <div class="header">
        <div class="header_title">
            <div class="header_title_size">
                项目开展情况统计
            </div>
            <div class="header_date">
                <div class="pr-14px">年度</div>
                <el-date-picker v-model="queryParams.startYear" type="year" placeholder="开始" class="!w-90px"
                    @change="handleQuery" value-format="YYYY"/>
            </div>
        </div>
        <div class="flex items-center pt-12px pr-20px pl-20px">
            <div class="header_card1">
                <div class="header_card_title">
                    项目总数
                </div>
                <el-row class="flex items-center pl-20px pt-10px">
                    <el-col :span="10" :xs="24">
                        <el-image style="width: 64px; height: 64px" :src="url1" fit="cover" />
                    </el-col>
                    <el-col :span="14" :xs="24">
                        <div class="card_number text-center">
                            {{topNumData.projectNum}}
                        </div>
                        <div class="card_text text-center">
                            当前项目总数
                        </div>
                    </el-col>
                </el-row>
            </div>
            <div class="header_card2">
                <div class="header_card_title">
                    执行中
                </div>
                <el-row class="flex items-center pl-20px pt-10px">
                    <el-col :span="6" :xs="24">
                        <el-image style="width: 64px; height: 64px" :src="url3" fit="cover" />
                    </el-col>
                    <el-col :span="9" :xs="24">
                        <div class="card_number text-center">
                            {{topNumData.runProjectNum}}
                        </div>
                        <div class="card_text text-center">
                            执行中的项目
                        </div>
                    </el-col>
                    <el-col :span="9" :xs="24">
                        <div class="card_number text-center">
                            {{topNumData.runProjectNumRate}}%
                        </div>
                        <div class="card_text text-center">
                            占比
                        </div>
                    </el-col>
                </el-row>
            </div>
            <div class="header_card2">
                <div class="header_card_title">
                    已完成
                </div>
                <el-row class="flex items-center pl-20px pt-10px">
                    <el-col :span="6" :xs="24">
                        <el-image style="width: 64px; height: 64px" :src="url4" fit="cover" />
                    </el-col>
                    <el-col :span="9" :xs="24">
                        <div class="card_number text-center">
                            {{topNumData.finishProjectNum}}
                        </div>
                        <div class="card_text text-center">
                            已完成的项目
                        </div>
                    </el-col>
                    <el-col :span="9" :xs="24">
                        <div class="card_number text-center">
                            {{topNumData.finishProjectNumRate}}%
                        </div>
                        <div class="card_text text-center">
                            占比
                        </div>
                    </el-col>
                </el-row>
            </div>
            <div class="header_card2">
                <div class="header_card_title">
                    已超期
                </div>
                <el-row class="flex items-center pl-20px pt-10px">
                    <el-col :span="6" :xs="24">
                        <el-image style="width: 64px; height: 64px" :src="url5" fit="cover" />
                    </el-col>
                    <el-col :span="9" :xs="24">
                        <div class="card_number text-center">
                            {{topNumData.timeOutProjectNum}}
                        </div>
                        <div class="card_text text-center">
                            已超期的计划
                        </div>
                    </el-col>
                    <el-col :span="9" :xs="24">
                        <div class="card_number text-center">
                            {{topNumData.timeOutProjectNumRate}}%
                        </div>
                        <div class="card_text text-center">
                            占比
                        </div>
                    </el-col>
                </el-row>
            </div>
            <div class="header_card3">
                <div class="header_card_title">
                    已归档
                </div>
                <el-row class="flex items-center pl-20px pt-10px">
                    <el-col :span="6" :xs="24">
                        <el-image style="width: 64px; height: 64px" :src="url2" fit="cover" />
                    </el-col>
                    <el-col :span="9" :xs="24">
                        <div class="card_number text-center">
                            {{topNumData.archivedProjectNum}}
                        </div>
                        <div class="card_text text-center">
                            已归档的项目
                        </div>
                    </el-col>
                    <el-col :span="9" :xs="24">
                        <div class="card_number text-center">
                            {{topNumData.archivedProjectNumRate}}%
                        </div>
                        <div class="card_text text-center">
                            占比
                        </div>
                    </el-col>
                </el-row>
            </div>
        </div>
    </div>
    <el-row :gutter="8" class="pt-16px">
        <el-col :span="6" :xs="24">
            <ContentWrap>
                <div class="flex_title">
                    <div class="title-left"></div>
                    <div class="pl-8px">项目类型分布</div>
                </div>
                <div :class="oneChart" ref="oneChartRef" style="height:288px;width:100%;"></div>
            </ContentWrap>
        </el-col>
        <el-col :span="6" :xs="24">
            <ContentWrap>
                <div class="flex_title">
                    <div class="title-left"></div>
                    <div class="pl-8px">组织方式分布</div>
                </div>
                <div :class="fourChart" ref="fourChartRef" style="height:288px;width:100%;"></div>
            </ContentWrap>
        </el-col>
        <el-col :span="6" :xs="24">
            <ContentWrap>
                <div class="flex_title">
                    <div class="title-left"></div>
                    <div class="pl-8px">项目实施进度分布</div>
                </div>
                <div :class="threeChart" ref="threeChartRef" style="height:288px;width:100%;"></div>
            </ContentWrap>
        </el-col>
        <el-col :span="6" :xs="24">
            <ContentWrap>
                <div class="flex_title">
                    <div class="title-left"></div>
                    <div class="pl-8px">现场审计文档统计</div>
                </div>
                <div :class="twoChart" ref="twoChartRef" style="height:288px;width:100%;"></div>
            </ContentWrap>
        </el-col>
    </el-row>
    <ContentWrap>
        <div class="flex_title">
            <div class="title-left"></div>
            <div class="pl-8px">审计对象档案归档情况统计</div>
        </div>
        <div :class="fiveChart" ref="fiveChartRef" style="height:288px;width:100%;"></div>
    </ContentWrap>
</template>

<script lang="ts" setup>
import * as echarts from 'echarts';
import demoviewIcon1 from '@/assets/imgs/decision/icon-jihua.png';
import demoviewIcon2 from '@/assets/imgs/decision/icon-weilixiang.png';
import demoviewIcon3 from '@/assets/imgs/decision/icon-zhixingzhong.png';
import demoviewIcon4 from '@/assets/imgs/decision/icon-yiwancheng.png';
import demoviewIcon5 from '@/assets/imgs/decision/icon-yichaoqi.png';
import { colorList } from '../../index'
import { ItemApi, TopNumVO } from '@/api/decision/item'
import { getDictLabel } from '@/utils/dict'
defineOptions({
    name: 'ProjectKanban'
})
const queryParams = reactive({
    startYear: undefined,
})
const loading = ref(true) // 列表的加载中

const url1 = ref(demoviewIcon1)
const url2 = ref(demoviewIcon2)
const url3 = ref(demoviewIcon3)
const url4 = ref(demoviewIcon4)
const url5 = ref(demoviewIcon5)
const topNumData = ref({} as TopNumVO)

const color = ['#2A71C0', '#FF9C01', '#AC3C52', '#C96F37', '#95C2ED', '#E6E8F4', '#ABEDD8']

const getAllApi = async () => {
    topNumData.value  = await ItemApi.getTopNum(queryParams)
    const TypeNumData = await ItemApi.getTypeNum(queryParams)
    const OrgNumData = await ItemApi.getOrgNum(queryParams)
    const StateNumData = await ItemApi.getStateNum(queryParams)
    const WordNumData = await ItemApi.getWordNum(queryParams)
    console.log(WordNumData);
    await Promise.all([getOneChart(TypeNumData)])
    await Promise.all([getTwoChart(WordNumData)])
    await Promise.all([getThreeChart(StateNumData)])
    await Promise.all([getFourChart(OrgNumData)])
    await Promise.all([getFiveChart()])
    loading.value = false
}

// 第一个echarts
let oneChart: echarts.ECharts | null = null;
const oneChartRef = ref()
const getOneChart = async (val) => {
    oneChart = echarts.init(oneChartRef.value)
    const data = val
    data.forEach((x: any) => {
        x.name = x.typeName
        x.value = x.num
    })
    oneChart.setOption({
        color: color,
        // tooltip: {
        //     trigger: 'item',
        // },
        legend: {
            top: 'bottom',
            itemWidth: 14,
            data: data,
            itemHeight: 14,
            textStyle: {
                lineHeight: 14
            }
        },
        series: [
            {
                name: '姓名',
                type: 'pie',
                radius: ['50%', '60%'],
                center: ['50%', '40%'],
                avoidLabelOverlap: false,
                padAngle: 5,
                itemStyle: {
                    borderRadius: 10
                },
                label: {
                    show: false,
                    position: 'center'
                },
                emphasis: {
                    label: {
                        show: true,
                        fontSize: 18,
                        fontWeight: 500,
                        formatter: function (params) {
                            return '{total|' + params.value + '}' + '\n ' + params.name;
                        },
                        rich: {
                            total: {
                                fontSize: 25,
                                color: '#000',
                                lineHeight: 40
                            }
                        }
                    }
                },
                labelLine: {
                    show: false
                },
                data: data
            }
        ]
    })
}

// 第二个echarts
let twoChart: echarts.ECharts | null = null;
const twoChartRef = ref()
const getTwoChart = async (val) => {
    twoChart = echarts.init(twoChartRef.value)
    const data = [
        { value: val.schemaNum, name: '审计方案' },
        { value: val.certNum, name: '审计取证单' },
        { value: val.baseWordNum, name: '审计工作底稿' },
        { value: val.reportWordNum, name: '审计报告' },
    ]
    twoChart.setOption({
        color: color,
        tooltip: {
            trigger: 'item',
        },
        legend: {
            top: 'bottom',
            itemWidth: 14,
            data: data,
            itemHeight: 14,
            textStyle: {
                lineHeight: 14
            }
        },
        series: [
            {
                name: '姓名',
                type: 'pie',
                radius: [20, 100],
                center: ['50%', '40%'],
                roseType: 'area',
                data: data,
                itemStyle: {
                    borderRadius: 2,
                    normal: {
                        color: function (params) {
                            return colorList[params.dataIndex];
                        },
                    },
                },
                label: {
                    show: false
                },
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }
        ]
    })
}

// 第三个echarts
let threeChart: echarts.ECharts | null = null;
const threeChartRef = ref()
const getThreeChart = async (val) => {
    threeChart = echarts.init(threeChartRef.value)
    const dataList = val.map((item) =>  getDictLabel('proj_parent_project_stage',item.state))
	const dataList1 = val.map((item) => item.num)
	const dataList2 = val.map((item) => item.rate)
    threeChart.setOption({
        color: color,
        grid: {
            top: "10%"
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross',
                crossStyle: {
                    color: '#999'
                }
            }
        },
        legend: {
            data: ['项目数量', '占比'],
            itemWidth: 14,
            top: 'bottom',
            itemHeight: 14,
            textStyle: {
                lineHeight: 14
            }
        },
        xAxis: [
            {
                type: 'category',
                data: dataList,
                axisPointer: {
                    type: 'shadow'
                },
                axisLabel: {
                    interval: 0,
                    formatter: function (value) {
                        // 返回简短版本的标签文本
                        return value.substring(0, 1) + '...'; // 截取前8个字符并添加省略号
                    }
                }
            }
        ],
        yAxis: [
            {
                type: 'value',
            },
            {
                type: 'value',
                splitLine: {
					show: false,
				}
            }
        ],
        series: [
            {
                name: '项目数量',
                type: 'bar',
                data: dataList1,
                barMaxWidth: 40,
                itemStyle: {
                    borderRadius: [10, 10, 0, 0]
                }
            },
            {
                name: '占比',
                type: 'line',
                yAxisIndex: 1,
                data: dataList2
            }
        ]
    })
}

// 第四个echarts
let fourChart: echarts.ECharts | null = null;
const fourChartRef = ref()
const getFourChart = async (val) => {
    fourChart = echarts.init(fourChartRef.value)
    const data = val
    data.forEach((x: any) => {
        x.value = x.num
        x.name = getDictLabel('organization_type',x.typeId)
    })
    fourChart.setOption({
        color: color,
        tooltip: {
            trigger: 'item',
        },
        legend: {
            top: 'bottom',
            itemWidth: 14,
            data: data,
            itemHeight: 14,
            textStyle: {
                lineHeight: 14
            }
        },
        series: [
            {
                name: '姓名',
                type: 'pie',
                radius: '60%',
                center: ['50%', '40%'],
                data: data,
                label: {
                    show: false
                },
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                },
                selectedMode: "single",
            }
        ]
    })
}

// 第五个echarts
let fiveChart: echarts.ECharts | null = null;
const fiveChartRef = ref()
const getFiveChart = async () => {
    fiveChart = echarts.init(fiveChartRef.value)
    fiveChart.setOption({
        color: color,
        grid: {
            top: "10%",
            right: 50,
            left: 50,
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross',
                crossStyle: {
                    color: '#999'
                }
            }
        },
        legend: {
            data: ['计划数', '已完成', '计划完成率'],
            itemWidth: 14,
            top: 'bottom',
            itemHeight: 14,
            textStyle: {
                lineHeight: 14
            }
        },
        xAxis: [
            {
                type: 'category',
                data: ['青岛港引航站有限公司', '青岛港务局驻深圳办', '青岛国际邮轮开发建', '山东港信期货有限公司', '青岛港融资担保有限', '青岛港口投资建设', '山东港口青港实华能', '山东港口威海港有限', '青岛港国际股份有限', '山东港口投资控股有'],
                axisPointer: {
                    type: 'shadow'
                },
                axisLabel: {
                    interval: 0,
                }
            }
        ],
        yAxis: [
            {
                type: 'value',
            },
            {
                type: 'value',
                splitLine: {
					show: false,
				}
            }
        ],
        series: [
            {
                name: '计划数',
                type: 'bar',
                data: [20, 30, 23, 32, 30, 20, 25, 20, 15, 21],
                barMaxWidth: 40,
                itemStyle: {
                    borderRadius: [10, 10, 0, 0],
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 1, color: '#BBC8DE' },
                        { offset: 0, color: '#07397E' }
                    ])
                }
            },
            {
                name: '已完成',
                type: 'bar',
                data: [5, 15, 11, 15, 12, 10, 5, 2, 5, 11],
                barMaxWidth: 40,
                itemStyle: {
                    borderRadius: [10, 10, 0, 0],
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 1, color: '#75ACE3' },
                        { offset: 0, color: '#82ADD7' }
                    ])
                }
            },
            {
                name: '计划完成率',
                type: 'line',
                yAxisIndex: 1,
                data: [25.00, 50.00, 47.83, 46.88, 40.00, 50.00, 20.00, 10.00, 33.33, 52.38]
            }
        ]
    })
}
/** 搜索按钮操作 */
const handleQuery = () => {
    console.log('000')
    getAllApi()
}

/** 初始化 **/
onMounted(() => {
    getAllApi()
})
</script>

<style scoped>
/* 去除输入框边框 */
::v-deep .el-date-editor {
	--el-input-border-color: transparent !important;
	--el-input-bg-color: transparent;
    --el-input-hover-border-color: #fff;
}
.flex_title {
    display: flex;
    align-items: center;
    line-height: 33px;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 700;
    font-size: 16px;
    color: #333333;
    border-bottom: 1px solid #EEEFF0;
}

.title-left {
    width: 5px;
    height: 15px;
    background: #2F4CAD;
    border-radius: 0px 0px 0px 0px;
}

.header {
    height: 199px;
    border: 2px solid rgba(255, 255, 255, 1);
    background: #F7F7F7;
    border-radius: 8px;
}

.header_title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 20px 0;
}

.header_title_size {
    height: 26px;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 500;
    font-size: 18px;
    color: #333333;
    line-height: 26px;
}

.header_date {
    width: 144px;
    height: 34px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 8px 8px 8px 8px;
    border: 2px solid #FFFFFF;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    font-size: 15px;
    color: #999;
    line-height: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.header_card1 {
    flex: 1;
    height: 125px;
    background: linear-gradient(160.24deg, #A0BDF0 13.03%, #07397E 86.79%);
    border-radius: 8px 8px 8px 8px;
    border: 2px solid #FFFFFF;
}

.header_card2 {
    flex: 1.5;
    height: 125px;
    background: linear-gradient(160.24deg, #A0BDF0 13.03%, #07397E 86.79%);
    border-radius: 8px 8px 8px 8px;
    border: 2px solid #FFFFFF;
    margin-left: 6px;
}

.header_card3 {
    flex: 1.5;
    height: 125px;
    background: linear-gradient(160.24deg, #FCC535 13.03%, #FF7F00 86.79%);
    border-radius: 8px 8px 8px 8px;
    border: 2px solid #FFFFFF;
    margin-left: 6px;
}

.header_card_title {
    height: 23px;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    font-size: 16px;
    color: #fff;
    line-height: 23px;
    padding-top: 14px;
    padding-left: 20px;
}

.header_card_item {
    flex: 1;
}

.card_number {
    height: 41px;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 700;
    font-size: 26px;
    color: #fff;
    line-height: 41px;
}

.card_text {
    height: 22px;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    font-size: 15px;
    color: #fff;
    line-height: 22px;
}
</style>