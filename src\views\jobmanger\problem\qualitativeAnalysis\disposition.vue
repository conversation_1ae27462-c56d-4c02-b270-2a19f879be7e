<template>
	<Dialog :title="dialogTitle" :scroll="true" v-model="dialogVisible">
		<el-row :gutter="16">
			<el-col :span="12" :xs="24">
				<ContentWrap class="h-1/1">
					<JobmangerLeftTree @node-click="handleDeptNodeClick" :searchUrl="searchUrl" :showEdit="false" />
				</ContentWrap>
			</el-col>
			<el-col :span="12" :xs="24">
				<ContentWrap class="h-1/1">
					<el-input v-model="name" class="mb-20px" clearable placeholder="请输入名称">
						<template #prefix>
							<Icon icon="ep:search" />
						</template>
					</el-input>
					<el-tree ref="treeRef" :data="deptList" check-strictly show-checkbox :expand-on-click-node="false"
						:filter-node-method="filterNode" :props="defaultProps" :default-expand-all="false"
						highlight-current node-key="id" @check="handleNodeClick"
						:default-expanded-keys="defaultExpandedKeys">
						<template #default="{ node }">
							<span class="custom-tree-node">
								<Icon icon="f7:doc" :size="12" />
								{{ node.label }}
							</span>
						</template>
					</el-tree>
				</ContentWrap>
			</el-col>
		</el-row>
		<template #footer>
			<el-button @click="submitForm" type="primary" :disabled="formLoading">保存</el-button>
			<!-- <el-button @click="submitForm" type="primary" :disabled="formLoading">提交</el-button> -->
			<el-button @click="dialogVisible = false">取消</el-button>
		</template>
	</Dialog>
</template>
<script setup lang="ts">
	import { getIntDictOptions, getStrDictOptions, DICT_TYPE } from '@/utils/dict'
	import { QualitativeAnalysisApi } from '@/api/jobmanger/problem/qualitativeAnalysis'
	import FileForm from '@/views/infra/file/FileForm.vue'
	import JobmangerLeftTree from '@/views/jobmanger/components/JobmangerLeftTree.vue'
	import { defaultProps, handleTree, findPath, treeToList } from '@/utils/tree'
	import { ElTree } from 'element-plus'
	defineOptions({ name: 'ChoiceMatter' })

	const { t } = useI18n() // 国际化
	const message = useMessage() // 消息弹窗
	// const searchUrl = ref('/audit/tank-matters-info/get-matterslist') // 树形数据
	const searchUrl = ref('/audit/tank-trees-type/get?type=4') // 树形数据
	const dialogVisible = ref(false) // 弹窗的是否展示
	const dialogTitle = ref('') // 弹窗的标题
	const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
	const deptList = ref<TreeNode[]>([]) // 树形结构
	const currentRow = ref()
	const matterId = ref()
	const name = ref('')
	/** 右侧被点击 */
	const handleNodeClick = async (data, check) => {
		console.log(data, check);
		currentRow.value = check
	}
	const treeRef = ref<InstanceType<typeof ElTree>>()
	const defaultExpandedKeys = ref<(string | number)[]>([])
	/** 基于名字过滤 */
	const filterNode = (name : string, data : TreeNode) => {
		if (!name) return true
		return data.name.includes(name)
	}
	/** 打开弹窗 */
	const open = async (id ?: number) => {
		dialogVisible.value = true
		dialogTitle.value = t('配置问题定性')
		// formType.value = type
		// resetForm()
		formLoading.value = true
		try {
			currentRow.value = []
			matterId.value = {}
			deptList.value = []
			const res = await QualitativeAnalysisApi.getQualitativeAnalysisAll()
			deptList.value.push(...handleTree(res, 'id', 'parentId'))
			defaultExpandedKeys.value = deptList.value.map((node) => node.id)
		} finally {
			formLoading.value = false
		}
	}
	defineExpose({ open }) // 提供 open 方法，用于打开弹窗

	/** 左侧被点击 */
	const handleDeptNodeClick = async (row) => {
		matterId.value = row.id
	}

	/** 提交表单 */
	const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
	const submitForm = async () => {
		if (currentRow.value.checkedNodes === undefined || !matterId.value) {
			message.error('请选择事项和问题分类')
			return
		}
		currentRow.value.checkedNodes.forEach(item => {
			item.issueId = item.id
			item.auditMattersId = matterId.value
		})
		// 提交请求
		formLoading.value = true
		try {
			await QualitativeAnalysisApi.addQualitativeAnalysis(currentRow.value.checkedNodes)
			message.success(t('common.createSuccess'))
			dialogVisible.value = false
			// 发送操作成功的事件
			emit('success')
		} finally {
			formLoading.value = false
		}
	}
</script>
<style lang="scss" scoped>
	.title {
		font-weight: 900;
		font-size: 16px;
		margin-bottom: 16px;
		margin-top: 16px;
		display: flex;
		align-items: center;
	}
</style>