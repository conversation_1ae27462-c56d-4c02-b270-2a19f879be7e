<!--
* @Author: l<PERSON><PERSON><PERSON>g
* @Date: 2024-09-12 17:30:58
* @Description: 审计对象库详情=>
-->
<template>
	<Dialog v-model="dialogVisible" :scroll="true" title="详情" width="70%">
		<div v-loading="detailLoading">
			<el-descriptions :column="3" border>
				<el-descriptions-item label="公司名称:">{{ detailData.companyName }}</el-descriptions-item>
				<el-descriptions-item label="公司法人:">{{ detailData.legalPerson }}</el-descriptions-item>
				<el-descriptions-item label="公司类别:">{{ detailData.companyType}}</el-descriptions-item>
				<el-descriptions-item label="公司性质:">
					<dict-tag type="audit_tank_object_company" :value="detailData.comNature" />
				</el-descriptions-item>
				<el-descriptions-item label="注册资金(万元):">{{ detailData.registerCapital}}</el-descriptions-item>
				<el-descriptions-item label="注册地址:">{{ detailData.address}}</el-descriptions-item>
				<el-descriptions-item label="注册日期:">{{ formatTime(detailData.registerTime, 'yyyy-MM-dd')}}</el-descriptions-item>
				<el-descriptions-item label="联系电话:">{{ detailData.linkTel}}</el-descriptions-item>
				<el-descriptions-item label="董事长:">{{ detailData.chairmanName}}</el-descriptions-item>
				<el-descriptions-item label="总经理:">{{ detailData.managerName}}</el-descriptions-item>
				<el-descriptions-item :span="2" label="公司领导:">{{ detailData.leaders}}</el-descriptions-item>
				<el-descriptions-item :span="3" label="基本情况:">{{ detailData.basicInfo}}</el-descriptions-item>
				<el-descriptions-item :span="3" label="营业执照范围:">{{ detailData.licenseScope}}</el-descriptions-item>
				<el-descriptions-item :span="3" label="主要开展业务情况:">{{ detailData.mainBusi}}</el-descriptions-item>
				<el-descriptions-item :span="3" label="战略定位:">{{ detailData.strategy}}</el-descriptions-item>
				<el-descriptions-item :span="3" label="主责主业:">{{ detailData.mainResp}}</el-descriptions-item>
				<el-descriptions-item :span="3" label="专精特新:">{{ detailData.masteryNew}}</el-descriptions-item>
			</el-descriptions>
			<!-- <el-form ref="formRef" :model="detailData" label-width="110px" v-loading="detailLoading">
				<el-row>
					<el-col :span="8">
						<el-form-item label="公司名称">
							{{ detailData.auditRoleCode }}
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="公司法人">
							{{ detailData.auditRoleCode }}
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="公司类别">
							{{ detailData.auditRoleCode }}
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="公司性质">
							{{ detailData.auditRoleCode }}
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="注册资金(万元)">
							{{ detailData.auditRoleCode }}
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="注册地址">
							{{ detailData.auditRoleCode }}
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="注册日期">
							{{ detailData.auditRoleCode }}
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="联系电话">
							{{ detailData.auditRoleCode }}
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="董事长">
							{{ detailData.auditRoleCode }}
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="总经理">
							{{ detailData.auditRoleCode }}
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="公司领导">
							{{ detailData.auditRoleCode }}
						</el-form-item>
					</el-col>
				</el-row>
				<el-form-item label="基本情况">
					<el-input v-model="detailData.auditRoleCode" type="textarea" placeholder="请输入" readonly
						class="!w-885px" />
				</el-form-item>
				<el-form-item label="营业执照范围">
					<el-input v-model="detailData.auditRoleCode" type="textarea" placeholder="请输入" readonly
						class="!w-885px" />
				</el-form-item>
				<el-form-item label="主要开展业务情况">
					<el-input v-model="detailData.auditRoleCode" type="textarea" placeholder="请输入" readonly
						class="!w-885px" />
				</el-form-item>
				<el-form-item label="战略定位">
					<el-input v-model="detailData.auditRoleCode" type="textarea" placeholder="请输入" readonly
						class="!w-885px" />
				</el-form-item>
				<el-form-item label="主责主业">
					<el-input v-model="detailData.auditRoleCode" type="textarea" placeholder="请输入" readonly
						class="!w-885px" />
				</el-form-item>
				<el-form-item label="专精特新">
					<el-input v-model="detailData.auditRoleCode" type="textarea" placeholder="请输入" readonly
						class="!w-885px" />
				</el-form-item>
			</el-form> -->
			<el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick" v-if="typeString == 'list'">
				<el-tab-pane label="历任领导" name="0">
					<el-table :data="detailData.tankObjLeaderHisDOList" border :stripe="true" :show-overflow-tooltip="true">
						<el-table-column type="index" label="序号" width="60" align="center"/>
						<el-table-column label="姓名" align="center" prop="leaderName" width="100" />
						<el-table-column label="职务" align="center" prop="duties" min-width="100"/>
						<el-table-column label="任职开始日期" align="center" prop="startDate" min-width="100">
							<template #default="scope">
								{{ formatDate(scope.row.startDate, 'YYYY-MM-DD') }}
							</template>
						</el-table-column>
						<el-table-column label="任职结束日期" align="center" prop="endDate" min-width="100">
							<template #default="scope">
								{{ formatDate(scope.row.endDate, 'YYYY-MM-DD') }}
							</template>
						</el-table-column>
						<el-table-column label="主要责任" align="center" prop="responsibilities" min-width="180" />
						<el-table-column label="资金情况" align="center" prop="fundSituation" min-width="120"/>
						<el-table-column label="经营情况" align="center" prop="busiSituation" min-width="120"/>
						<el-table-column label="任期内主要工作" align="center" prop="mainJob" min-width="180" />
						<el-table-column label="操作" align="center" width="100">
							<template #default="scope">
								<el-button link type="primary" @click="handleWbView(scope.row)">查看</el-button>
							</template>
						</el-table-column>
					</el-table>
				</el-tab-pane>
				<el-tab-pane label="财务摘要" name="1">
					<el-table :data="detailData.tankObjFinancialSumDOList" border :stripe="true" :show-overflow-tooltip="true">
						<el-table-column type="index" label="序号" width="60" align="center"/>
						<el-table-column label="年度" align="center" prop="sumYear" min-width="120" />
						<el-table-column label="收入(万元)" align="center" prop="income" />
						<el-table-column label="利润(万元)" align="center" prop="profit" />
						<!-- <el-table-column label="操作" align="center" width="150">
							<template #default="scope">
								<el-button link type="primary" @click="handleNbView(scope.row)">查看</el-button>
							</template>
						</el-table-column> -->
					</el-table>
				</el-tab-pane>
				<el-tab-pane label="其他重点关注内容" name="2">
					<el-table :data="detailData.tankObjectFileRespVOList" border :stripe="true" :show-overflow-tooltip="true">
						<el-table-column type="index" label="序号" width="60" align="center"/>
						<el-table-column label="附件名称" align="left" prop="fileName" min-width="120" />
							<el-table-column label="上传时间" align="center" prop="createTime">
								<template #default="scope">
									{{ formatDate(scope.row.createTime)}}
								</template>
							</el-table-column>
							<el-table-column label="操作" align="center" width="150">
								<template #default="scope">
									<el-button link type="primary" @click="handleDownload(scope.row.fileUrl, scope.row.fileName)">下载</el-button>
								</template>
							</el-table-column>
					</el-table>
				</el-tab-pane>
				<el-tab-pane label="整改情况" name="3">
					<!-- <Rectifyreform ref="RectifyreformRef" /> -->
					<el-table :data="detailData.sjmxList" :stripe="true" border :show-overflow-tooltip="true">
						<el-table-column type="index" label="序号" width="60" align="center" />
						<el-table-column label="标准编号" align="left" prop="name" min-width="180" />
						<el-table-column label="标准名称" align="left" prop="name" min-width="180"/>
						<el-table-column label="制定/修正" align="center" prop="name" min-width="100"/>
						<el-table-column label="标准状态" align="center" prop="name" min-width="100"/>
						<el-table-column label="归口部门" align="left" prop="name" min-width="180"/>
						<el-table-column label="操作" align="center" width="100">
							<template #default="scope">
								<el-button link type="primary" @click="handleMxView(scope.row)">查看</el-button>
							</template>
						</el-table-column>
					</el-table>
				</el-tab-pane>
				<!-- <el-tab-pane label="开展项目" name="4">
					<el-table :data="detailData.sjmxList" :stripe="true" :show-overflow-tooltip="true">
						<el-table-column type="index" label="序号" width="60" />
						<el-table-column label="项目名称" align="left" prop="name" min-width="200" />
						<el-table-column label="项目编号" align="left" prop="name" min-width="180" />
						<el-table-column label="项目描述" align="center" prop="name" />
						<el-table-column label="项目状态" align="center" prop="name" />
						<el-table-column label="操作" align="center" width="150">
							<template #default="scope">
								<el-button link type="primary" @click="handleMxView(scope.row)">查看</el-button>
							</template>
						</el-table-column>
					</el-table>
				</el-tab-pane>
				<el-tab-pane label="关联问题" name="5">
					<el-table :data="detailData.sjmxList" :stripe="true" :show-overflow-tooltip="true">
						<el-table-column type="index" label="序号" width="60" />
						<el-table-column label="问题名称" align="center" prop="name" min-width="120" />
						<el-table-column label="问题定性分类" align="center" prop="name" />
						<el-table-column label="问题描述" align="center" prop="name" />
						<el-table-column label="关联项目" align="center" prop="name" />
						<el-table-column label="操作" align="center" width="150">
							<template #default="scope">
								<el-button link type="primary" @click="handleMxView(scope.row)">查看</el-button>
							</template>
						</el-table-column>
					</el-table>
				</el-tab-pane> -->
			</el-tabs>
			<!-- 分页 -->
			<!-- <Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
				@pagination="getList" /> -->
		</div>
	</Dialog>
	<!-- 查看 -->
	<DeteilLeaders ref="DeteilLeadersRef" />
</template>

<script lang="ts" setup>
	import { DICT_TYPE, NumberDictDataType, getIntDictOptions } from '@/utils/dict'
	import { formatTime } from '@/utils'
	import { formatDate } from '@/utils/formatTime'
	import { AuditRoleDetailVO } from '@/api/basicData/auditRole'
	import { ObjectbaseApi, ObjectVO, ObjectDetailVO } from '@/api/jobmanger/audittarget'
	import Rectifyreform from '@/views/auditManagement/auditRectifica/reformingStandingBook/index.vue'
	import DeteilLeaders from './DeteilLeaders.vue'
	import { downFile } from '@/utils/fileName'
	defineOptions({ name: 'Detail' })

	const dialogVisible = ref(false) // 弹窗的是否展示
	const detailLoading = ref(false) // 表单地加载中
	const message = useMessage()
	const detailData = ref({} as ObjectDetailVO) // 详情数据
	const queryParams = reactive({
		id: undefined,
		auditRoleName: undefined,
		pageSize: 10,
		pageNo: 1,
		projectPhase: undefined,
		date: undefined,
		status: undefined,
	})
	const queryFormRef = ref() // 搜索的表单
	const total = ref(1000)
	const typeString = ref()
	/** 打开弹窗 */
	const open = async (type: string, id: number) => {
		dialogVisible.value = true
		// 设置数据
		detailLoading.value = true
		typeString.value = type
		try {
			if(type == 'list'){
				// 审计对象列表的查看
				const row = await ObjectbaseApi.getCasebase(id)
				detailData.value = row
			}else if( type == 'item'){
				// 项目列表的查看
				const row = await ObjectbaseApi.getItemDetail(id)
				detailData.value = row
			}
		} finally {
			detailLoading.value = false
		}
	}
	defineExpose({ open }) // 提供 open 方法，用于打开弹窗

	// 文件下载
	const handleDownload = async(url: string, name: string) =>{
		await message.confirm('是否确认下载？')
		downFile(url, name)
	}

	const activeName = ref('0')
	const handleClick = () => {

	}
	// 查看详情
	const DeteilLeadersRef = ref()
	const handleWbView = (val) => {
		DeteilLeadersRef.value.open('详情',val)
	 }
	const handleNbView = () => { }
	const handleZlView = () => { }
	const handleMxView = () => { }
</script>
