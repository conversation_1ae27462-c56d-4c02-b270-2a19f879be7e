<script lang="ts" setup>
import { ref } from 'vue'
import {DICT_TYPE, getStrDictOptions} from "@/utils/dict";
import * as CatalogApi from '@/api/model/catalog';
import * as modelApi from '@/api/model/info';

const message = useMessage() // 消息弹窗

defineOptions({name: "SolidifiedModelForm"})

const formRefModel = ref() // 表单 Ref
const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const buildMethod = ref('')
const check = ref(true)

const formData = ref({
  id: undefined,
  modelCode: '',
  modelName: '',
  mainCatalogId: undefined,
  mainCatalogName: undefined,
  subCatalogId: undefined,
  subCatalogName: undefined,
  riskPointDescription: '',
  riskDataCharacteristic: '',
  modelRule: '',
  buildMethod: '',
  showMethod: '',
  modelType: '',
  isPublish: undefined,
  isSingleMonthId: undefined,
  pageLink: '',
  tableNameEn: '',
  procedureName: '',
  executeSql: '',
  scopeField:'',

})

const formRules = reactive({
  mainCatalogId: [{required: true, message: '归属目录不能为空', trigger: 'change'}],
  subCatalogId: [{required: true, message: '归属目录不能为空', trigger: 'change'}],
  modelName: [{required: true, message: '模型名称不能为空', trigger: 'blur'}],
  tableNameEn: [{required: true, message: '表名不能为空', trigger: 'blur'}],
  procedureName: [{required: true, message: '存储过程名不能为空', trigger: 'blur'}],
  riskPointDescription: [{required: true, message: '风险点描述不能为空', trigger: 'blur'}],
  riskDataCharacteristic: [{required: true, message: '风险数据特征不能为空', trigger: 'blur'}],
  modelRule: [{required: true, message: '模型规则不能为空', trigger: 'blur'}],
  buildMethod: [{required: true, message: '构建方式不能为空', trigger: 'change'}],
  pageLink: [{required: check, message: '访问链接不能为空', trigger: 'blur'}],
  executeSql: [{required: true, message: '执行SQL不能为空', trigger: 'blur'}],
  isPublish: [{required: true, message: '发布状态不能为空', trigger: 'blur'}]
})


const open = async (type: string,  modelType: string, sqlcode: string,id: number ) => {
  reset();
  formType.value = type
  dialogTitle.value = "新增固化模型";
  await getTree()
  formData.value.buildMethod = modelType === 'doubtful' ?  'fix' : 'sql'
  if(modelType === 'query') {
    dialogTitle.value = "SQL模型";
    buildMethod.value = 'sql'
    check.value = false
    formData.value.executeSql = sqlcode
  }else{
    buildMethod.value = 'fix'
  }

  if (type === 'update') {
    formLoading.value = true
    try {
      const res = await modelApi.getModelInfo(id);
      formData.value = res;
      await CatalogApi.getChildOptions(res.mainCatalogId).then(childOpts => {
        childOptions.value = childOpts;
      });
      //CatalogApi.getChildOptions(res.mainCatalogId);
      dialogTitle.value = "修改";
      dialogVisible.value = true
    } finally {
      formLoading.value = false;
    }
  }
  dialogVisible.value = true

}

defineExpose({open}) // 提供 open 方法，用于打开弹窗


/** 获取下拉框[上级菜单]的数据  */
const menuTree = ref([])
const getTree = async () => {
  menuTree.value = []
  const res = await CatalogApi.getMainTree()
  menuTree.value = res
}


const childOptions = ref([])
const getChildOptions = async (id) => {
  childOptions.value = []
  const res = await CatalogApi.getChildOptions(id)
  childOptions.value = res
}

const buildMethodOptions = [
  {
    value: 'sql',
    label: 'SQL',
  },
  {
    value: 'fix',
    label: '固化',
  }
]


const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验
  if (!formRefModel.value) return
  const valid = await formRefModel.value.validate()
  if (!valid) return
  formLoading.value = true;
  try {
    const data = formData.value;
    // 修改的提交
    if (data.id) {
      await modelApi.saveModelInfo(data);
      message.success("修改成功");
    }else{
      // 添加的提交
      await modelApi.saveModelInfo(data);
      message.success("新增成功");
    }
    emit('success')
    dialogVisible.value = false;
  } finally {
    formLoading.value = false;
  }
}

/** 表单重置 */
const reset = () => {
  formData.value = {
    id: undefined,
    modelName: '',
    mainCatalogId: undefined,
    subCatalogId: undefined,
    riskPointDescription: '',
    riskDataCharacteristic: '',
    modelRule: '',
    buildMethod: '',
    showMethod: '',
    modelType: '',
    isPublish: undefined,
    isSingleMonthId: undefined,
    pageLink: '',
    tableNameEn: '',
    procedureName: '',
    executeSql: ''
  };
  formRefModel.value?.resetFields()
}

</script>

<template>
  <div class="app-container">
    <!-- 对话框(添加 / 修改)  -->
    <Dialog :title="dialogTitle" v-model="dialogVisible" width="75%" top="2vh" max-height="75vh" append-to-body>
      <el-form ref="formRefModel" :model="formData" :rules="formRules" v-loading="formLoading" class="common-submit-form" label-width="110px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="归属目录" prop="mainCatalogId">
              <el-select v-model="formData.mainCatalogId"
                         placeholder="请选择归属目录"
                         @change="getChildOptions"
                         clearable
                         class="!w-100%">
                <el-option v-for="item in menuTree" :key="item.value" :label="item.label" :value="item.value"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="二级目录" prop="subCatalogId">
              <el-select v-model="formData.subCatalogId" placeholder="请选择子目录" clearable class="!w-100%">
                <el-option v-for="item in childOptions" :key="item.value" :label="item.label" :value="item.value"/>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="模型名称" prop="modelName">
          <el-input v-model="formData.modelName" placeholder="请输入模型名称"/>
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item v-if="buildMethod !== 'sql'" label="表名" prop="tableNameEn">
              <el-input v-model="formData.tableNameEn" placeholder="请输入表名"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="buildMethod !== 'sql'" label="存储过程名" prop="procedureName">
              <el-input v-model="formData.procedureName" placeholder="请输入存储过程名"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="风险点描述" prop="riskPointDescription">
          <el-input type="textarea" v-model="formData.riskPointDescription" :rows="2"/>
        </el-form-item>
        <el-form-item label="风险数据特征" prop="riskDataCharacteristic">
          <el-input type="textarea" v-model="formData.riskDataCharacteristic" :rows="2"/>
        </el-form-item>
        <el-form-item label="模型规则" prop="modelRule">
          <el-input type="textarea" v-model="formData.modelRule" :rows="2"/>
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="构建方式" prop="buildMethod">
              <el-select v-model="formData.buildMethod" placeholder="请选择构建方式" :disabled="true" class="!w-240px">
                <el-option v-for="item in buildMethodOptions" :key="item.value" :label="item.label" :value="item.value"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发布状态" prop="isPublish">
              <el-radio-group v-model="formData.isPublish">
                <el-radio v-for="dict in getStrDictOptions(DICT_TYPE.COMMON_IS_OR_STATUS)" :key="dict.value" :label="dict.value">
                  {{ dict.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item v-if="buildMethod !== 'sql'" label="是否单账期" prop="isSingleMonthId">
              <el-radio-group v-model="formData.isSingleMonthId">
                <el-radio v-for="dict in getStrDictOptions(DICT_TYPE.COMMON_IS_OR_STATUS)" :key="dict.value" :label="dict.value">
                  {{ dict.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item v-if="buildMethod !== 'sql'" label="访问链接" prop="pageLink">
          <el-input v-model="formData.pageLink" placeholder="请输入访问链接"/>
        </el-form-item>
        <el-form-item v-if="buildMethod === 'sql'" label="SQL语句" prop="executeSql">
          <el-input type="textarea" :rows="3" v-model="formData.executeSql" placeholder="请输入SQL"/>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm" :disabled="formLoading">保 存</el-button>
          <el-button @click="dialogVisible = false">取 消</el-button>
        </div>
      </template>
    </Dialog>
  </div>

</template>

<style scoped lang="scss">

</style>
