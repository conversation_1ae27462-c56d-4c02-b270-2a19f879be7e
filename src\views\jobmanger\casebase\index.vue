<!--
* @Author: lijunliang
* @Date: 2024-09-12 16:25:50
* @Description: 审计案例库=>
-->
<template>
	<ContentWrap class="common-card-search">
		<!-- 搜索工作栏 -->
		<el-form class="-mb-15px common-search-form" :model="queryParams" ref="queryFormRef" :inline="true" label-width="110px">
			<el-form-item label="案例编码" prop="caseNo">
				<el-input v-model="queryParams.caseNo" placeholder="请输入案例编码" clearable @keyup.enter="handleQuery"
					class="!w-200px" />
			</el-form-item>
			<el-form-item label="案例名称" prop="caseName">
				<el-input v-model="queryParams.caseName" placeholder="请输入案例名称" clearable @keyup.enter="handleQuery"
					class="!w-200px" />
			</el-form-item>
			<el-form-item label="适用审计事项" prop="auditScope">
				<!-- <el-tree-select
					v-model="queryParams.suitAudit"
					:data="mattersAll"
					check-strictly
					filterable
					placeholder="请选择事项名称"
					:render-after-expand="false"
					class="!w-200px"
				/> -->
				<el-input v-model="queryParams.auditScope" placeholder="请输入事项名称" clearable @keyup.enter="handleQuery"
					class="!w-200px" />
			</el-form-item>
			<el-form-item label="填报人" prop="writeBy">
				<!-- <el-select v-model="queryParams.writeById" filterable placeholder="请输入填报人" class="!w-200px">
					<el-option
						v-for="item in userData"
						:key="item.id"
						:label="item.nickname"
						:value="item.id"
					/>
				</el-select> -->
				<el-input v-model="queryParams.writeBy" placeholder="请输入填报人" clearable @keyup.enter="handleQuery"
					class="!w-200px" />
			</el-form-item>
			<el-form-item label="填报单位" prop="writeDept">
				<!-- <el-tree-select
					v-model="queryParams.writeDept"
					ref="treeRef"
					clearable
					filterable
					placeholder="请选择填报单位"
					:data="deptList"
					check-strictly
					:expand-on-click-node="false"
					:check-on-click-node="true"
					:default-expand-all="false"
					highlight-current
					node-key="id"
					@node-click="handleNodeClick"
					:load="loadNode"
					:default-expanded-keys="defaultExpandedKeys"
					:filter-node-method="filterNode"
					lazy
					class="!w-200px"
					>
					<template #default="{ data: { name } }">{{ name }}</template>
				</el-tree-select> -->
				<el-input v-model="queryParams.writeDept" placeholder="请输入填报单位" clearable @keyup.enter="handleQuery"
					class="!w-200px" />
			</el-form-item>
			<el-form-item label="共享状态" prop="shareFlag">
				<el-select v-model="queryParams.shareFlag" placeholder="请选择共享状态" clearable class="!w-200px">
					<el-option v-for="dict in getIntDictOptions('shared_state')" :key="dict.value"
						:label="dict.label" :value="dict.value" />
				</el-select>
			</el-form-item>
			<el-form-item label="优秀案例" prop="outstandFlag">
				<el-select v-model="queryParams.outstandFlag" placeholder="请选择优秀案例" clearable class="!w-200px">
					<el-option v-for="dict in getIntDictOptions('excellent_case')" :key="dict.value"
						:label="dict.label" :value="dict.value" />
				</el-select>
			</el-form-item>
			<el-form-item label="案例状态" prop="status">
				<el-select v-model="queryParams.status" placeholder="请选择案例状态" clearable class="!w-200px">
					<el-option v-for="dict in getIntDictOptions('case_status')" :key="dict.value"
						:label="dict.label" :value="dict.value" />
				</el-select>
			</el-form-item>
		</el-form>
		<div class="right-search-btn">
			<el-button  type="primary"  @click="handleQuery">
					<Icon icon="ep:search" class="mr-5px" />搜索
				</el-button>
				<el-button @click="resetQuery">
					<Icon icon="ep:refresh" class="mr-5px" />重置
				</el-button>
		</div>
	</ContentWrap>
	<!-- 列表 -->
	<ContentWrap>
    <div class="button_margin15">
				<el-button v-hasPermi="['audit:tank-case-lib:create']" type="primary" plain @click="openForm('create')">
					<Icon icon="ep:plus" class="mr-5px" />新增审计案例
				</el-button>
				<el-button v-hasPermi="['audit:tank-case-lib:export']" :loading="exportLoading" plain
					@click="handleExport">
					<Icon class="mr-5px" icon="ep:download" />
					导出
				</el-button>
      </div>
		<el-table v-loading="loading" :data="list" :stripe="true" border :show-overflow-tooltip="true">
			<el-table-column label="序号" width="60" align="center">
				<template
					#default="{ $index }">{{ (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1 }}</template>
			</el-table-column>
			<el-table-column label="案例编码" align="left" prop="caseNo" min-width="180" />
			<el-table-column label="案例名称" align="left" prop="caseName" min-width="180"/>
			<el-table-column label="适用审计事项" align="left" prop="auditScope" min-width="180"/>
			<el-table-column label="填报人员" align="center" prop="writeBy" min-width="100"/>
			<el-table-column label="填报单位" align="left" prop="writeDept" min-width="180"/>
			<el-table-column label="共享状态" align="center" prop="shareFlag" min-width="100">
				<template #default="scope">
					<dict-tag type="shared_state" :value="scope.row.shareFlag" />
				</template>
			</el-table-column>
			<el-table-column label="优秀案例" align="center" prop="outstandFlag" min-width="100">
				<template #default="scope">
					<dict-tag type="excellent_case" :value="scope.row.outstandFlag" />
				</template>
			</el-table-column>
			<el-table-column label="案例状态" align="center" prop="status" min-width="100">
				<template #default="scope">
					<dict-tag type="case_status" :value="scope.row.status" />
				</template>
			</el-table-column>
			<el-table-column label="操作" align="center" width="250" fixed="right">
				<template #default="scope">
					<el-button v-hasPermi="['audit:tank-case-lib:get']" v-if="scope.row.status == 1" link type="primary" @click="openDetailForm(scope.row.id)">查看</el-button>
					<el-button v-hasPermi="['audit:tank-case-lib:update']" v-if="scope.row.status !== 1" link type="primary" @click="openForm('update', scope.row.id)">编辑</el-button>
					<el-button v-hasPermi="['audit:tank-case-lib:on-off']" v-if="scope.row.status !== 1" link type="primary" @click="handleStatus(scope.row,1)">启用</el-button>
					<el-button v-hasPermi="['audit:tank-case-lib:on-off']" v-if="scope.row.status == 1" link type="danger" @click="handleStatus(scope.row,2)">停用</el-button>
					<el-button v-hasPermi="['audit:tank-case-lib:delete']" v-if="scope.row.status !== 1" link type="danger" @click="handleDelete(scope.row.id)">删除</el-button>
				</template>
			</el-table-column>
		</el-table>
		<!-- 分页 -->
		<Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
			@pagination="getList" />
	</ContentWrap>

	<!-- 表单弹窗：添加/修改 -->
	<Form ref="formRef" @success="getList" @getTree="getTree" />
	<!-- 查看详情弹窗 -->
	<Detail ref="detailRef" />
</template>

<script setup lang="ts">
	import { getIntDictOptions, getStrDictOptions, DICT_TYPE, NumberDictDataType } from '@/utils/dict'
	import download from '@/utils/download'
	import { AuditRoleApi, AuditRoleVO, AuditRoleDetailVO } from '@/api/basicData/auditRole'
	import { ItemLibraryApi } from '@/api/jobmanger/itemLibrary'
	import { CasebaseApiNew, CasebaseNew } from '@/api/jobmanger/casebase'
	import Form from './form.vue'
	import { formatTime } from '@/utils'
	import Detail from './Detail.vue'
import { ca } from 'element-plus/es/locale'
	import { handleTree } from '@/utils/tree'
import { log } from 'console'

	import * as DeptApi from '@/api/system/dept'
	import { defaultProps, handleLazyTree } from '@/utils/tree'
	import { TimeSplicing } from '@/utils/formatTime'
	defineOptions({ name: 'Casebase‌‌' })

	const message = useMessage() // 消息弹窗
	const { t } = useI18n() // 国际化
	const { query } = useRoute() //接收路由传参
	const loading = ref(true) // 列表的加载中
	const list = ref<AuditRoleVO[]>([]) // 列表的数据
	const mattersAll = ref<any[]>([]) //审计事项数据
	const queryParams = reactive({
		id: undefined,
		auditRoleName: undefined,
		pageSize: 10,
		pageNo: 1,
		projectPhase: undefined,
		date: undefined,

		caseNo: undefined,
		caseName: undefined,
		suitAudit: [],
		writeBy: undefined,
		writeById: undefined,
		writeDept: undefined,
		writeDeptId: undefined,
		shareFlag: undefined,
		outstandFlag: undefined,
		status: undefined,
	})
	const queryFormRef = ref() // 搜索的表单
	const exportLoading = ref(false) // 导出的加载中
	const total = ref(0)
	const userData = ref([]) //填报人员信息

	/** 查询列表 */
	const getList = async () => {
		loading.value = true
		try {
			const data = await CasebaseApiNew.getCasebaseList(queryParams)
			list.value = data.list
			total.value = data.total
		} finally {
			loading.value = false
		}
	}

	const detailRef = ref()
	const openDetailForm = (id: number) => {
		detailRef.value.open(id)
	}

	/** 搜索按钮操作 */
	const handleQuery = () => {
		queryParams.pageNo = 1
		getList()
	}

	/** 重置按钮操作 */
	const resetQuery = () => {
		queryFormRef.value.resetFields()
		handleQuery()
	}

	/** 添加/修改操作 */
	const formRef = ref()
	const openForm = (type : string, id ?: number) => {
		formRef.value.open(type, id)
	}
	// const res = await AuditRoleApi.getMatters()
	// console.log(res)

	/** 删除按钮操作 */
	const handleDelete = async (id: number) => {
		try {
			// 删除的二次确认
			await message.delConfirm()
			// 发起删除
			await CasebaseApiNew.deleteCasebaseNew(id)
			message.success(t('common.delSuccess'))
			// 刷新列表
			await getList()
		} catch { }
	}

	/** 启用/停用按钮操作*/
	const handleStatus = async(row: any, num: number) => {
		try {
			// 修改状态的二次确认-草稿0/启用1/停用2
			const text = row.status === 2 ? '启用' : '停用'
			await message.confirm('请确认' + text + '"' + row.caseName + '"，如果您不想'+ text +'此数据，请点击“取消”')
			row.status = num
			// 发起修改
			await CasebaseApiNew.editCasebaseNew(row)
			message.success(t('common.updateSuccess'))
			// 刷新列表
			await getList()
		} catch { }
	}

	/** 导出按钮操作 */
	const handleExport = async () => {
		try {
			// 导出的二次确认
			await message.exportConfirm()
			// 发起导出
			exportLoading.value = true
			const data = await CasebaseApiNew.exportCasebaseNew(queryParams)
			const time = TimeSplicing(new Date())
    		download.excel(data, `审计案例库${time}.xls`)
		} catch {
		} finally {
			exportLoading.value = false
		}
	}

	/** 获取全部审计事项 */
	const getAudit = async () =>{
		try {
			const res = await ItemLibraryApi.getMatterslist()
			mattersAll.value.push(...handleTree(res, 'id', 'parentId'))

		} catch{}
	}

	/** 查询所有用户信息(填报人员) */
	const getUser = async () => {
		loading.value = true
		try {
			const data = await CasebaseApiNew.getUserData()
			userData.value = data
		} finally {
			loading.value = false
		}
	}

	// 填报单位树结构
	interface DeptNode {
		id: number
		masterOrgId?: number | string
		name: string
		parentId: number | string
		children?: DeptNode[]
		isLeaf?: boolean
	}
	const deptList = ref<DeptNode[]>([]) // 树形结构
	const defaultExpandedKeys = ref<(string | number)[]>([])
	const getTree = async (id) => {
		deptList.value = []
		const res = await DeptApi.getSimpleDeptList(id)
		deptList.value.push(...handleLazyTree(res, 'id', 'parentId'))
		defaultExpandedKeys.value = deptList.value.map((node) => node.id)
		console.log(deptList.value)
	}
	const loadNode = async (node: Node, resolve: (data: any[]) => void) => {
		try {
			const nodeId = node.data.id
			if (nodeId == undefined || nodeId == null) {
				return
			}
			const res = await DeptApi.getSimpleDeptList(nodeId)
			const children = handleLazyTree(res, 'id', 'parentId', 'children')
			resolve(children)
		} catch (error) {
			resolve([])
		}
	}

	//智库首页跳转-查询详情
	const projectId = ref({id: query.id as unknown as number})
	const getDateil = async() => {
		if(projectId.value.id){
			await openDetailForm(projectId.value.id)
			// 使用一次后销毁传参
			const newUrl = await window.location.href.replace(/\?.*$/, '') //获取当前路径，并去除参数
			await history.replaceState({}, '', newUrl) //更新浏览器历史记录，不触发页面重新加载
		}
	}

	/** 初始化 **/
	onMounted(() => {
		getList()
		getUser()
		getAudit()
		getTree(0)
		getDateil()
	})
</script>
