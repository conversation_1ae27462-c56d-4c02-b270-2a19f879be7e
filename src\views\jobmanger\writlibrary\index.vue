<!--
* @Author: lijunliang
* @Date: 2024-09-12 14:25:50
* @Description: 审计文书库=>
-->
<template>
	<el-row :gutter="16">
		<el-col :span="5" :xs="24">
			<ContentWrap class="h-1/1">
				<JobmangerLeftTree @node-click="handleDeptNodeClick" :createUrl="createUrl" :editUrl="editUrl"
					:searchUrl="searchUrl" :delUrl="delUrl" :detailsUrl="detailsUrl" :type='2' :showBut='true' />
			</ContentWrap>
		</el-col>
		<el-col :span="19" :xs="24">
			<ContentWrap class="common-card-search">
				<!-- 搜索工作栏 -->
				<el-form class="-mb-15px common-search-form" :model="queryParams" ref="queryFormRef" :inline="true" label-width="110px">
					<el-form-item label="文书名称" prop="docName">
						<el-input v-model="queryParams.docName" placeholder="请输入文书名称" clearable
							@keyup.enter="handleQuery" class="!w-200px" />
					</el-form-item>
					<el-form-item label="文书编码" prop="docNo">
						<el-input v-model="queryParams.docNo" placeholder="请输入文书编码" clearable @keyup.enter="handleQuery"
							class="!w-200px" />
					</el-form-item>
					<el-form-item label="所属公司" prop="companyName">
						<el-input v-model="queryParams.companyName" placeholder="请输入所属公司" clearable
							@keyup.enter="handleQuery" class="!w-200px" />
					</el-form-item>
					<el-form-item label="功能描述" prop="docDesc">
						<el-input v-model="queryParams.docDesc" placeholder="请输入功能描述" clearable
							@keyup.enter="handleQuery" class="!w-200px" />
					</el-form-item>
					<el-form-item label="所属模块" prop="ofModuleName">
						<el-input v-model="queryParams.ofModuleName" placeholder="请输入所属模块" clearable
							@keyup.enter="handleQuery" class="!w-200px" />
					</el-form-item>
					<el-form-item label="文书状态" prop="status">
						<el-select v-model="queryParams.status" placeholder="请选择文书状态" clearable class="!w-200px">
							<el-option v-for="dict in getIntDictOptions('audit_document_status')"
								:key="dict.value" :label="dict.label" :value="dict.value" />
						</el-select>
					</el-form-item>
				</el-form>
		<div class="right-search-btn">
			<el-button  type="primary"  @click="handleQuery">
					<Icon icon="ep:search" class="mr-5px" />搜索
				</el-button>
				<el-button @click="resetQuery">
					<Icon icon="ep:refresh" class="mr-5px" />重置
				</el-button>
		</div>
			</ContentWrap>
			<!-- 列表 -->
			<ContentWrap>
        <div class="button_margin15">
						<el-button v-hasPermi="['audit:tank-doc-info:create']" type="primary" plain @click="openForm('create')">
							<Icon icon="ep:plus" class="mr-5px" />新增文书库
						</el-button>
						<el-button v-hasPermi="['audit:tank-doc-info:export']" :loading="exportLoading" plain
							@click="handleExport">
							<Icon class="mr-5px" icon="ep:download" />
							导出
						</el-button>
          </div>
				<el-table v-loading="loading" :data="list" :stripe="true" border :show-overflow-tooltip="true">
					<el-table-column label="序号" width="60" align="center">
						<template
							#default="{ $index }">{{ (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1 }}</template>
					</el-table-column>
					<el-table-column label="文书名称" align="left" prop="docName" min-width="260" />
					<el-table-column label="文书编码" align="left" prop="docNo" min-width="180"/>
					<el-table-column label="所属公司" align="left" prop="companyName" min-width="260"/>
					<el-table-column label="所属模块" align="left" prop="ofModuleName" min-width="260"/>
					<el-table-column label="模版版本" align="left" prop="docDesc"  min-width="260"/>
					<el-table-column label="功能描述" align="left" prop="docDesc"  min-width="260"/>
					<el-table-column label="文书状态" align="center" prop="status"  min-width="100">
						<template #default="scope">
							<dict-tag type="audit_document_status" :value="scope.row.status" />
						</template>
					</el-table-column>
					<el-table-column label="操作" align="center" width="220" fixed="right">
						<template #default="scope">
							<el-button v-hasPermi="['audit:tank-doc-info:get']" v-show="scope.row.status == 1" link type="primary" @click="openDetailForm(scope.row.id)">查看</el-button>
							<el-button v-hasPermi="['audit:tank-doc-info:update']" v-show="scope.row.status !== 1" link type="primary" @click="openForm('update', scope.row.id)">编辑</el-button>
							<el-button v-hasPermi="['audit:tank-doc-info:on-off']" v-show="scope.row.status == 0 || scope.row.status == 2" link type="primary"
								@click="handleStatus(scope.row)">启用</el-button>
							<el-button v-hasPermi="['audit:tank-doc-info:on-off']" v-show="scope.row.status == 1" link type="danger"
								@click="handleStatus(scope.row)">停用</el-button>
							<el-button v-hasPermi="['audit:tank-doc-info:delete']" v-show="scope.row.status !== 1" link type="danger" @click="handleDelete(scope.row.id)">删除</el-button>
						</template>
					</el-table-column>
				</el-table>
				<!-- 分页 -->
				<Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
					@pagination="getList" />
			</ContentWrap>
		</el-col>
	</el-row>

	<!-- 表单弹窗：添加/修改 -->
	<Form ref="formRef" @success="getList" />
	<!-- 查看详情弹窗 -->
	<Detail ref="detailRef" />
</template>

<script setup lang="ts">
	import { getIntDictOptions, getStrDictOptions, DICT_TYPE, NumberDictDataType } from '@/utils/dict'
	// import { handleTree } from '@/utils/tree'
	import download from '@/utils/download'
	import { WritlibraryApi, WritlibraryVO, WritlibraryDetailVO } from '@/api/jobmanger/writlibrary'
	import Form from './form.vue'
	import { formatTime } from '@/utils'
	import Detail from './Detail.vue'
	import JobmangerLeftTree from '@/views/jobmanger/itemLibrary/components/JobmangerLeftTree.vue'
	import { TimeSplicing } from '@/utils/formatTime'
	defineOptions({ name: 'Writlibrary‌‌' })

	const message = useMessage() // 消息弹窗
	const { t } = useI18n() // 国际化
	const { query,} = useRoute() //接收路由传参4
	const { replace } = useRouter() //路由跳转
	const loading = ref(true) // 列表的加载中
	const list = ref<WritlibraryVO[]>([]) // 列表的数据
	const queryParams = reactive({
		pageSize: 10,
		pageNo: 1,
		docName: undefined,
		docNo: undefined,
		companyName: undefined,
		docDesc: undefined,
		status: undefined,
		ofModuleId: undefined,
	})
	const queryFormRef = ref() // 搜索的表单
	const exportLoading = ref(false) // 导出的加载中
	const total = ref(0)
	const searchUrl = '/audit/tank-trees-type/get?type=2'
	const createUrl = '/audit/tank-trees-type/create'
	const editUrl = '/audit/tank-trees-type/update'
	const delUrl = '/audit/tank-trees-type/delete'
	const detailsUrl = '/audit/tank-trees-type/get-tree'
	/** 查询列表 */
	const getList = async () => {
		loading.value = true
		try {
			const data = await WritlibraryApi.getWritlibraryList(queryParams)
			list.value = data.list
			total.value = data.total
			// list.value = handleTree(data, 'id', 'id')
		} finally {
			loading.value = false
		}
	}

	const detailRef = ref()
	const openDetailForm = (id : number) => {
		detailRef.value.open(id)
	}

	/** 搜索按钮操作 */
	const handleQuery = () => {
		queryParams.pageNo = 1
		getList()
	}

	/** 重置按钮操作 */
	const resetQuery = () => {
		queryFormRef.value.resetFields()
		handleQuery()
	}

	/** 处理树形被点击 */
	const handleDeptNodeClick = async (row) => {
		queryParams.ofModuleId = row.id
		await getList()
	}

	/** 添加/修改操作 */
	const formRef = ref()
	const openForm = (type : string, id ?: number) => {
		formRef.value.open(type, id, queryParams.ofModuleId)
	}

	// 启用停用
	const handleStatus = async (row : WritlibraryVO) => {

		try {
			// 修改状态的二次确认-草稿0/启用1/停用2
			const text = row.status === 2 ? '启用' : '停用'
			await message.confirm('请确认' + text + '"' + row.docName + '"，如果您不想'+ text +'此数据，请点击“取消”')
			// 发起删除
			await WritlibraryApi.changeWritlibrary(row.id)
			message.success('操作成功')
		} catch {

		} finally {
			// 刷新列表
			await getList()
		}
	}

	/** 删除按钮操作 */
	const handleDelete = async (id : number) => {
		try {
			// 删除的二次确认
			await message.delConfirm()
			// 发起删除
			await WritlibraryApi.deleteWritlibrary(id)
			message.success(t('common.delSuccess'))
			// 刷新列表
			await getList()
		} catch { }
	}

	/** 导出按钮操作 */
	const handleExport = async () => {
		try {
			// 导出的二次确认
			await message.exportConfirm()
			// 发起导出
			exportLoading.value = true
			const data = await WritlibraryApi.exportWritlibrary(queryParams)
			const time = TimeSplicing(new Date())
    		download.excel(data, `审计文书库${time}.xls`)
		} catch {
		} finally {
			exportLoading.value = false
		}
	}

	//智库首页跳转-查询详情
	const projectId = ref({id: query.id as unknown as number})
	const getDateil = async() => {
		if(projectId.value.id){
			await openDetailForm(projectId.value.id)
			// 使用一次后销毁传参
			const newUrl = await window.location.href.replace(/\?.*$/, '') //获取当前路径，并去除参数
			await history.replaceState({}, '', newUrl) //更新浏览器历史记录，不触发页面重新加载
		}
	}

	/** 初始化 **/
	onMounted(() => {
		getList()
		getDateil()
	})
</script>
