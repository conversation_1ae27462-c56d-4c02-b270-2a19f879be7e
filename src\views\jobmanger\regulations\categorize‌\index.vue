<!--
* @Author: li<PERSON>liang
* @Date: 2024-09-11 17:22:50
* @Description: 法律法规分类=>
-->
<template>
	<el-row :gutter="16">
		<el-col :span="6" :xs="24">
			<ContentWrap class="h-1/1">
				<el-tabs v-model="activeName" class="demo-tabs" @tab-change="handleClick">
					<el-tab-pane label="内部规章制度分类" name="1" />
					<el-tab-pane label="法律法规分类" name="2" />
				</el-tabs>
				<JobmangerLeftTree ref="JobmangerLeftTreeRef" @node-click="handleDeptNodeClick" :searchUrl="searchUrl"
					:showEdit='false' />
			</ContentWrap>
		</el-col>
		<el-col :span="18" :xs="24">
			<ContentWrap class="common-card-search">
				<!-- 搜索工作栏 -->
				<el-form class="-mb-15px common-search-form" :model="queryParams" ref="queryFormRef" :inline="true" label-width="68px">
					<el-form-item label="分类名称" prop="typeName">
						<el-input v-model="queryParams.typeName" placeholder="请输入分类名称" clearable
							@keyup.enter="handleQuery" class="!w-240px" />
					</el-form-item>
					<el-form-item label="分类编码" prop="typeCode">
						<el-input v-model="queryParams.typeCode" placeholder="请输入分类编码" clearable
							@keyup.enter="handleQuery" class="!w-240px" />
					</el-form-item>
					<el-form-item label="分类描述" prop="typeDesc">
						<el-input v-model="queryParams.typeDesc" placeholder="请输入分类描述" clearable
							@keyup.enter="handleQuery" class="!w-240px" />
					</el-form-item>
				</el-form>
		<div class="right-search-btn">
			<el-button  type="primary"  @click="handleQuery">
					<Icon icon="ep:search" class="mr-5px" />搜索
				</el-button>
				<el-button @click="resetQuery">
					<Icon icon="ep:refresh" class="mr-5px" />重置
				</el-button>
		</div>
			</ContentWrap>
			<!-- 列表 -->
			<ContentWrap>
        <div class="button_margin15">
          <el-button type="primary" plain @click="openForm('create')">
            <Icon icon="ep:plus" class="mr-5px" />新增
          </el-button>
          <el-button :loading="exportLoading" plain
            @click="handleExport">
            <Icon class="mr-5px" icon="ep:download" />
            导出
          </el-button>
        </div>
				<el-table v-loading="loading" :data="list" border :stripe="true" :show-overflow-tooltip="true">
					<el-table-column label="序号" width="60" align="center">
						<template
							#default="{ $index }">{{ (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1 }}</template>
					</el-table-column>
					<el-table-column label="分类名称" align="left" prop="typeName" min-width="180" />
					<el-table-column label="分类编码" align="left" prop="typeCode" min-width="150" />
					<el-table-column label="上级分类" align="center" prop="parentName" min-width="100" />
					<el-table-column label="分类描述" align="left" prop="typeDesc" min-width="180"/>
					<el-table-column label="操作" align="center" width="150" fixed="right">
						<template #default="scope">
							<el-button link type="primary" @click="openForm('update', scope.row.id)">编辑</el-button>
							<el-button link type="danger" @click="handleDelete(scope.row.id)">删除</el-button>
						</template>
					</el-table-column>
				</el-table>
				<!-- 分页 -->
				<Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
					@pagination="getList" />
			</ContentWrap>
		</el-col>
	</el-row>

	<!-- 表单弹窗：添加/修改 -->
	<Form ref="formRef" @success="getFormList" />
</template>

<script setup lang="ts">
	import download from '@/utils/download'
	import { CategorizeApi, CategorizeVO } from '@/api/jobmanger/regulations/categorize‌'
	import Form from './form.vue'
	import JobmangerLeftTree from '@/views/jobmanger/components/JobmangerLeftTree.vue'
	defineOptions({ name: 'Categorize‌‌' })
	import { TimeSplicing } from '@/utils/formatTime'

	const message = useMessage() // 消息弹窗
	const { t } = useI18n() // 国际化

	const loading = ref(true) // 列表的加载中
	const list = ref<CategorizeVO[]>([]) // 列表的数据
	const queryParams = reactive({
		pageSize: 10,
		pageNo: 1,
		typeName: undefined,
		typeCode: undefined,
		typeDesc: undefined,
		parentId: undefined,
		treeOfType: 1,
	})
	const queryFormRef = ref() // 搜索的表单
	const exportLoading = ref(false) // 导出的加载中
	const total = ref(0)
	const searchUrl = ref('/audit/tank-trees-type/get?type=1')
	const JobmangerLeftTreeRef = ref()
	const activeName = ref('1')
	const handleClick = async (type) => {
		activeName.value = type
		if (type == '1') {
			queryParams.treeOfType = 1
			await (searchUrl.value = '/audit/tank-trees-type/get?type=1')
			JobmangerLeftTreeRef.value.getTree()
		} else {
			queryParams.treeOfType = 3
			await (searchUrl.value = '/audit/tank-trees-type/get?type=3')
			JobmangerLeftTreeRef.value.getTree()
		}
		await getList()
	}
	/** 查询列表 */
	const getList = async () => {
		loading.value = true
		try {
			const data = await CategorizeApi.getCategorizeList(queryParams)
			list.value = data.list
			total.value = data.total
		} finally {
			loading.value = false
		}
	}

	const getFormList = () => {
		getList()
		JobmangerLeftTreeRef.value.getTree()
	}

	/** 搜索按钮操作 */
	const handleQuery = () => {
		queryParams.pageNo = 1
		getList()
	}

	/** 重置按钮操作 */
	const resetQuery = () => {
		queryFormRef.value.resetFields()
		handleQuery()
	}

	/** 处理部门被点击 */
	const handleDeptNodeClick = async (row) => {
		queryParams.parentId = row.id
		await getList()
	}

	/** 添加/修改操作 */
	const formRef = ref()
	const openForm = (type : string,id?:number) => {
		formRef.value.open(type, queryParams.treeOfType, queryParams.parentId,id)
	}

	/** 删除按钮操作 */
	const handleDelete = async (id : number) => {
		try {
			// 删除的二次确认
			await message.delConfirm()
			// 发起删除
			await CategorizeApi.deleteCategorize(id)
			message.success(t('common.delSuccess'))
			// 刷新列表
			await getFormList()
		} catch { }
	}

	/** 导出按钮操作 */
	const handleExport = async () => {
		try {
			// 导出的二次确认
			await message.exportConfirm()
			// 发起导出
			exportLoading.value = true
			const data = await CategorizeApi.exportCategorize(queryParams)
			const time = TimeSplicing(new Date())
    		download.excel(data, `法律法规分类${time}.xls`)
		} catch {
		} finally {
			exportLoading.value = false
		}
	}

	/** 初始化 **/
	onMounted(() => {
		getList()
	})
</script>
