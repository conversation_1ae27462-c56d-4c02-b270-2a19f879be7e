<!--
* @Author: li<PERSON><PERSON>g
* @Date: 2024-10-22 17:10:50
* @Description: 工程项目总览=>
-->
<template>
	<ContentWrap class="common-card-search">
		<!-- 搜索工作栏 -->
		<el-form class="-mb-15px common-search-form" :model="queryParams" ref="queryFormRef" :inline="true" label-width="110px">
			<el-form-item label="项目名称" prop="projectName">
				<el-input v-model="queryParams.projectName" placeholder="请输入项目名称" clearable @keyup.enter="handleQuery"
					class="!w-200px" />
			</el-form-item>
			<el-form-item label="投资单位" prop="proOwnersName">
				<el-input v-model="queryParams.proOwnersName" placeholder="请输入投资单位" clearable @keyup.enter="handleQuery"
					class="!w-200px" />
			</el-form-item>
			<el-form-item label="管理单位" prop="company">
				<el-input v-model="queryParams.company" placeholder="请输入管理单位" clearable @keyup.enter="handleQuery"
					class="!w-200px" />
			</el-form-item>
			<el-form-item label="合同名称" prop="subcontractname">
				<el-input v-model="queryParams.subcontractname" placeholder="请输入合同名称" clearable @keyup.enter="handleQuery"
					class="!w-200px" />
			</el-form-item>
			<el-form-item label="工程名称" prop="title">
				<el-input v-model="queryParams.title" placeholder="请输入工程名称" clearable @keyup.enter="handleQuery"
					class="!w-200px" />
			</el-form-item>
			<el-form-item label="施工单位" prop="partyb">
				<el-input v-model="queryParams.partyb" placeholder="请输入施工单位" clearable @keyup.enter="handleQuery"
					class="!w-200px" />
			</el-form-item>
			<el-form-item v-show="activeName=='2'"  label="控制价审计单位" prop="zjjgname">
				<el-input v-model="queryParams.zjjgname" placeholder="请输入控制价审计单位" clearable
					@keyup.enter="handleQuery" class="!w-200px" />
			</el-form-item>
			<el-form-item v-show="activeName=='1'"  label="结算审计单位" prop="zjjgname">
				<el-input v-model="queryParams.zjjgname" placeholder="请输入结算审计单位" clearable @keyup.enter="handleQuery"
					class="!w-200px" />
			</el-form-item>
		</el-form>
		<div class="right-search-btn">
			<el-button type="primary" @click="handleQuery">
				<Icon icon="ep:search" class="mr-5px" />搜索
			</el-button>
			<el-button @click="resetQuery">
				<Icon icon="ep:refresh" class="mr-5px" />重置
			</el-button>
		</div>
	</ContentWrap>

	<!-- 列表 -->
	<ContentWrap>
		<!-- <el-button :loading="exportLoading" plain @click="handleExport">
			<Icon class="mr-5px" icon="ep:download" />
			导出
		</el-button> -->
		<el-tabs v-model="activeName" class="demo-tabs" @tab-change="handleClick">
			<el-tab-pane label="结算审计" name="1" />
			<el-tab-pane label="控制价审计" name="2" />
		</el-tabs>
		<!-- 结算审计 -->
		<el-table v-show="activeName=='1'" border v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
			<el-table-column label="序号" width="60" align="center">
				<template #default="{ $index }">{{ (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1
					}}</template>
			</el-table-column>
			<el-table-column label="项目名称" align="left" prop="projectName" min-width="180" />
			<el-table-column label="投资单位" align="left" prop="proOwnersName" min-width="180"/>
			<el-table-column label="管理单位" align="left" prop="company" min-width="180"/>
			<el-table-column label="合同名称" align="left" prop="subcontractname" min-width="180"/>
			<el-table-column label="合同金额（元）" align="center" prop="subcontractamount" min-width="130" />
			<el-table-column label="施工单位" align="left" prop="partyb" min-width="180" />
			<el-table-column label="工程名称" align="left" prop="title" min-width="180"/>
			<el-table-column label="结算审计单位" align="center" prop="zjjgname" min-width="150"/>
			<el-table-column label="结算报审时间" align="center" prop="sjbsendhdbdate" min-width="150">
				<template #default="scope">
					<span>{{ formatTime(scope.row.sjbsendhdbdate, 'yyyy-MM-dd') }}</span>
				</template>
			</el-table-column>
			<el-table-column label="结算报审值(万元)" align="center" prop="settlementamount" min-width="150"/>
			<el-table-column label="结算审定时间" align="center" prop="sjbsenddate" min-width="150">
				<template #default="scope">
					<span>{{ formatTime(scope.row.sjbsenddate, 'yyyy-MM-dd') }}</span>
				</template>
			</el-table-column>
			<el-table-column label="结算审定值(万元)" align="center" prop="jsdwJsmoney" min-width="150"/>
			<el-table-column label="审减(万元)" align="center" prop="zjjgSjzj" min-width="120"/>
			<el-table-column label="结算审计发现的主要问题" align="left" prop="sjyy" min-width="180"/>
		</el-table>
		<!-- 控制价审计 -->
		<el-table v-show="activeName=='2'" border v-loading="loading" :data="list1" :stripe="true" :show-overflow-tooltip="true">
			<el-table-column label="序号" width="60" align="center">
				<template #default="{ $index }">{{ (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1
					}}</template>
			</el-table-column>
			<el-table-column label="项目名称" align="left" prop="projectName" min-width="180" />
			<el-table-column label="投资单位" align="left" prop="proOwnersName" min-width="180"/>
			<el-table-column label="管理单位" align="left" prop="company" min-width="180"/>
			<el-table-column label="工程名称" align="left" prop="name" min-width="180"/>
			<el-table-column label="招投控制价编制单位" align="left" prop="bzdw" min-width="180"/>
			<el-table-column label="招投控制价报审时间" align="center" prop="sjbsenddate" min-width="160">
				<template #default="scope">
					<span>{{ formatTime(scope.row.sjbsenddate, 'yyyy-MM-dd') }}</span>
				</template>
			</el-table-column>
			<el-table-column label="招投控制价报审金额" align="center" prop="settlementamount" min-width="160"/>
			<el-table-column label="招投控制价审计单位" align="left" prop="zjjgname" min-width="180"/>
			<el-table-column label="招投控制价审定时间" align="center" prop="sjbsendhdbdate" min-width="160">
				<template #default="scope">
					<span>{{ formatTime(scope.row.sjbsendhdbdate, 'yyyy-MM-dd') }}</span>
				</template>
			</el-table-column>
			<el-table-column label="招投控制价审定金额" key="jtsjJsmoney" align="center" min-width="160" />
			<el-table-column label="招投控制价审计调整率" key="sjtzl" align="center" min-width="170" />
		</el-table>
		<!-- 分页 -->
		<Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
			@pagination="getList" />
	</ContentWrap>
</template>

<script setup lang="ts">
import { ProjectOverviewApi, ProjectOverviewVO, ProjectOverviewSettleVO } from '@/api/decision/engineering/projectOverview'
import { formatTime } from '@/utils'
import download from '@/utils/download'
defineOptions({ name: 'ProjectOverview' })
const message = useMessage() // 消息弹窗
const loading = ref(true) // 列表的加载中
const list = ref<ProjectOverviewSettleVO[]>([]) // 列表的数据
const list1 = ref<ProjectOverviewVO[]>([]) // 列表的数据
const queryParams = reactive({
	pageSize: 10,
	pageNo: 1,
	zjjgname: undefined,
	bzdw: undefined,
	proOwnersName: undefined,
	publishDate: [],
	startDate: undefined,
	endDate: undefined,
	title: undefined,	// 工程名称
	projectName: undefined,	//	项目名称
	company: undefined,	//	管理单位
	partyb: undefined,	//	施工单位
	subcontractname: undefined,	//	合同名称
})
const queryFormRef = ref() // 搜索的表单
const total = ref(0)
const activeName = ref('1')
const handleClick = async (type) => {
	console.log(type);
	activeName.value = type
	await getList()
}
/** 查询列表 */
const getList = async () => {
	loading.value = true
	try {
		if(activeName.value == '1'){
			// 结算审计
			const data = await ProjectOverviewApi.getProjectOverviewSettleList(queryParams)
			list.value = data.list
			total.value = data.total
		}else{
			// 控制价审计
			const data = await ProjectOverviewApi.getProjectOverviewList(queryParams)
			list1.value = data.list
			total.value = data.total
		}
	} finally {
		loading.value = false
	}
}
/** 搜索按钮操作 */
const handleQuery = () => {
	queryParams.pageNo = 1
	getList()
}
/** 重置按钮操作 */
const resetQuery = () => {
	queryFormRef.value.resetFields()
	handleQuery()
}
const exportLoading = ref(false) // 导出的加载中
/** 导出按钮操作 */
const handleExport = async () => {
	try {
		// 导出的二次确认
		await message.exportConfirm()
		// 发起导出
		exportLoading.value = true
		// const data = await ItemLibraryApi.exportItemLibrary(queryParams)
		// download.excel(data, '审计事项库.xls')
	} catch {
	} finally {
		exportLoading.value = false
	}
}
/** 初始化 **/
onMounted(() => {
	getList()
})
</script>
