<!--
* @Author: li<PERSON><PERSON>g
* @Date: 2024-09-12 16:47:50
* @Description: 审计对象库=>
-->
<template>
	<el-row :gutter="16">
		<el-col :span="6" :xs="24">
			<ContentWrap class="h-1/1">
				<!-- 左侧树结构 -->
				<TreeList @node-click="handleDeptNodeClick" searchUrl="/system/dept/get-dept-by-userid" />
			</ContentWrap>
		</el-col>
		<el-col :span="18" :xs="24">
			<ContentWrap class="common-card-search">
				<!-- 搜索工作栏 -->
				<el-form class="-mb-15px common-search-form" :model="queryParams" ref="queryFormRef" :inline="true" label-width="110px">
					<el-form-item label="公司名称" prop="companyName">
						<el-input v-model="queryParams.companyName" placeholder="请输入公司名称" clearable @keyup.enter="handleQuery" class="!w-200px" />
					</el-form-item>
					<el-form-item label="公司法人" prop="legalPerson">
						<el-input v-model="queryParams.legalPerson" placeholder="请输入公司法人" clearable @keyup.enter="handleQuery" class="!w-200px" />
					</el-form-item>
				</el-form>
				<div class="right-search-btn">
					<el-button  type="primary"  @click="handleQuery">
						<Icon icon="ep:search" class="mr-5px" />
						搜索
					</el-button>
					<el-button @click="resetQuery">
						<Icon icon="ep:refresh" class="mr-5px" />
						重置
					</el-button>
				</div>
			</ContentWrap>
			<!-- 列表 -->
			<ContentWrap>
        		<div class="button_margin15">
					<el-button v-hasPermi="['audit:tank-object:create']" type="primary" plain @click="dialogVisible = true">
						<Icon icon="ep:plus" class="mr-5px" />
						新增审计对象
					</el-button>
					<el-button v-hasPermi="['audit:tank-object:export']" :loading="exportLoading" plain @click="handleExport">
						<Icon class="mr-5px" icon="ep:download" />
						导出
					</el-button>
         		 </div>
				<el-table v-loading="loading" :data="list" :stripe="true" border :show-overflow-tooltip="true">
					<el-table-column label="序号" width="60" align="center" >
						<template
							#default="{ $index }">{{ (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1 }}</template>
					</el-table-column>
					<el-table-column label="公司名称" align="left" prop="companyName" min-width="180" />
					<el-table-column label="公司法人" align="left" prop="legalPerson" min-width="180"/>
					<el-table-column label="基本情况" align="left" prop="basicInfo"  min-width="180"/>
					<el-table-column label="更新时间" align="center" prop="updateTime" min-width="180">
						<template #default="scope">
							{{ formatDate(scope. row.updateTime) }}
						</template>
					</el-table-column>
					<el-table-column label="操作" align="center" width="200" fixed="right">
						<template #default="scope">
							<el-button v-hasPermi="['audit:proj-plan-project:list']" link type="primary" @click="openDetailList(scope.row.companyId)">项目列表</el-button>
							<el-button v-hasPermi="['audit:tank-object:get']" link type="primary" @click="openDetailForm(scope.row.id)">查看</el-button>
							<el-button v-hasPermi="['audit:tank-object:update']" link type="primary" @click="openForm('update', scope.row, scope.row)">编辑</el-button>
						</template>
					</el-table-column>
				</el-table>
				<!-- 分页 -->
				<Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
					@pagination="getList" />
			</ContentWrap>
		</el-col>
	</el-row>

	<!-- 新增选择部门 -->
	<Dialog title="选择部门" :scroll="true" v-model="dialogVisible" width="45%">
		<TreeList @node-click="DeptNodeClickItem" searchUrl="/system/dept/get-dept-by-userid?id=" />
	</Dialog>

	<!-- 表单弹窗：添加/修改 -->
	<Form ref="formRef" @success="getList" />
	<!-- 查看详情弹窗 -->
	<Detail ref="detailRef" />
	<!-- 查看项目列表弹框 -->
	<AudList ref="lsitRef" />
</template>

<script setup lang="ts">
	import { getIntDictOptions, getStrDictOptions, DICT_TYPE, NumberDictDataType } from '@/utils/dict'

	import { handleTree } from '@/utils/tree'
	import download from '@/utils/download'
	import { ObjectbaseApi, ObjectVO, ObjectDetailVO } from '@/api/jobmanger/audittarget'
	import Form from './form.vue'
	import { formatTime } from '@/utils'
	import Detail from './Detail.vue'
	import JobmangerLeftTree from '@/views/jobmanger/components/JobmangerLeftTree.vue'
	import TreeList from './TreeList.vue'
	import AudList from './audList.vue'
	import * as JobmangerLeftTreeApi from '@/api/jobmanger/jobmangerLeftTree'
	import { formatDate, TimeSplicing } from '@/utils/formatTime'
	defineOptions({ name: 'Audittarget‌‌' })

	const message = useMessage() // 消息弹窗
	const { t } = useI18n() // 国际化
	const dialogVisible = ref(false)
	const { query } = useRoute() //接收路由传参
	const loading = ref(true) // 列表的加载中
	const list = ref<ObjectDetailVO[]>([]) // 列表的数据
	const queryParams = reactive({
		id: undefined,
		pageSize: 10,
		pageNo: 1,
		companyId: undefined, //部门id
		companyName: undefined, //公司名称
		legalPerson: undefined,//公司法人
	})
	const queryFormRef = ref() // 搜索的表单
	const exportLoading = ref(false) // 导出的加载中
	const total = ref(0)
	/** 查询列表 */
	const getList = async () => {
		dialogVisible.value = false
		loading.value = true
		try {
			const data = await ObjectbaseApi.getCasebaseList(queryParams)
			list.value = data.list
			total.value = data.total
			// list.value = handleTree(data, 'id', 'id')
		} finally {
			loading.value = false
		}
	}

	// 获取当前登陆人所在部门
	const getCompny = async() =>{
		const res =  await JobmangerLeftTreeApi.getJobLeftTreeList('/system/dept/get-dept-by-userid')
		if(res && Array.isArray(res) && res.length > 0){
			queryParams.companyId = res[0].companyId
		}
	}

	// 项目列表操作按钮
	const lsitRef = ref()
	const openDetailList = (id: any) => {
		lsitRef.value.open(id)
	}

	// 查看操作按钮
	const detailRef = ref()
	const openDetailForm = (id: number) => {
		detailRef.value.open('list' ,id)
	}

	/** 搜索按钮操作 */
	const handleQuery = () => {
		queryParams.pageNo = 1
		getList()
	}

	/** 重置按钮操作 */
	const resetQuery = () => {
		// queryParams.companyId = undefined
		queryFormRef.value.resetFields()
		handleQuery()
	}

	/** 处理部门被点击 */
	const handleDeptNodeClick = async (row) => {
		queryParams.companyId = row.id
		await getList()
	}

	// 点击新增打开部门树结构选择
	const DeptNodeClickItem =  async (row) => {
		await openForm('create',row)
	}

	/** 添加/修改操作 */
	const formRef = ref()
	const openForm = (type : string, row:any, id ?: number) => {
		formRef.value.open(type, row, id)
	}

	/** 删除按钮操作 */
	const handleDelete = async (id : number) => {
		try {
			// 删除的二次确认
			await message.delConfirm()
			// 发起删除
			await ObjectbaseApi.deleteObjectbase(id)
			message.success(t('common.delSuccess'))
			// 刷新列表
			await getList()
		} catch { }
	}

	/** 导出按钮操作 */
	const handleExport = async () => {
		try {
			// 导出的二次确认
			await message.exportConfirm()
			// 发起导出
			exportLoading.value = true
			const data = await ObjectbaseApi.exportObjectbase(queryParams)
			const time = TimeSplicing(new Date())
    		download.excel(data, `审计对象库${time}.xls`)
		} catch {
		} finally {
			exportLoading.value = false
		}
	}

	//智库首页跳转-查询详情
	const projectId = ref({id: query.id as unknown as number})
	const getDateil = async() => {
		if(projectId.value.id){
			await openDetailForm(projectId.value.id)
			// 使用一次后销毁传参
			const newUrl = await window.location.href.replace(/\?.*$/, '') //获取当前路径，并去除参数
			await history.replaceState({}, '', newUrl) //更新浏览器历史记录，不触发页面重新加载
		}
	}

	/** 初始化 **/
	onMounted( async() => {
		await getCompny()
		await getList()
		getDateil()
	})
</script>
