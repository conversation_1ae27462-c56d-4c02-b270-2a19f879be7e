<!--
* @Author: li<PERSON>liang
* @Date: 2024-09-11 11:31:32
* @Description: 审计文书库表单=>
-->
<template>
	<Dialog :title="dialogTitle" v-model="dialogVisible" width="65%">
		<el-form ref="formRef" class="common-submit-form" :model="formData" :rules="formRules" v-loading="formLoading">
			<el-row>
				<!-- <el-col :span="8"> -->
					<el-form-item label="文书名称" prop="docName">
						<el-input v-model="formData.docName" placeholder="请输入文书名称" class="!w-240px" />
					</el-form-item>
				<!-- </el-col> -->
				<!-- <el-col :span="8"> -->
					<el-form-item label="文书编码" prop="docNo">
						<el-input v-model="formData.docNo" placeholder="自动生成" disabled class="!w-240px" />
					</el-form-item>
				<!-- </el-col> -->
				<!-- <el-col :span="8"> -->
					<el-form-item label="所属模块" prop="ofModuleId">
						<el-tree-select v-model="formData.ofModuleId" placeholder="请选择所属模块" :data="typeOptions"
							:render-after-expand="false" check-strictly filterable node-key="id" style="width: 240px">
							<template #default="{ data: { name } }">{{ name }}</template>
						</el-tree-select>
					</el-form-item>

					<el-form-item label="所属公司" prop="companyId">
						<el-tree-select
							v-model="formData.companyId"
							ref="treeRef"
							clearable
							filterable
							placeholder="请选择所属公司"
							:data="deptList"
							check-strictly
							:expand-on-click-node="false"
							:check-on-click-node="true"
							:default-expand-all="false"
							highlight-current
							node-key="id"
							@node-click="handleNodeClick"
							:load="loadNode"
							:default-expanded-keys="defaultExpandedKeys"
							:filter-node-method="filterNode"
							lazy
							class="!w-240px"
							>
							<template #default="{ data: { name } }">{{ name }}</template>
						</el-tree-select>
					</el-form-item>
				<!-- </el-col> -->
			</el-row>
			<el-form-item label="功能描述" prop="docDesc">
				<el-input v-model="formData.docDesc" type="textarea" placeholder="请输入" class="!w-950px" />
			</el-form-item>
			<div class="del_hover">
				<el-form-item label="模版列表" prop="tankDocInfoFileReqVOList">
					<el-button type="primary" plain @click="handleImport('docx')">
						<Icon icon="ep:upload" class="mr-5px" /> 选择文件
					</el-button>
				</el-form-item>
			</div>
			<el-table :data="formData.tankDocInfoFileReqVOList" border :stripe="true" :show-overflow-tooltip="true" max-height="500">
				<el-table-column label="版本号" align="center" prop="version" width="120" />
				<el-table-column label="文档名称" align="left" prop="fileName" min-width="120">
					<template #default="scope">
						<el-link :underline="false" type="primary" @click="handleView('预览','VIEW', scope.row.id)">{{ scope.row.fileName }}</el-link>
					</template>
				</el-table-column>
				<el-table-column label="创建人" align="center" prop="creatorName" width="120" />
				<el-table-column label="创建时间" align="center" prop="createTime" width="180">
					<template #default="scope">
						<span>{{ formatDate(scope.row.createTime) }}</span>
					</template>
				</el-table-column>
				<el-table-column label="状态" align="center" prop="status" width="120">
					<template #default="scope">
						<el-switch v-model="scope.row.status" inline-prompt :active-value="1" :inactive-value="0" active-text="有效" inactive-text="无效" @change="getChangData(scope.row, scope.$index)" />
					</template>
				</el-table-column>
				<el-table-column label="操作" align="center" width="150">
					<template #default="scope">
						<el-button link type="primary" @click="handleDownload(scope.row.fileUrl,scope.row.fileName)">下载</el-button>
						<el-button link type="danger" @click="listDelete('tankDocInfoFileReqVOList', scope.$index)">删除</el-button>
					</template>
				</el-table-column>
			</el-table>
		</el-form>

		<template #footer>
			<el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
			<el-button @click="dialogVisible = false">取 消</el-button>
		</template>

	</Dialog>

	<!-- 文件上传 -->
	<FileForm ref="formImgRef" @success="handleUploadSuccess" :type="fileType" :limit="fileLimit" />
	<!-- 预览附件 -->
	<DialogFlie ref="DialogFlieRef" />
</template>
<script setup lang="ts">
	import { getIntDictOptions, getStrDictOptions, DICT_TYPE } from '@/utils/dict'
	import { WritlibraryApi, WritlibraryVO } from '@/api/jobmanger/writlibrary'
	import FileForm from '@/views/infra/file/FileForm.vue'
	import { CACHE_KEY, useCache } from '@/hooks/web/useCache'
	import { formatDate } from '@/utils/formatTime'
	import * as JobmangerLeftTreeApi from '@/api/jobmanger/jobmangerLeftTree'
	import { handleTree } from '@/utils/tree'
	import { downFile } from '@/utils/fileName'
	import * as DeptApi from '@/api/system/dept'
	import { defaultProps, handleLazyTree } from '@/utils/tree'

	/** 审计角色 表单 */
	defineOptions({ name: 'WritForm' })

	const { t } = useI18n() // 国际化
	const message = useMessage() // 消息弹窗
	const { wsCache } = useCache()

	const dialogVisible = ref(false) // 弹窗的是否展示
	const dialogTitle = ref('') // 弹窗的标题
	const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
	const formType = ref('') // 表单的类型：create - 新增；update - 修改
	const formData = ref({
		id: undefined,
		docName: undefined,
		docNo: undefined,
		status: undefined,
		docDesc: undefined,
		ofModuleId: undefined,
		tankDocInfoFileReqVOList: [],
		companyId: undefined, //所属公司
	})
	const formRules = reactive({
		docName: [{ required: true, message: '文书名称不能为空', trigger: 'blur' }, {
			pattern: /^[\u4e00-\u9fa5]*$/,
			message: '文书名称只能中文',
			trigger: 'blur'
		}],
		ofModuleId: [{ required: true, message: '所属模块不能为空', trigger: 'change' }]
	})
	const formRef = ref() // 表单 Ref
	const typeOptions = ref([])

	const formImgRef = ref()
	const fileType = ref('docx')
	const fileLimit = ref(1)
	// 上传方法
	const handleImport = (type : string) => {
		fileType.value = type
		// fileLimit.value = type === 'file' ? 5 : 1
		formImgRef.value.open()
	}
	const handleUploadSuccess = (fileList) => {
		if (fileList && fileList.length > 0) {
			let currentVersion = 'V1.0'

			fileList.forEach((item, index) => {
				console.log(item.response)
				let res = item.response.data
				if (formData.value.tankDocInfoFileReqVOList.length === 0) {
					currentVersion = 'V1.0'
				} else {
					// 获取最后一个文件的版本号
					const lastFileVersion = formData.value.tankDocInfoFileReqVOList[0].version

					const versionParts = lastFileVersion.replace('V', '').split('.').map(Number)
					let major = versionParts[0]
					let minor = (versionParts[1] || 0) + 1
					console.log(minor)

					// 检查是否需要进位到下一个大版本
					if (minor >= 10) {
						major += 1
						minor = 0
					}

					currentVersion = `V${major}.${minor}`
				}
				// 创建新的文件对象并设置版本号
				const fileObj = {
					id: res.id, // 假设每个文件对象都有一个 'id' 属性
					version: currentVersion,
					fileName: res.fileName,
					fileUrl: res.fileUrl,
					status: index === formData.value.tankDocInfoFileReqVOList.length - 1 ? 1 : 0,
					creatorName: res.creatorName,
					createTime: res.createTime
				}
				// 追加到 tankDocInfoFileReqVOList 数组
				formData.value.tankDocInfoFileReqVOList.unshift(fileObj)
				formData.value.tankDocInfoFileReqVOList.forEach((file, index) => {
					file.status = index === 0 ? 1 : 0
				})
				console.log(formData.value.tankDocInfoFileReqVOList)
			})
		}
	}

	// 填报单位树结构
	interface DeptNode {
		id: number
		masterOrgId?: number | string
		name: string
		parentId: number | string
		children?: DeptNode[]
		isLeaf?: boolean
	}
	const deptList = ref<DeptNode[]>([]) // 树形结构
	const defaultExpandedKeys = ref<(string | number)[]>([])
	const getTree = async (id) => {
		deptList.value = []
		const res = await DeptApi.getSimpleDeptList(id)
		deptList.value.push(...handleLazyTree(res, 'id', 'parentId'))
		defaultExpandedKeys.value = deptList.value.map((node) => node.id)
		console.log(deptList.value)
	}
	const loadNode = async (node: Node, resolve: (data: any[]) => void) => {
		try {
			const nodeId = node.data.id
			if (nodeId == undefined || nodeId == null) {
				return
			}
			const res = await DeptApi.getSimpleDeptList(nodeId)
			const children = handleLazyTree(res, 'id', 'parentId', 'children')
			resolve(children)
		} catch (error) {
			resolve([])
		}
	}

	/** 打开弹窗 */
	const open = async (type : string, id ?: number, ofModuleId?: any) => {
		dialogVisible.value = true
		dialogTitle.value = t('action.' + type)
		formType.value = type
		typeOptions.value = []
		resetForm()
		const res = await JobmangerLeftTreeApi.getJobLeftTreeList('/audit/tank-trees-type/get?type=' + 2)
		typeOptions.value.push(...handleTree(res, 'id', 'parentId'))
		formData.value.ofModuleId = ofModuleId || undefined
		//获取填报单位
		await getTree(0)
		// 修改时，设置数据
		if (id) {
			formLoading.value = true
			try {
				formData.value = await WritlibraryApi.getWritlibrary(id)
				formData.value.tankDocInfoFileReqVOList.forEcah(item => {
					item.fileName = item.docName
				})
			} finally {
				formLoading.value = false
			}
		}
	}
	defineExpose({ open }) // 提供 open 方法，用于打开弹窗

	/*
	* @Author: lijunliang
	* @Date: 2024-09-12 11:27:52
	* @Description: 删除=>
	*/
	const listDelete = async (type : string, index : number = 0) => {
		await message.delConfirm()
		formData.value[type].splice(index, 1)
		await message.success(t('common.delSuccess'))
	}
	/*
	* @Author: lijunliang
	* @Date: 2024-09-12 11:26:36
	* @Description: 下载=>
	*/
	const handleDownload = async(url : string, name: string) => {
		// 下载的二次确认
    	await message.confirm('是否确认下载？')
		downFile(url, name)
	}

	// 预览
	const DialogFlieRef = ref()
	const handleView = async (name : string, type : string, id : number) => {
		await DialogFlieRef.value.open(name, type, id)
	}

	/** 提交表单 */
	const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
	const submitForm = async () => {
		// 校验表单
		await formRef.value.validate()
		// 提交请求
		formLoading.value = true
		try {
			const data = formData.value as unknown as WritlibraryVO
			if (formType.value === 'create') {
				await WritlibraryApi.createWritlibrary(data)
				message.success(t('common.createSuccess'))
			} else {
				await WritlibraryApi.updateWritlibrary(data)
				message.success(t('common.updateSuccess'))
			}
			dialogVisible.value = false
			// 发送操作成功的事件
			emit('success')
		} finally {
			formLoading.value = false
		}
	}

	/** 重置表单 */
	const resetForm = () => {
		formData.value = {
			id: undefined,
			docName: undefined,
			docNo: undefined,
			status: undefined,
			docDesc: undefined,
			ofModuleId: undefined,
			tankDocInfoFileReqVOList: [],
			companyId: undefined,
		}
		formRef.value?.resetFields()
	}
</script>