<!--
* @Author: li<PERSON>liang
* @Date: 2024-10-23 10:30:58
* @Description: 单位详情列表=>
-->
<template>
	<Dialog v-model="dialogVisible" :loading="loading" :scroll="true" title="详情列表" width="75%">
		<el-tabs v-model="activeName" class="demo-tabs" @tab-change="handleClick">
			<el-tab-pane label="初审" name="1" />
			<el-tab-pane label="复审" name="2" />
		</el-tabs>
		<el-button :loading="exportLoading" plain @click="handleExport">
			<Icon class="mr-5px" icon="ep:download" />
			导出
		</el-button>
		<el-table border v-show="activeName=='1'" :data="detailData" :stripe="true" :show-overflow-tooltip="true" >
			<el-table-column label="序号" type="index" width="60" align="center"/>
			<el-table-column label="工程名称" align="left" prop="engineeringName" min-width="180"/>
			<el-table-column label="投资单位" align="left" prop="investorName" min-width="180"/>
			<el-table-column label="管理单位" prop="managerName" align="left" min-width="180" />
			<el-table-column label="施工单位" align="left" prop="constructorName" min-width="180" />
			<el-table-column label="合同金额" align="center" prop="contractAmount" min-width="150" />
			<el-table-column label="施工单位报审金额" align="center" prop="constructionCompanyAudit" min-width="180" />
			<el-table-column label="管理单位初审金额" align="center" prop="managementUnitFirstAudit" min-width="180" />
			<el-table-column label="一审单位" align="left" prop="auditorName" min-width="180"/>
			<el-table-column label="一审单位初审金额" align="center" prop="firstAuditUnitInitialAuditAmount" min-width="180" />
			<el-table-column label="一审单位审定金额" align="center" prop="firstAuditUnitFinalAuditAmount" min-width="180" />
			<el-table-column label="审减值" align="center" prop="auditReductionValue" />
			<el-table-column label="审减率" align="center" prop="auditReductionRate" />
		</el-table>
		<el-table border v-show="activeName=='2'" :data="detailData" :stripe="true" :show-overflow-tooltip="true" >
			<el-table-column label="序号" type="index" width="60" align="center"/>
			<el-table-column label="工程名称" align="left" prop="engineeringName" key="0" min-width="180"/>
			<el-table-column label="投资单位" align="left" prop="investorName" key="1" min-width="180"/>
			<el-table-column label="管理单位" prop="managerName" align="left" min-width="180" key="2"/>
			<el-table-column align="center"  key="3">
				<template #header>
					<div class="flex items-center" style="justify-content:center">
						<span >一审数据</span>
						<el-link class="ml-10px" type="primary" :underline="false" @click="showHide=!showHide">{{showHide==false?'展开其他数据':'收起'}}</el-link>
					</div>
				</template>
				<el-table-column v-if="showHide" key="4" label="合同金额" align="center" prop="contractAmount" min-width="150"/>
				<el-table-column v-if="showHide" key="5" label="施工单位" align="left" prop="constructorName" min-width="180"/>
				<el-table-column v-if="showHide" key="6" label="施工单位报审金额" align="center" prop="constructionCompanyAudit" min-width="180" />
				<el-table-column v-if="showHide" key="7" label="管理单位初审金额" align="center" prop="managementUnitFirstAudit" min-width="180" />
				<el-table-column label="一审单位" align="left" prop="auditorName" min-width="180"/>
				<el-table-column label="一审单位初审金额" align="center" prop="firstAuditUnitInitialAuditAmount" min-width="180" />
				<el-table-column v-if="showHide" key="8" label="一审单位审定金额" align="center" prop="firstAuditUnitFinalAuditAmount" min-width="180" />
			</el-table-column>
			<el-table-column label="复审单位" align="left" prop="reviewUnit" min-width="180" />
			<el-table-column label="复审单位初审金额" align="center" prop="reviewUnitInitialAuditAmount" min-width="180" />
			<el-table-column label="复审单位审定金额" align="center" prop="reviewUnitFinalAuditAmount" min-width="180" />
			<el-table-column label="审减值" align="center" prop="auditReductionValue" />
			<el-table-column label="审减率" align="center" prop="auditReductionRate">
				<template #default="scope">
					{{scope.row.auditReductionRate?scope.row.auditReductionRate + "%" : '0%'}}
				</template>
			</el-table-column>
		</el-table>
		<!-- 分页 -->
		<Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
			@pagination="getList" />
	</Dialog>
</template>

<script lang="ts" setup>
import { AuditEfficiencyDetailsVO, ReasonDeductioneApi } from '@/api/decision/engineering/projectSettlement/reason'
import download from '@/utils/download'
defineOptions({ name: 'Detail' })

const queryParams = reactive({
	pageSize: 10,
	pageNo: 1,
	questionName: undefined,
	auditSequence: '初审',
})
const dialogVisible = ref(false) // 弹窗的是否展示
const loading = ref(true) // 列表的加载中
const detailData = ref([]) // 详情数据
const showHide = ref(false) // 列表显示隐藏
const activeName = ref('1')
const total = ref(0)
const message = useMessage() // 消息弹窗
const handleClick = async (type) => {
	if(type == 1){
		queryParams.auditSequence = '初审'
	}else if(type == 2){
		queryParams.auditSequence = '复审'
	}
	await getList()
}

/** 打开弹窗 */
const open = async (id?: number, qusName: string) => {
	queryParams.questionName = qusName
	dialogVisible.value = true
	// 设置数据
	loading.value = true
	await getList()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

// 数据详情列表
const getList = async() => {
	// 设置数据
	loading.value = true
	try {
		const data = await ReasonDeductioneApi.getReasonDeductioneDetails(queryParams)
		detailData.value = data.list
		total.value = data.total
	} finally {
		loading.value = false
	}
}

const exportLoading = ref(false) // 导出的加载中
/** 导出按钮操作 */
const handleExport = async () => {
	try {
		// 导出的二次确认
		await message.exportConfirm()
		// 发起导出
		exportLoading.value = true
		if(queryParams.auditSequence == '初审') {
			const data = await ReasonDeductioneApi.exportReasonDeductioneDetails(queryParams)
			download.excel(data, '初审-审减原因.xls')
		}else if(queryParams.auditSequence == '复审') {
			const data = await ReasonDeductioneApi.exportReasonDeductioneDetailsTwo(queryParams)
			download.excel(data, '复审-审减原因.xls')
		}
	} catch {
	} finally {
		exportLoading.value = false
	}
}
</script>
