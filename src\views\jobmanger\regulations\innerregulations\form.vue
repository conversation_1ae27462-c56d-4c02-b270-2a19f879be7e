<!--
* @Author: li<PERSON>liang
* @Date: 2024-09-11 13:48:53
* @Description: 内部规章制度表单=>
-->
<template>
	<Dialog :title="dialogTitle" v-model="dialogVisible" width="70%">
		<el-form class="common-submit-form" ref="formRef" :model="formData" :rules="formRules" v-loading="formLoading">
			<el-row>
				<!-- <el-col :span="8"> -->
					<el-form-item label="公司名称" prop="companyId">
						<el-tree-select
							v-model="formData.companyId"
							ref="treeRef"
							filterable
							clearable
							placeholder="请选择公司名称"
							:data="deptList"
							check-strictly
							:expand-on-click-node="false"
							:check-on-click-node="true"
							:default-expand-all="false"
							highlight-current
							node-key="id"
							@node-click="handleNodeClickCorporation"
							:load="loadNode"
							:default-expanded-keys="defaultExpandedKeys"
							:filter-node-method="filterNode"
							lazy
							class="!w-240px"
							>
							<template #default="{ data: { name } }">{{ name }}</template>
						</el-tree-select>
					</el-form-item>
				<!-- </el-col> -->
				<!-- <el-col :span="8"> -->
					<el-form-item label="标准编号" prop="standarNum">
						<el-input v-model="formData.standarNum" placeholder="请输入标准编号" class="!w-240px" />
					</el-form-item>
				<!-- </el-col> -->
				<!-- <el-col :span="8"> -->
					<el-form-item label="分类" prop="typeId" label-width="70px">
						<el-tree-select
							v-model="formData.typeId"
							:data="mattersList"
							clearable
							check-strictly
							:default-expanded-keys="defaultExpandedKeysType"
							placeholder="请选择分类"
							:render-after-expand="false"
							class="!w-240px"
						/>
					</el-form-item>
				<!-- </el-col> -->
				<!-- <el-col :span="8"> -->
					<el-form-item label="标准体系" prop="standardsysCode">
						<el-select v-model="formData.standardsysCode" placeholder="请选择" clearable class="!w-240px">
							<el-option v-for="dict in getIntDictOptions('institutionalsy_stem')" :key="dict.value"
								:label="dict.label" :value="dict.value" />
						</el-select>
					</el-form-item>
				<!-- </el-col> -->
				<!-- <el-col :span="8"> -->
					<el-form-item label="标准名称" prop="standarName">
						<el-input v-model="formData.standarName" placeholder="请输入标准名称" class="!w-240px" />
					</el-form-item>
				<!-- </el-col> -->
				<!-- <el-col :span="8"> -->
					<el-form-item label="执行日期" prop="exeDate">
						<el-date-picker v-model="formData.exeDate" type="date" value-format="x" format="YYYY-MM-DD" clearable placeholder="选择某一天"
							class="!w-240px" />
					</el-form-item>
				<!-- </el-col> -->
				<!-- <el-col :span="8"> -->
					<el-form-item label="归口部门" prop="deptId">
						<el-tree-select
							v-model="formData.deptId"
							ref="treeRef"
							filterable
							clearable
							placeholder="请选择归口部门"
							:data="deptList"
							check-strictly
							:expand-on-click-node="false"
							:check-on-click-node="true"
							:default-expand-all="false"
							highlight-current
							node-key="id"
							@node-click="handleNodeClickSection"
							:load="loadNode"
							:default-expanded-keys="defaultExpandedKeys"
							:filter-node-method="filterNode"
							lazy
							class="!w-240px"
							>
							<template #default="{ data: { name } }">{{ name }}</template>
						</el-tree-select>
					</el-form-item>
				<!-- </el-col> -->
				<!-- <el-col :span="8"> -->
					<el-form-item label="修订/制订" prop="amend">
						<el-select v-model="formData.amend" placeholder="请选择修订/制订" clearable class="!w-240px">
							<el-option v-for="dict in getIntDictOptions('internalre_vision')" :key="dict.value"
								:label="dict.label" :value="dict.value" />
						</el-select>
					</el-form-item>
				<!-- </el-col> -->
				<!-- <el-col :span="8"> -->
					<el-form-item label="标准状态" prop="status">
						<el-select v-model="formData.status" placeholder="请选择标准状态" clearable class="!w-240px">
							<el-option v-for="dict in getIntDictOptions('Internalst_andard_state')" :key="dict.value"
								:label="dict.label" :value="dict.value" />
						</el-select>
					</el-form-item>
				<!-- </el-col> -->
				<el-col :span="24">
					<el-form-item label="备注" prop="remark">
						<el-input v-model="formData.remark" type="textarea" placeholder="请输入" />
					</el-form-item>
				</el-col>
				<el-col :span="24" class="del_hover">
					<el-form-item label="附件" prop="fileList">
						<el-button type="primary" plain @click="handleImport('file')">
							<Icon icon="ep:upload" class="mr-5px" /> 上传文件
						</el-button>
					</el-form-item>
				</el-col>
				<!-- <el-col :span="24"> -->
					<!-- <el-form-item> -->
						<el-table :data="formData.fileList" border :stripe="true" :show-overflow-tooltip="true">
							<el-table-column label="文档名称" align="left" prop="fileName" min-width="120"/>
							<el-table-column label="创建人" align="center" prop="creatorName" />
							<el-table-column label="创建时间" key="creationtime" align="center">
								<template #default="{row}">
									<span>{{ formatDate(row.createTime) }}</span>
								</template>
							</el-table-column>
							<el-table-column label="操作" align="center" width="160">
								<template #default="{row}">
									<el-button link type="primary" @click="handleView('预览','VIEW',row.id)">预览</el-button>
									<el-button link type="primary" @click="handleDownload(row?.fileUrl, row?.fileName)">下载</el-button>
									<el-button link type="danger" @click="listDelete( row.id)">删除</el-button>
								</template>
							</el-table-column>
						</el-table>
					<!-- </el-form-item> -->
				<!-- </el-col> -->
			</el-row>
		</el-form>
		<template #footer>
			<el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
			<el-button @click="dialogVisible = false">取 消</el-button>
		</template>
	</Dialog>
	<!-- 文件上传 -->
	<FileForm ref="formImgRef" @success="handleUploadSuccess" :type="fileType" />
	<!-- 预览附件 -->
	<DialogFlie ref="DialogFlieRef" />
</template>
<script setup lang="ts">
	import { getIntDictOptions, getStrDictOptions, DICT_TYPE } from '@/utils/dict'
	import { RulesRegulationsApi, RulesRegulationsVO, RulesRegulationsDetailVO } from '@/api/jobmanger/regulations/innerregulations'
	import FileForm from '@/views/infra/file/FileForm.vue'
	import { formatDate } from '@/utils/formatTime'
	import * as DeptApi from '@/api/system/dept'
	import { defaultProps, handleLazyTree, handleTree } from '@/utils/tree'
	import { downFile } from '@/utils/fileName'

	/** 审计角色 表单 */
	defineOptions({ name: 'InnerregulationsForm' })

	const { t } = useI18n() // 国际化
	const message = useMessage() // 消息弹窗

	const dialogVisible = ref(false) // 弹窗的是否展示
	const dialogTitle = ref('') // 弹窗的标题
	const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
	const formType = ref('') // 表单的类型：create - 新增；update - 修改
	interface fileVO {
		id : number
		fileName : string
		fileState : string
		version : string
	}
	const formData = ref({
		id: undefined,
		auditRoleName: undefined,
		parentId: undefined,
		sort: undefined,
		status: undefined,
		projectPhase: undefined,
		companyName: undefined,
		deptName: undefined,
		attachments: [],
		fileList: [],
		typeId: undefined,
	})
	const formRules = reactive({
		companyId: [{ required: true, message: '公司名称不能为空', trigger: 'blur' }],
		standarNum: [{ required: true, message: '标准编号不能为空', trigger: 'blur' }],
		typeId: [{ required: true, message: '分类不能为空', trigger: 'blur' }],
		standardsysCode: [{ required: true, message: '标准体系不能为空', trigger: 'blur' }],
		standarName: [{ required: true, message: '标准名称不能为空', trigger: 'blur' }],
		exeDate: [{ required: true, message: '执行日期不能为空', trigger: 'blur' }],
		deptId: [{ required: true, message: '归口部门不能为空', trigger: 'change' }],
		amend: [{ required: true, message: '修订/制订不能为空', trigger: 'blur' }],
		status: [{ required: true, message: '标准状态不能为空', trigger: 'blur' }],
		fileList: [{ required: true, message: '附件不能为空', trigger: 'blur' }],
	})
	const formRef = ref() // 表单 Ref

	const formImgRef = ref()
	const fileType = ref('img')
	// const fileLimit = ref(1)
	// 上传方法
	const handleImport = (type : string) => {
		fileType.value = type
		// fileLimit.value = type === 'file' ? 5 : 1
		formImgRef.value.open()
	}
	const handleUploadSuccess = (fileList) => {
		console.log(fileList)
		formData.value.fileList = formData.value.fileList ?? []
		if (fileList && fileList.length > 0) {
			let currentVersion = 'V1.0'

			fileList.forEach((item, index) => {
				let res = item.response.data
				// 创建新的文件对象并设置版本号
				const fileObj = {
					id: res.id, // 假设每个文件对象都有一个 'id' 属性
					version: currentVersion,
					fileName: res.fileName,
					fileUrl: res.fileUrl,
					fileState: index === formData.value.fileList.length - 1 ? '1' : '0',
					creatorName: res.creatorName,
					createTime: res.createTime
				}
				// 追加到 fileIds 数组
				formData.value.fileList.unshift(fileObj)
				formData.value.fileList.forEach((file, index) => {
					file.fileState = index === 0 ? '1' : '0'
				})
				console.log(formData.value.fileList)
			})
		}
	}

	// 预览
	const DialogFlieRef = ref()
	const handleView = async (name : string, type : string, id : number) => {
		await DialogFlieRef.value.open(name, type, id)
	}

	// 下载
	const handleDownload = async(url : string, name: string) => {
		// 下载的二次确认
    	await message.confirm('是否确认下载？')
		downFile(url, name)
	}

	const listDelete = async (id : number) => {
		console.log(id)

		await message.delConfirm()
		formData.value.fileList = formData.value.fileList.filter((item) => item.id !== id)
		console.log(formData.value.fileList)

		await message.success(t('common.delSuccess'))
	}

	/** 打开弹窗 */
	const open = async (type : string, id ?: number, matterId: any, list: any) => {
		dialogVisible.value = true
		dialogTitle.value = t('action.' + type)
		formType.value = type
		resetForm()
		formData.value.typeId = matterId
		await innerData()
		await getTree()
		// 修改时，设置数据
		if (id) {
			formLoading.value = true
			try {
				formData.value = await RulesRegulationsApi.getRulesRegulations(id)
			} finally {
				formLoading.value = false
			}
		}
	}
	defineExpose({ open }) // 提供 open 方法，用于打开弹窗
	/** 提交表单 */
	const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
	const submitForm = async () => {
		// 校验表单
		await formRef.value.validate()
		// 提交请求
		formLoading.value = true
		try {
			const data = formData.value as unknown as RulesRegulationsVO
			if (formType.value === 'create') {
				await RulesRegulationsApi.createRulesRegulations(data)
				message.success(t('common.createSuccess'))
			} else {
				await RulesRegulationsApi.updateRulesRegulations(data)
				message.success(t('common.updateSuccess'))
			}
			dialogVisible.value = false
			// 发送操作成功的事件
			emit('success')
		} finally {
			formLoading.value = false
		}
	}

	// 填报单位树结构
	interface DeptNode {
		id: number
		masterOrgId?: number | string
		name: string
		parentId: number | string
		children?: DeptNode[]
		isLeaf?: boolean
	}
	const deptList = ref<DeptNode[]>([]) // 树形结构
	const defaultExpandedKeys = ref<(string | number)[]>([])
	const getTree = async (id) => {
		deptList.value = []
		const res = await DeptApi.getSimpleDeptList(id)
		deptList.value.push(...handleLazyTree(res, 'id', 'parentId'))
		defaultExpandedKeys.value = deptList.value.map((node) => node.id)
		console.log(deptList.value)
	}
	const loadNode = async (node: Node, resolve: (data: any[]) => void) => {
		try {
			const nodeId = node.data.id
			if (nodeId == undefined || nodeId == null) {
				return
			}
			const res = await DeptApi.getSimpleDeptList(nodeId)
			const children = handleLazyTree(res, 'id', 'parentId', 'children')
			resolve(children)
		} catch (error) {
			resolve([])
		}
	}

	/** 处理部门被点击 */
	const handleNodeClickCorporation = async (row : { [key : string] : any }) => {
		formData.value.companyName = row.name
	}
	const handleNodeClickSection = async (row : { [key : string] : any }) => {
		formData.value.deptName = row.name
	}

	// 获取内部规章制度分类
	const mattersList = ref([])
	const defaultExpandedKeysType = ref<(string | number)[]>([])
	const innerData = async () => {
		try {
			mattersList.value = []
			const res = await RulesRegulationsApi.getInnerregulationsList()
			mattersList.value.push(...handleTree(res, 'id', 'parentId'))
			defaultExpandedKeysType.value = mattersList.value.map((node) => node.id)
		} finally {
		}
	}

	/** 重置表单 */
	const resetForm = () => {
		formData.value = {
			id: undefined,
			auditRoleName: undefined,
			parentId: undefined,
			sort: undefined,
			status: undefined,
			projectPhase: undefined,
			date: undefined,
			typeId: undefined,
		}
		formRef.value?.resetFields()
	}
</script>