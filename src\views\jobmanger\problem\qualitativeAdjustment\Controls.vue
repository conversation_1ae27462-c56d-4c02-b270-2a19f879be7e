<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-11-13 11:34:59
 * @Description: 问题定性调整-调整分类
-->
<template>
	<Dialog :title="dialogTitle" :scroll="true" v-model="dialogVisible" width="80%">
        <el-row :gutter="16">
            <el-col :span="5" :xs="24">
                <ContentWrap class="h-1/1">
                    <JobmangerLeftTree ref="JobmangerLeftTreeRef" @node-click="handleDeptNodeClick" :searchUrl="searchUrl"
                        :showEdit='false' />
                </ContentWrap>
            </el-col>
            <el-col :span="19" :xs="24">
                <ContentWrap class="common-card-search">
                    <!-- 搜索工作栏 -->
                    <el-form class="-mb-15px common-search-form" :model="queryParams" ref="queryFormRef" :inline="true" label-width="110px">
                        <el-form-item label="问题定性" prop="quesName">
                            <el-input v-model="queryParams.quesName" placeholder="请输入问题分类名称" clearable
                                @keyup.enter="handleQuery" class="!w-200px" />
                        </el-form-item>
                        <el-form-item label="定性描述" prop="quesDesc">
                            <el-input v-model="queryParams.quesDesc" placeholder="请输入问题描述" clearable
                                @keyup.enter="handleQuery" class="!w-200px" />
                        </el-form-item>
						<el-form-item label="定性分类" prop="typeName">
                            <el-input v-model="queryParams.typeName" placeholder="请输入定性分类" clearable
                                @keyup.enter="handleQuery" class="!w-200px" />
                        </el-form-item>
                    </el-form>
					<div class="right-search-btn">
						<el-button  type="primary"  @click="handleQuery">
							<Icon icon="ep:search" class="mr-5px" />搜索
						</el-button>
						<el-button @click="resetQuery">
							<Icon icon="ep:refresh" class="mr-5px" />重置
						</el-button>
					</div>
                </ContentWrap>
                <!-- 列表 -->
                <ContentWrap>
                    <el-table v-loading="loading" :data="list" :stripe="true" border :show-overflow-tooltip="true">
                        <el-table-column label="序号" width="60" align="center">
                            <template
                                #default="{ $index }">{{ (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1 }}</template>
                        </el-table-column>
                        <el-table-column label="问题定性" align="left" prop="quesName" min-width="180" />
                        <!-- <el-table-column label="问题定性" align="left" prop="quesQualitation" min-width="180"/> -->
                        <el-table-column label="定性描述" align="left" prop="quesDesc" min-width="180"/>
                        <el-table-column label="审计建议" align="left" prop="auditSugg" min-width="180"/>
                        <el-table-column label="审计事项" align="left" prop="auditMattersName" min-width="180"/>
                        <el-table-column label="定性分类" align="left" prop="typeName" min-width="180"/>
                        <el-table-column label="创建人" align="center" prop="createrName" min-width="100"/>
                        <el-table-column label="备注" align="left" prop="remark" min-width="180"/>
                        <el-table-column label="操作" align="center" width="100" fixed="right">
                            <template #default="scope">
                                <el-button link type="primary" @click="getSelect(scope.row.id, scope.row.typeId)">选择</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                    <!-- 分页 -->
                    <Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
                        @pagination="getList" />
                </ContentWrap>
            </el-col>
        </el-row>

	</Dialog>
</template>
<script setup lang="ts">
	import { getIntDictOptions, getStrDictOptions, DICT_TYPE } from '@/utils/dict'
	import { QualitativeAnalysisApi, QualitativeAnalysisVO } from '@/api/jobmanger/problem/qualitativeAnalysis'
	import { defaultProps, handleTree } from '@/utils/tree'
	import { CACHE_KEY, useCache } from '@/hooks/web/useCache'
	import JobmangerLeftTree from '@/views/jobmanger/itemLibrary/components/JobmangerLeftTree.vue'
	import { QualitativeAdjustmentApi } from '@/api/jobmanger/problem/qualitativeAdjustment'

	/** 审计角色 表单 */
	defineOptions({ name: 'QualitativeAnalysisForm' })

	const { t } = useI18n() // 国际化
	const message = useMessage() // 消息弹窗
	const { wsCache } = useCache()
	const dialogVisible = ref(false) // 弹窗的是否展示
	const dialogTitle = ref('') // 弹窗的标题
	const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
	const formType = ref('') // 表单的类型：create - 新增；update - 修改
	const loading = ref(true) // 列表的加载中
	const list = ref<QualitativeAnalysisVO[]>([]) // 列表的数据
	const queryParams = reactive({
		id: undefined,
		pageSize: 10,
		pageNo: 1,
		quesName: undefined,
		quesQualitation: undefined,
		quesDesc: undefined,
		auditMattersId: undefined,
		questId: undefined,
		typeName: undefined,
	})
	const queryFormRef = ref() // 搜索的表单
	const exportLoading = ref(false) // 导出的加载中
	const total = ref(0)
	const questId = ref([]) //问题清单id

	/** 打开弹窗 */
	const open = async (type : string, typeId : any, id ?: [], activeName: any) => {
		dialogVisible.value = true
		dialogTitle.value = t(type)
		formType.value = type
		queryParams.auditMattersId = undefined,
		queryParams.questId = undefined,
		questId.value = []
		questId.value = id
		await getList()
	}
	defineExpose({ open }) // 提供 open 方法，用于打开弹窗

	// 问题分类接口
	const JobmangerLeftTreeRef = ref()
	var searchUrl = ref('/audit/tank-trees-type/get?type=4')
	const activeName = ref('1')
	const handleClick = async (type) => {
		await (searchUrl.value = '/audit/tank-trees-type/get?type=4')
		JobmangerLeftTreeRef.value.getTree()
		queryParams.auditMattersId = undefined,
		await getList()
	}
	const handleRefresh = async () => {
		await (searchUrl.value = '/audit/tank-trees-type/get?type=4')
		JobmangerLeftTreeRef.value.getTree()
		await getList()
	}

	/** 查询列表 */
	const getList = async () => {
		loading.value = true
		try {
			let data = {}
			data = await QualitativeAnalysisApi.getQualitativeAnalysisList(queryParams)
			// data = await QualitativeAnalysisApi.getQualitativeAnalysisListType()
			list.value = data.list
			total.value = data.total
		} finally {
			loading.value = false
		}
	}

	/** 搜索按钮操作 */
	const handleQuery = () => {
		queryParams.pageNo = 1
		getList()
	}

	/** 重置按钮操作 */
	const resetQuery = () => {
		queryFormRef.value.resetFields()
		handleQuery()
	}

	/** 处理部门被点击 */
	const handleDeptNodeClick = async (row) => {
		queryParams.auditMattersId = row.id
		queryParams.questId = row.id
		await getList()
	}

	// 选择
	const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
	const getSelect = async( id:number, typeId : number) => {
		const data = {
			questionId: id,
			quesInformation: questId.value
		}
		console.log(data)
		await message.confirm('是否确认选择当前问题定性？')
		await QualitativeAdjustmentApi.SelectApi(data)
		dialogVisible.value = false
		await message.success(t('操作成功！'))
		// 发送操作成功的事件
		emit('success')
	}

	/** 初始化 **/
	onMounted(() => {
		getList()
	})
</script>
