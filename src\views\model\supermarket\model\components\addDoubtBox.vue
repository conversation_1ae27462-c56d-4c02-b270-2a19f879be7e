<script lang="ts" setup>
import {ref} from "vue";
import {ElButton} from "element-plus";
import * as doubtfulApi from "@/api/model/doubt"
import * as warningApi from "@/api/model/warning";
import * as firstModelApi from "@/api/fixModel/first";

const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const tableLoading = ref(true)
const tableList = ref([])
const total = ref(0) // 列表的总页数

const formLoading = ref(false)

const tableTitle = ref([])
const tableData = ref([])

const formData = ref({
  businessId: '',
  doubtfulSource: '',
  companyId: '',
  modelName: '',
  checkUserId: '',
  monthId: '',
  doubtfulDescription: '',
  doubtfulJudgment: '',
  companyIds:[]
})

const userList = ref([]) // 核查人列表
const userQueryParams = reactive({
  pageNo: 1,
  pageSize: 10000,
})

const open = async (tempId: string, modelName: string, data: []) => {
  console.log(modelName)
  // console.log(data)
  await getUserList();
  await getTree();
  tableData.value = []
  tableList.value = []
  if (modelName) {
    formData.value.modelName = modelName
  }
  if (tempId) {
    queryParams.businessId = tempId
    formData.value.businessId = tempId
  }
  dialogVisible.value = true
  tableLoading.value = true
  dialogTitle.value = '加入疑点箱'
  if (data.length > 0) {
    tableTitle.value = Object.keys(data[0])
    tableData.value = data
  }
  // queryParams.modelId = data.id
  await getPageList()
}

const getUserList = async () => {
  const res = await warningApi.doubtfulCheckUserList(userQueryParams)
  if(res){
    // console.log(res)
    userList.value = res.list
  }
}


defineExpose({open}) // 提供 open 方法，用于打开弹窗

const queryParams = reactive({
  pageNo: 1,
  pageSize: 5,
  businessId: '',
  companyId: ''
})

const selectedMonth = ref(new Date().toISOString().split('T')[0]); // 获取当前日期，并格式化为YYYY-MM格式


const formRules = reactive({
  modelName: [{required: true, message: '模型名称不能为空', trigger: 'blur'}],
  companyId: [{required: true, message: '单位不能为空', trigger: 'blur'}],
  checkUserId: [{required: true, message: '核查人不能为空', trigger: 'blur'}],
  doubtfulDescription: [{required: true, message: '存储过程名不能为空', trigger: 'blur'}],
  doubtfulJudgment: [{required: true, message: '风险点描述不能为空', trigger: 'blur'}],
})

const getPageList = async () => {
  tableLoading.value = true
  const res = await doubtfulApi.getDoubtfulInfoPage(queryParams)
  tableList.value = res.list
  total.value = res.total
  tableLoading.value = false
}

const formRef = ref()
const save = async () => {
  if (!formRef.value) return
  const valid = await formRef.value.validate()
  if (!valid) return
  formLoading.value = true;
  try {
    const data = formData.value;
    const res = await doubtfulApi.saveDoubtInfo(data)
    if (res) {
      message.success("保存成功");
      reset();
      await getPageList()
    }
  } finally {
    formLoading.value = false;
  }
}

/** 表单重置 */
const reset = () => {
  formData.value.checkUserId = ''
  formData.value.doubtfulDescription = ''
  formData.value.doubtfulJudgment= ''
  formData.value.companyIds = []
  company.value = ''
}

const handleDeleteDoubt = async (id) => {
  const res = await doubtfulApi.deleteDoubtfulInfo(id)
  if (res) {
    message.success("删除成功");
    await getPageList()
  }
}

const statusMap = computed(() => ({
  '0': '未核查',
  '1': '核查中',
  '2': '已核查'
}));

// 根据状态值返回对应的汉字
const getStatusText = (status: number) => {
  return statusMap.value[status] || '未知';
};

const changeCompany = (value) => {
  // console.log(value)
  queryParams.companyId = value
  getPageList(queryParams)

}

const company = ref('')
const getUserCompany = async (value) => {
  // console.log(value)
  const obj = userList.value.find(item => item.value === value);
  // console.log(obj.company)
  company.value = obj.company
}

// 提交 发送至流程
const submit = async () => {
  await doubtfulApi.batchStartDoubtfulCheckFlow(formData.value.businessId).then(res => {
    if (res) {
      message.success("提交成功");
      dialogVisible.value = false;
    }
  })
}

const orgList = ref<Tree[]>([]) // 树形结构
const getTree = async () => {
  orgList.value = []
  const res = await firstModelApi.getUnitTree()
  orgList.value = res
}

</script>

<template>
  <Dialog :title="dialogTitle" v-loading="tableLoading" v-model="dialogVisible" width="65%" setScrollTop="2vh" :scroll="true"
             append-to-body>
    <el-form :model="formData" ref="formRef" class="common-submit-form"
             :rules="formRules"
             label-width="100px">
      <el-row>
        <el-col :span="12">
          <el-form-item label="模型名称" prop="modelName">
            <el-input size="small" v-model="formData.modelName" disabled/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="账期" prop="monthId">
            <el-date-picker size="small"
                            v-model="selectedMonth"
                            type="month"
                            value-format="YYYY-MM"
                            placeholder="选择账期"
                            class="!w-440px"
                            disabled
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="单位" prop="companyIds">
            <el-tree-select
              v-model="formData.companyIds"
              placeholder="请选择单位名称"
              ref="treeRef"
              :data="orgList"
              node-key="value"
              multiple
              show-checkbox
              filterable
              :check-strictly="true"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="核查人" prop="checkUserId">
            <el-select v-model="formData.checkUserId" size="small"
                       placeholder="请选择核查人"
                       clearable
                       filterable
                       class="!w-440px">
              <el-option v-for="item in userList"
                         :key="item.id"
                         :label="item.nickname" :value="item.id"/>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="疑点描述" prop="doubtfulDescription">
        <el-input size="small" type="textarea" :rows="2" v-model="formData.doubtfulDescription"
                  placeholder="请输入疑点描述"/>
      </el-form-item>
      <el-form-item label="疑点初判" prop="doubtfulJudgment">
        <el-input size="small" type="textarea" :rows="2" v-model="formData.doubtfulJudgment"
                  placeholder="请输入疑点初判"/>
      </el-form-item>
    </el-form>


    <div style="display: flex; justify-content: flex-end;">
      <el-button size="small" @click="dialogVisible = false">取 消</el-button>
      <el-button size="small" type="primary" @click="save" :disabled="formLoading">保 存</el-button>
      <el-button size="small" type="primary" @click="submit">提 交</el-button>
    </div>

    <hr/>
    <el-table border size="small" height="200" v-loading="false" :data="tableList" stripe>
      <el-table-column prop="monthId" label="账期" align="center" width="80"/>
      <el-table-column prop="doubtfulName" label="模型名称" align="left"/>
      <el-table-column prop="companyName" label="单位" align="left"/>
      <el-table-column prop="doubtfulDescription" label="疑点描述" align="left"/>
      <el-table-column prop="doubtfulJudgment" label="疑点初判" align="left"/>
      <el-table-column prop="checkUserName" label="核查人" align="left" width="80"/>
      <el-table-column prop="createUser" label="创建人" align="left" width="90"/>
      <el-table-column label="状态" align="center" width="80">
        <template #default="scope">
          {{ getStatusText(scope.row.checkStatus) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="70">
        <template #default="scope">
          <el-button
            v-if="scope.row.checkStatus === '0' && scope.row.deleteEnable"
            link
            size="default"
            type="danger"
            @click="handleDeleteDoubt(scope.row.id)"
          >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      @page-change="getPageList"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
    />


    <el-table border :data="tableData" height="200" size="small"
              :header-cell-style="{backgroundColor:'#f3f1f1',color:'#666'}" :stripe="true"
              :show-overflow-tooltip="true">
      <el-table-column width="200"
        v-for="column in tableTitle"
        :key="column"
        :label="column"
        :prop="column"
      />
    </el-table>


  </Dialog>

</template>

<style scoped lang="scss">
.button-group {
  display: flex;
  gap: 8px; /* 按钮之间的间距 */
  /* 确保按钮组在容器内靠右对齐 */
  margin-right: 10px;
}
::v-deep(.el-table .el-table__header .el-table__cell) {
  text-align: center;
}

/* 使用auto值将元素推到右侧 */
</style>
