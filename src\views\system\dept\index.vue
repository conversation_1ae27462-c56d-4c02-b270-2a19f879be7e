<template>
  <!-- 搜索工作栏 -->
  <el-row>
    <el-col :span="8" :xs="24">
      <ContentWrap class="h-1/1">
        <div class="ml-20px mr-20px">
          <el-radio-group v-model="radio">
            <el-radio :value="0">不包含隐藏</el-radio>
            <el-radio :value="1">全部</el-radio>
          </el-radio-group>
        </div>
        <DeptTree :radio="radio" @node-click="handleDeptNodeClick" @success="getTreeList" ref="deptTree" />
      </ContentWrap>
    </el-col>
    <el-col :span="16" :xs="24">
      <ContentWrap class="h-1/1">
        <el-row v-if="queryParams.id != -1">
          <el-col :span="24" :xs="24" class="text-right mb-16px">
            <el-button type="primary" plain @click="openEditForm('update')">
              <Icon icon="ep:edit" />编辑
            </el-button>
          </el-col>
        </el-row>
        <el-descriptions :column="1" border v-if="queryParams.id != -1">
          <el-descriptions-item label="名称">{{ detailData?.name }}</el-descriptions-item>
          <el-descriptions-item label="简称">{{ detailData?.shortName }}</el-descriptions-item>
          <el-descriptions-item label="简称二">{{ detailData?.shortName2 }}</el-descriptions-item>
          <el-descriptions-item label="类型">{{ getType(detailData?.orgType) }}</el-descriptions-item>
          <el-descriptions-item label="计划管理员">{{ detailData?.planManagerName }}</el-descriptions-item>
          <el-descriptions-item label="代码">{{ detailData?.orgCode }}</el-descriptions-item>
          <el-descriptions-item label="法人组织编码">{{ detailData?.legalDeptCode == null ? detailData.legalCompanyCode :
            detailData.legalDeptCode
            }}</el-descriptions-item>
          <el-descriptions-item label="父机构">
            <span :title="detailData?.parentName">{{ detailData?.parentName }}</span>
          </el-descriptions-item>
          <!-- <el-descriptions-item label="开始时间">{{formatTime(detailData?.createTime, 'yyyy-MM-dd') }}</el-descriptions-item>
          <el-descriptions-item label="结束时间">{{formatTime(detailData?.createTime, 'yyyy-MM-dd') }}</el-descriptions-item>-->
          <el-descriptions-item label="序号">{{ detailData?.sort }}</el-descriptions-item>
          <el-descriptions-item label="状态">{{ detailData?.status == 1 ? '停用' : '正常' }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatTime(detailData?.createTime, 'yyyy-MM-dd HH:mm:ss')
            }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ formatTime(detailData?.updateTime, 'yyyy-MM-dd HH:mm:ss')
            }}</el-descriptions-item>
          <el-descriptions-item label="是否显示">{{ detailData?.deptShow == 1 ? '隐藏' : '显示' }}</el-descriptions-item>
          <!-- <el-descriptions-item label="原名称">{{ detailData?.sort }}</el-descriptions-item> -->
          <!-- <el-descriptions-item label="简称">{{ detailData?.sort }}</el-descriptions-item>
          <el-descriptions-item label="机构管理员">{{ detailData?.sort }}</el-descriptions-item>-->
        </el-descriptions>
      </ContentWrap>
    </el-col>
  </el-row>

  <!-- 表单弹窗：添加/修改 -->
  <DeptForm ref="formRef" @success="getList" />
  <!-- 修改组织信息弹窗 -->
  <EditDeptForm ref="editForm" @success="getTreeList" />
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import { handleTree } from '@/utils/tree'
import * as DeptApi from '@/api/system/dept'
import DeptForm from './DeptForm.vue'
import * as UserApi from '@/api/system/user'
import DeptTree from './DeptTree.vue'
import { formatTime } from '@/utils'
import EditDeptForm from './EditDeptForm.vue'
defineOptions({ name: 'SystemDept' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const radio = ref(0)
const queryParams = reactive({
  // pageNo: 1,
  // pageSize: 10,
  id: -1
  // status: undefined
})
const detailData = ref({})
/** 查询部门列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await DeptApi.getDeptPage(queryParams)
    detailData.value = data
  } finally {
    loading.value = false
  }
}
const getType = (type: string) => {
  if (type === '0') {
    return '公司'
  } else if (type === '1') {
    return '部门'
  } else if (type === '2') {
    return '职位'
  }
}
const handleDeptNodeClick = async (row) => {
  queryParams.id = row.id
  await getList()
}
const editForm = ref()
const openEditForm = async () => {
  await editForm.value.open(queryParams.id)
}

const deptTree = ref()
const getTreeList = async () => {
  await getList()
  deptTree.value.getTree(0)
}

</script>
<style lang="scss" scoped>
:deep(.el-descriptions__label.el-descriptions__cell.is-bordered-label) {
  width: 240px !important;
}
</style>
