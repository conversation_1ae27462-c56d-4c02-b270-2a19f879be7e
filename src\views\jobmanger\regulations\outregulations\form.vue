<!--
* @Author: lijunliang
* @Date: 2024-09-11 16:31:32
* @Description: 外部法律法规表单=>
-->
<template>
	<Dialog :title="dialogTitle" v-model="dialogVisible" width="50%">
		<el-form class="common-submit-form" ref="formRef" :model="formData" :rules="formRules" v-loading="formLoading">
			<el-row>
				<el-col :span="24">
					<el-form-item label="法律法规名称" prop="lawName">
						<el-input v-model="formData.lawName" placeholder="请输入法律法规名称" />
					</el-form-item>
				</el-col>
				<!-- <el-col :span="12"> -->
					<el-form-item label="文号" prop="docNum" label-width="70px">
						<el-input v-model="formData.docNum" placeholder="请输入文号" class="!w-240px" />
					</el-form-item>
				<!-- </el-col> -->
				<!-- <el-col :span="12"> -->
					<el-form-item label="发布主体" prop="publishMains">
						<el-select v-model="formData.publishMains" placeholder="请选择发布主体" multiple class="!w-240px">
							<el-option v-for="dict in getIntDictOptions(DICT_TYPE.EXTERNAL_LAWS_REGULATIONS_SUBJECT)"
								:key="dict.value" :label="dict.label" :value="String(dict.value)" />
						</el-select>
					</el-form-item>
				<!-- </el-col> -->
				<!-- <el-col :span="12"> -->
					<el-form-item label="发布日期" prop="publishDate">
						<el-date-picker v-model="formData.publishDate" type="date" value-format="x" format="YYYY-MM-DD"
							placeholder="选择某一天" class="!w-240px" />
					</el-form-item>
				<!-- </el-col> -->
				<!-- <el-col :span="12"> -->
					<el-form-item label="生效日期" prop="effeDate">
						<el-date-picker v-model="formData.effeDate" type="date" value-format="x" format="YYYY-MM-DD"
							placeholder="选择某一天" class="!w-240px" />
					</el-form-item>
				<!-- </el-col> -->
				<!-- <el-col :span="12"> -->
					<el-form-item label="法规状态" prop="lawStatus">
						<el-select v-model="formData.lawStatus" placeholder="请选择法规状态" class="!w-240px">
							<el-option v-for="dict in getIntDictOptions(DICT_TYPE.EXTERNAL_LAWS_REGULATIONS_STATE)"
								:key="dict.value" :label="dict.label" :value="dict.value" />
						</el-select>
					</el-form-item>
				<!-- </el-col> -->
				<!-- <el-col :span="12"> -->
					<el-form-item label="分类" label-width="70px" prop="typeId">
						<el-tree-select
							v-model="formData.typeId"
							:data="mattersAll"
							check-strictly
							filterable
							:default-expanded-keys="defaultExpandedKeys"
							placeholder="请选择分类"
							:render-after-expand="false"
							class="!w-240px"
						/>
					</el-form-item>
				<!-- </el-col> -->
				<el-col :span="24" class="del_hover">
					<el-form-item label="附件" prop="fileList">
						<el-button type="primary" plain @click="handleImport('file')">
							<Icon icon="ep:upload" class="mr-5px" /> 上传文件
						</el-button>
					</el-form-item>
				</el-col>
			</el-row>
			<!-- <el-form-item> -->
				<!-- <div v-if="formData.fileList && formData.fileList.length > 0" class="news-attachments">
					<el-row :gutter="10">
						<el-col :span="24" v-for="(attachment, index) in formData.fileList" :key="index"
							class="mb-10px">
							<el-row>
								<el-col :span="18"><span class="attachment-file-name ellipsis"
										:title="attachment.fileName">{{
			              attachment.fileName
			            }}</span></el-col>
								<el-col :span="6">
									<el-button type="danger" class="mr-10px" size="small"
										@click="handleDelete('fileList', attachment.id, index)">删除</el-button>
								</el-col>
							</el-row>
						</el-col>
					</el-row>
				</div> -->
				<el-table :data="formData.fileList" border :stripe="true" :show-overflow-tooltip="true">
					<el-table-column label="文档名称" align="left" prop="fileName" min-width="380"/>
					<el-table-column label="创建人" align="center" prop="creatorName" fixed="right" width="120" />
					<el-table-column label="创建时间" key="creationtime" align="center" fixed="right" width="180">
						<template #default="{row}">
							<span>{{ formatDate(row.createTime) }}</span>
						</template>
					</el-table-column>
					<el-table-column label="操作" align="center" fixed="right" width="160">
						<template #default="scope">
							<el-button link type="primary" @click="handleView('预览','VIEW',scope.row.id)">预览</el-button>
							<el-button link type="primary" @click="handleDownload(scope.row?.fileUrl, scope.row?.fileName)">下载</el-button>
							<el-button link type="danger" @click="handleDelete('fileList', scope.row.id, scope.$index)">删除</el-button>
						</template>
					</el-table-column>
				</el-table>
			<!-- </el-form-item> -->
		</el-form>
		<template #footer>
			<el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
			<el-button @click="dialogVisible = false">取 消</el-button>
		</template>
	</Dialog>
	<!-- 文件上传 -->
	<FileForm ref="formImgRef" @success="handleUploadSuccess" :type="fileType" />
	<!-- 预览附件 -->
	<DialogFlie ref="DialogFlieRef" />
</template>
<script setup lang="ts">
	import { getIntDictOptions, getStrDictOptions, DICT_TYPE } from '@/utils/dict'
	import { RegulationsApi, RegulationsVO } from '@/api/jobmanger/regulations/outregulations'
	import FileForm from '@/views/infra/file/FileForm.vue'
	import * as FileApi from '@/api/infra/file'
	import { handleTree } from '@/utils/tree'
	import { formatTime } from '@/utils'
	import { formatDate } from '@/utils/formatTime'
	import { downFile } from '@/utils/fileName'

	/** 审计角色 表单 */
	defineOptions({ name: 'OutregulationsForm' })

	const { t } = useI18n() // 国际化
	const message = useMessage() // 消息弹窗
	const dialogVisible = ref(false) // 弹窗的是否展示
	const dialogTitle = ref('') // 弹窗的标题
	const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
	const formType = ref('') // 表单的类型：create - 新增；update - 修改
	const formData = ref({
		id: undefined,
		docNum: undefined,
		publishMains: undefined,
		lawName: undefined,
		publishDate: undefined,
		effeDate: undefined,
		lawStatus: undefined,
		fileList: [],
		typeId: undefined,
	})
	const formRules = reactive({
		publishMains: [{ required: true, message: '发布主体不能为空', trigger: 'change' }],
		lawName: [{ required: true, message: '法律法规名称不能为空', trigger: 'blur' }],
		publishDate: [{ required: true, message: '发布日期不能为空', trigger: 'change' }],
		effeDate: [{ required: true, message: '生效日期不能为空', trigger: 'change' }],
		lawStatus: [{ required: true, message: '法规状态不能为空', trigger: 'change' }],
		typeId: [{ required: true, message: '分类不能为空', trigger: 'change' }],
	})
	const formRef = ref() // 表单 Ref

	const formImgRef = ref()
	const fileType = ref('img')
	// const fileLimit = ref(1)
	// 上传方法
	const handleImport = (type : string) => {
		fileType.value = type
		// fileLimit.value = type === 'file' ? 5 : 1
		formImgRef.value.open()
	}
	const handleUploadSuccess = (fileList) => {
		let fileArr =
			fileList && fileList.length > 0
				? fileList.map((item) => {
					return {
						// url: item.response.data,
						// name: item.name,
						id: item.response.data.id,
						configId: item.response.data.configId,
						fileName: item.response.data.fileName,
						filePath: item.response.data.filePath,
						fileUrl: item.response.data.fileUrl,
						fileType: item.response.data.fileType,
						fileSize: item.response.data.fileSize,
						busiId: item.response.data.busiId,
						creatorName: item.response.data.creatorName,
						createTime: item.response.data.createTime,
						updaterName: item.response.data.updaterName
					}
				})
				: []
		if (fileType.value === 'file') {
			formData.value.fileList = formData.value.fileList.concat(...fileArr)
			if (formType.value === 'update') {
				RegulationsApi.updateRegulationsFile(formData.value)
			}
		} else if (fileType.value === 'img') {
			formData.value.showImg = fileArr
			formRef.value.validateField('showImg')
		}
	}

	// 预览
	const DialogFlieRef = ref()
	const handleView = async (name : string, type : string, id : number) => {
		await DialogFlieRef.value.open(name, type, id)
	}

	// 下载
	const handleDownload = async(url : string, name: string) => {
		// 下载的二次确认
    	await message.confirm('是否确认下载？')
		downFile(url, name)
	}
	const handleDelete = async (type : string, id : number, index : number = 0) => {
		await message.delConfirm()
		await FileApi.deleteFile(id)
		formData.value[type].splice(index, 1)
		await message.success(t('common.delSuccess'))
	}

	/** 打开弹窗 */
	const open = async (type : string, id ?: number, matterId: any) => {
		await getAudit()
		dialogVisible.value = true
		dialogTitle.value = t('action.' + type)
		formType.value = type
		resetForm()
		formData.value.typeId = matterId
		// 修改时，设置数据
		if (id) {
			formLoading.value = true
			try {
				formData.value = await RegulationsApi.getRegulations(id)
				// 将字典value改为number
				if(formData.value.publishMains){
					await formData.value.publishMains.map(item => {
						item = Number(item)
					})
				}
				if (formData.value.fileList == null) {
					formData.value.fileList = []
				}
			} finally {
				formLoading.value = false
			}
		}
	}
	defineExpose({ open }) // 提供 open 方法，用于打开弹窗
	/** 提交表单 */
	const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
	const submitForm = async () => {
		// 校验表单
		await formRef.value.validate()
		// 将字典value改为string
		if(formData.value.publishMains){
			await formData.value.publishMains.map(item => {
				item = item.toString()
			})
		}
		console.log(formData.value)
		// 提交请求
		formLoading.value = true
		try {
			const data = formData.value as unknown as RegulationsVO
			if (formType.value === 'create') {
				await RegulationsApi.createRegulations(data)
				message.success(t('common.createSuccess'))
			} else {
				await RegulationsApi.updateRegulations(data)
				message.success(t('common.updateSuccess'))
			}
			dialogVisible.value = false
			// 发送操作成功的事件
			emit('success')
		} finally {
			formLoading.value = false
		}
	}

	/** 获取分类 */ 
	const defaultExpandedKeys = ref<(string | number)[]>([]) //默认展示某一层级
	const mattersAll = ref<any[]>([]) //审计事项数据
	const getAudit = async () =>{
		mattersAll.value = []
		try {
			const res = await RegulationsApi.ListRegulations(3)
			mattersAll.value.push(...handleTree(res, 'id', 'parentId'))
			defaultExpandedKeys.value = mattersAll.value.map((node) => node.id)
			console.log(defaultExpandedKeys.value)
		} catch{}
	}

	/** 重置表单 */
	const resetForm = () => {
		formData.value = {
			id: undefined,
			docNum: undefined,
			publishMains: undefined,
			lawName: undefined,
			publishDate: undefined,
			effeDate: undefined,
			lawStatus: undefined,
			fileList: [],
			typeId: undefined,
		}
		formRef.value?.resetFields()
	}
</script>