<!--
* @Author: li<PERSON><PERSON>g
* @Date: 2024-10-23 10:30:58
* @Description: 审减原因详情列表=>
-->
<template>
	<Dialog v-model="dialogVisible" :scroll="true" title="中介机构详情列表" width="75%">
		<el-table border :data="list" :stripe="true" :show-overflow-tooltip="true" >
			<el-table-column label="序号" type="index" width="60" align="center"/>
			<el-table-column label="工程名称" align="left" prop="name" min-width="180"/>
			<el-table-column label="投资单位" align="left" prop="proOwnersName" min-width="180"/>
			<el-table-column label="控制价编制单位" align="left" prop="bzdw" min-width="180" />
			<el-table-column label="控制价报审金额" align="center" prop="settlementamount" min-width="150" />
			<el-table-column label="控制价审计单位" prop="zjjgname" align="left" min-width="180"/>
			<el-table-column label="控制价审定金额" align="center" prop="jtsjJsmoney" min-width="150" />
			<el-table-column label="审增值" align="center" prop="zjjgSjzjD" min-width="120" />
			<el-table-column label="审减值" align="center" prop="zjjgSjzj" min-width="120"/>
			<el-table-column label="审计调整率" align="center" prop="sjtzl" min-width="120" />
		</el-table>
		<!-- 分页 -->
		<Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
			@pagination="getList" />
	</Dialog>
</template>

<script lang="ts" setup>
import { BiddingControlPriceApi, BiddingControlPriceVO } from '@/api/decision/engineering/biddingControlPrice'
defineOptions({ name: 'Detail' })

const dialogVisible = ref(false) // 弹窗的是否展示
const detailLoading = ref(false) // 表单地加载中
const loading = ref(true) // 列表的加载中
const list = ref<BiddingControlPriceVO[]>([]) // 列表的数据
const queryParams = reactive({
	pageSize: 10,
	pageNo: 1,
	zjjgid: undefined as number | undefined,
	menu: undefined,
	question: undefined as string | undefined,
})
const total = ref(0)

/** 打开弹窗 */
const open = async (row?: string) => {
	dialogVisible.value = true
	// 设置数据
	detailLoading.value = true
	try {
		queryParams.question = row
		await getList()
	} finally {
		detailLoading.value = false
	}
}
/** 查询列表 */
const getList = async () => {
	loading.value = true
	try {
		const data = await BiddingControlPriceApi.getBiddingControlPriceList(queryParams)
		list.value = data.list
		total.value = data.total
	} finally {
		loading.value = false
	}
}

defineExpose({ open }) // 提供 open 方法，用于打开弹窗
</script>
