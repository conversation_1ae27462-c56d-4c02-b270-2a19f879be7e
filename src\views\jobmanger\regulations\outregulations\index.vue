<!--
* @Author: lijunliang
* @Date: 2024-09-11 16:24:50
* @Description: 外部法律法规=>
-->
<template>
  <el-row :gutter="16">
		<el-col :span="5" :xs="24">
			<ContentWrap class="h-1/1">
				<JobmangerLeftTree @node-click="handleDeptNodeClick" :createUrl="createUrl" :editUrl="editUrl"
					:searchUrl="searchUrl" :delUrl="delUrl" :detailsUrl="detailsUrl" :type='3' :showBut='true' />
			</ContentWrap>
		</el-col>
		<el-col :span="19" :xs="24">
      <ContentWrap class="common-card-search">
        <el-form
          class="-mb-15px common-search-form"
          :model="queryParams"
          ref="queryFormRef"
          :inline="true"
          label-width="100px"
        >
            <el-form-item class="label" label="文号" prop="docNum">
              <el-input
                v-model="queryParams.docNum"
                placeholder="请输入文号"
                clearable
                @keyup.enter="handleQuery"
                class="!w-200px"
              />
            </el-form-item>
            <el-form-item class="label" label="发布主体" prop="publishMain">
              <el-select
                v-model="queryParams.publishMain"
                placeholder="请选择发布主体"
                clearable
                class="!w-200px"
              >
                <el-option
                  v-for="dict in getIntDictOptions(DICT_TYPE.EXTERNAL_LAWS_REGULATIONS_SUBJECT)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item class="label" label="法律法规名称" prop="lawName">
              <el-input
                v-model="queryParams.lawName"
                placeholder="请输入法律法规名称"
                clearable
                @keyup.enter="handleQuery"
                class="!w-200px"
              />
            </el-form-item>
            <el-form-item class="label" label="法规状态" prop="lawStatus">
              <el-select
                v-model="queryParams.lawStatus"
                placeholder="请选择法规状态"
                clearable
                class="!w-200px"
              >
                <el-option
                  v-for="dict in getIntDictOptions(DICT_TYPE.EXTERNAL_LAWS_REGULATIONS_STATE)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item class="label" label="发布日期">
              <el-date-picker
                v-model="queryParams.publishDate"
                type="date"
                clearable
                placeholder="选择发布日期"
                value-format="YYYY-MM-DD"
                class="!w-200px"
              />
            </el-form-item>
            <el-form-item class="label" label="生效日期">
              <el-date-picker
                v-model="queryParams.effeDate"
                type="date"
                clearable
                value-format="YYYY-MM-DD"
                placeholder="选择生效日期"
                class="!w-200px"
              />
            </el-form-item>
        </el-form>
        <div class="right-search-btn">
          <el-button  type="primary"  @click="handleQuery">
              <Icon icon="ep:search" class="mr-5px" />搜索
            </el-button>
            <el-button @click="resetQuery">
              <Icon icon="ep:refresh" class="mr-5px" />重置
            </el-button>
        </div>
        </ContentWrap>

      <!-- 列表 -->
        <ContentWrap>
        <div class="button_margin15">
          <el-button
             v-hasPermi="['audit:tank-ext-law-regulat:create']"
            @click="openForm('create', '', queryParams.typeId)"
            type="primary"
          >
            <Icon icon="ep:plus" class="mr-5px" />新增法律法规
          </el-button>
          <el-button
             v-hasPermi="['audit:tank-ext-law-regulat:export']"
            :loading="exportLoading"
            @click="handleExport"
          >
            <Icon class="mr-5px" icon="ep:download" />
            导出
          </el-button>
        </div>
        <el-table v-loading="loading" :data="list" border :stripe="true" :show-overflow-tooltip="true">
          <el-table-column label="序号" width="60" align="center">
            <template #default="{ $index }">{{
              (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1
            }}</template>
          </el-table-column>
          <el-table-column label="法律法规名称" align="left" prop="lawName" min-width="300" />
          <el-table-column label="文号" align="left" prop="docNum" min-width="260"/>
          <el-table-column label="发布主体" align="center" prop="publishMainName" min-width="180"/>
          <el-table-column label="分类" align="center" prop="typeName" min-width="100" />
          <el-table-column label="发布日期" key="publishDate" align="center" min-width="120">
            <template #default="scope">
              <span>{{ formatTime(scope.row.publishDate, 'yyyy-MM-dd') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="生效日期" align="center" prop="effeDate" min-width="120">
            <template #default="scope">
              <span>{{ formatTime(scope.row.effeDate, 'yyyy-MM-dd') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="法规状态" align="center" prop="lawStatus" min-width="100">
            <template #default="scope">
              <dict-tag
                :type="DICT_TYPE.EXTERNAL_LAWS_REGULATIONS_STATE"
                :value="scope.row.lawStatus"
              />
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" fixed="right" width="200">
            <template #default="scope">
              <el-button v-hasPermi="['audit:tank-ext-law-regulat:get']" link type="primary" @click="openDetailForm(scope.row.id)">查看</el-button>
              <el-button
                v-hasPermi="['audit:tank-ext-law-regulat:update']"
                link
                type="primary"
                @click="openForm('update', scope.row.id)"
                >编辑</el-button
              >
              <el-button
                v-hasPermi="['audit:tank-ext-law-regulat:delete']"
                link
                type="danger"
                @click="handleDelete(scope.row.id)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页 -->
        <Pagination
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </ContentWrap>
      <!-- </el-card> -->
    </el-col>
  </el-row>

  <!-- 表单弹窗：添加/修改 -->
  <Form ref="formRef" @success="getList" />
  <!-- 查看详情弹窗 -->
  <Detail ref="detailRef" />
</template>

<script setup lang="ts">
import { getIntDictOptions, getStrDictOptions, DICT_TYPE, NumberDictDataType } from '@/utils/dict'

import { handleTree } from '@/utils/tree'
import download from '@/utils/download'
import {
  RegulationsApi,
  RegulationsVO,
  RegulationsDetailVO
} from '@/api/jobmanger/regulations/outregulations'
import Form from './form.vue'
import { formatTime } from '@/utils'
import Detail from './Detail.vue'
import { ElementPlusInfoType } from '@/types/elementPlus'
import { TimeSplicing } from '@/utils/formatTime'
import JobmangerLeftTree from '@/views/jobmanger/itemLibrary/components/JobmangerLeftTree.vue'
defineOptions({ name: 'Outregulations' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const { query } = useRoute() //接收路由传参
const loading = ref(true) // 列表的加载中
const list = ref<RegulationsVO[]>([]) // 列表的数据
const queryParams = reactive({
  pageSize: 10,
  pageNo: 1,
  docNum: undefined,
  publishMain: undefined,
  lawName: undefined,
  lawStatus: undefined,
  effeDate: undefined,
  publishDate: undefined,
  typeId: undefined, //分类
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const total = ref(0)
const searchUrl = '/audit/tank-trees-type/get?type=3'
const createUrl = '/audit/tank-trees-type/create'
const editUrl = '/audit/tank-trees-type/update'
const delUrl = '/audit/tank-trees-type/delete'
const detailsUrl = '/audit/tank-trees-type/get-tree'
/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await RegulationsApi.getRegulationsList(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

// 查看按钮操作
const detailRef = ref()
const openDetailForm = (id: number) => {
  detailRef.value.open(id)
}
/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  queryParams.effeDate = undefined
  queryParams.publishDate = undefined
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number, matterId) => {
  formRef.value.open(type, id, matterId)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await RegulationsApi.deleteRegulations(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await RegulationsApi.exportRegulations(queryParams)
    const time = TimeSplicing(new Date())
    download.excel(data, `外部法律法规${time}.xls`)
  } catch {
  } finally {
    exportLoading.value = false
  }
}

//智库首页跳转-查询详情
const projectId = ref({id: query.id as unknown as number})
const getDateil = async() => {
  if(projectId.value.id){
    await openDetailForm(projectId.value.id)
    // 使用一次后销毁传参
    const newUrl = await window.location.href.replace(/\?.*$/, '') //获取当前路径，并去除参数
		await history.replaceState({}, '', newUrl) //更新浏览器历史记录，不触发页面重新加载
  }
}

/** 树形被点击 */
const handleDeptNodeClick = async (row) => {
  queryParams.typeId = row.id
  await getList()
}

/** 初始化 **/
onMounted(() => {
  getList()
  getDateil()
})
</script>

