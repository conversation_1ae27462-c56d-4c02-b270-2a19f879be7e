<!--
* @Author: lijunliang
* @Date: 2024-10-22 17:10:50
* @Description: 工程结算-审计效率=>
-->
<template>
	<ContentWrap>
		<!-- 搜索工作栏 -->
		<el-form class="-mb-15px" :model="queryParams" ref="queryFormRef" :inline="true" label-width="100px">
			<el-form-item label="审计单位" prop="auditorName">
				<el-input v-model="queryParams.auditorName" placeholder="请输入审计单位" clearable @keyup.enter="handleQuery"
					class="!w-200px" />
			</el-form-item>
			<el-form-item label="投资单位" prop="investorName">
				<el-input v-model="queryParams.investorName" placeholder="请输入投资单位" clearable @keyup.enter="handleQuery"
					class="!w-200px" />
			</el-form-item>
			<el-form-item label="管理单位" prop="managerName">
				<el-input v-model="queryParams.managerName" placeholder="请输入管理单位" clearable @keyup.enter="handleQuery"
					class="!w-200px" />
			</el-form-item>
			<el-form-item label="施工单位" prop="constructorName">
				<el-input v-model="queryParams.constructorName" placeholder="请输入施工单位" clearable @keyup.enter="handleQuery"
					class="!w-200px" />
			</el-form-item>
			<el-form-item label="审计序列" prop="auditSequence">
				<el-select v-model="queryParams.auditSequence" placeholder="请选择审计序列" clearable class="!w-200px">
					<el-option v-for="dict in getIntDictOptions('audit_sequences')" :key="dict.value"
						:label="dict.label" :value="dict.value" />
				</el-select>
			</el-form-item>
			<el-form-item label="合同名称" prop="contractName">
				<el-input v-model="queryParams.contractName" placeholder="请输入合同名称" clearable @keyup.enter="handleQuery"
					class="!w-200px" />
			</el-form-item>
			<el-form-item label="日期" prop="publishDate">
				<el-date-picker v-model="queryParams.publishDate" type="daterange" unlink-panels range-separator="-"
					start-placeholder="开始" end-placeholder="结束" value-format="YYYY-MM-DD" clearable @change="changeDate" />
			</el-form-item>
			<el-form-item>
				<el-button type="primary" @click="handleQuery">
					<Icon icon="ep:search" class="mr-5px" />搜索
				</el-button>
				<el-button @click="resetQuery">
					<Icon icon="ep:refresh" class="mr-5px" />重置
				</el-button>
			</el-form-item>
		</el-form>
	</ContentWrap>

	<!-- 列表 -->
	<ContentWrap>
		<el-tabs v-model="activeName" class="demo-tabs" @tab-change="handleClick">
			<el-tab-pane label="基建工程" name="1" />
			<el-tab-pane label="技改工程" name="2" />
		</el-tabs>
		<el-button :loading="exportLoading" plain @click="handleExport">
			<Icon class="mr-5px" icon="ep:download" />
			导出
		</el-button>
		<el-table ref="tableRef1" border v-show="activeName=='1'" v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true" :span-method="arraySpanMethod" show-summary :summary-method="getSummaries">
			<el-table-column label="序号" width="60" align="center">
				<template #default="{ $index }">{{ (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1
					}}</template>
			</el-table-column>
			<el-table-column label="一审单位（中介机构名称）" align="left" prop="auditorName" min-width="180">
				<template #default="scope">
					<span class="click-pointer" @click="openDetailForm(scope.row.id)">{{ scope.row.auditorName }}</span>
				</template>
			</el-table-column>
			<el-table-column label="委托基建结算数量(项）(向中介发出任务时间）" align="center" prop="entrustedSettlements" />
			<el-table-column label="审定基建结算数量(项）(中介出具终审意见的时间）" align="center" prop="auditedSettlements" />
			<el-table-column label="基建结算审计完成率" align="center" prop="auditCompletionRate">
				<template #default="scope">
					{{ scope.row.auditCompletionRate?scope.row.auditCompletionRate + '%': '0%'}}
				</template>
			</el-table-column>
			<el-table-column label="初审平均耗时(天）(中介出具初审意见天数）" align="center" prop="initialAuditDuration" />
			<el-table-column label="终审平均耗时(天）(中介出具终审意见天数）" align="center" prop="finalAuditDuration" />
			<el-table-column label="出具纸版审计报告平均耗时(天）(中介出具审计报告天数）" align="center" prop="reportDuration" />
		</el-table>
		<el-table ref="tableRef2" border v-show="activeName=='2'" v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true" :span-method="arraySpanMethod" show-summary :summary-method="getSummaries">
			<el-table-column label="序号" width="60" align="center">
				<template #default="{ $index }">{{ (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1
					}}</template>
			</el-table-column>
			<el-table-column label="一审单位（中介机构名称）" align="left" prop="auditorName" min-width="180">
				<template #default="scope">
					<el-link @click="openDetailForm(scope.row.id)" :underline="false" type="primary">{{
						scope.row.auditorName }}</el-link>
				</template>
			</el-table-column>
			<el-table-column label="委托技改结算数量(项)" align="center" prop="entrustedSettlements" />
			<el-table-column label="审定技改结算数量(项)" align="center" prop="auditedSettlements" />
			<el-table-column label="基建结算审计完成率" align="center" prop="auditCompletionRate">
				<template #default="scope">
					{{ scope.row.auditCompletionRate?scope.row.auditCompletionRate + '%': '0%'}}
				</template>
			</el-table-column>
			<el-table-column label="初审平均耗时(天)" align="center" prop="initialAuditDuration" />
			<el-table-column label="终审平均耗时(天)" align="center" prop="finalAuditDuration" />
			<el-table-column label="出具纸版审计报告平均耗时(天)" align="center" prop="reportDuration" />
		</el-table>
		<!-- 分页 -->
		<Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
			@pagination="getList" />
	</ContentWrap>
	<!-- 一审单位详情弹窗 -->
	<Detail ref="detailRef" />
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { AuditEfficiencyVO, AuditEfficiencyApi } from '@/api/decision/engineering/projectSettlement/auditEfficiency'
import Detail from './Detail.vue'
import download from '@/utils/download'
import { h } from 'vue'
import type { VNode } from 'vue'
import type { TableColumnCtx } from 'element-plus'
import { mergeCells } from '@/utils/mergeCells'
defineOptions({ name: 'AuditEfficiency' })

const message = useMessage() // 消息弹窗
const loading = ref(true) // 列表的加载中
const list = ref<AuditEfficiencyVO[]>([]) // 列表的数据
const queryParams = reactive({
	pageSize: 10,
	pageNo: 1,
	auditorName: undefined,
	investorName: undefined,
	managerName: undefined,
	constructorName: undefined,
	auditSequence: undefined,
	contractName: undefined,
	publishDate: undefined,
	startDate: undefined,
	endDate: undefined,
	enType: '基本建设',
})
const queryFormRef = ref() // 搜索的表单
const total = ref(0)
const activeName = ref('1')

// tab切换触发事件
const handleClick = async (type) => {
	if(type == 1) {
		queryParams.enType = '基本建设'
	}else if(type == 2) {
		queryParams.enType = '技改工程'
	}
	await getList()
}

// 时间选择触发事件
const changeDate = async(data) => {
	queryParams.startDate = data[0]
	queryParams.endDate = data[1]
}

// 合并表尾合计行单元格的方法
const tableRef1 = ref()
const tableRef2 = ref()
const arraySpanMethod = ({ row, column, rowIndex, columnIndex }:any)=> {
	if(activeName.value == 1) {
		mergeCells(tableRef1, 1, 4)
	}else if(activeName.value == 2){
		mergeCells(tableRef2, 1, 4)
	}

}

interface SummaryMethodProps<T = AuditEfficiencyVO> {
  columns: TableColumnCtx<T>[]
  data: T[]
}
// 表尾合计显示
const getSummaries = (param: SummaryMethodProps) => {
  const { columns, data } = param
  const sums: (string | VNode)[] = []
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = ''
      return
    }
    const values = data.map((item) => Number(item[column.property]))
    if (!values.every((value) => Number.isNaN(value)) &&  dataTotal.value) {
	  sums[5] = dataTotal.value.initialAuditDurationTotal
	  sums[6] = dataTotal.value.finalAuditDurationTotal
	  sums[7] = dataTotal.value. reportDurationTotal
    } else {
      sums[index] = h('div', { style: { textDecoration: 'underline' } }, [
        '平均值',
      ])
    }
  })
  return sums
}
/** 查询列表 */
const getList = async () => {
	loading.value = true
	try {
		const data = await AuditEfficiencyApi.getAuditEfficiencyList(queryParams)
		list.value = data.list
		total.value = data.total
		await getTotal()
	} finally {
		loading.value = false
	}
}

// 查询合计数据
const dataTotal = ref()
const getTotal = async () => {
	loading.value = true
	try {
		dataTotal.value = await AuditEfficiencyApi.getAuditEfficiencyTotal(queryParams)
	} finally {
		loading.value = false
	}
}

/** 搜索按钮操作 */
const handleQuery = () => {
	queryParams.pageNo = 1
	getList()
}
/** 重置按钮操作 */
const resetQuery = () => {
	queryFormRef.value.resetFields()
	queryParams.startDate = undefined,
	queryParams.endDate = undefined,
	handleQuery()
}
const exportLoading = ref(false) // 导出的加载中
/** 导出按钮操作 */
const handleExport = async () => {
	try {
		// 导出的二次确认
		await message.exportConfirm()
		// 发起导出
		exportLoading.value = true
		const data = await AuditEfficiencyApi.exportAuditEfficiency(queryParams)
		download.excel(data, '工程结算-审计效率.xls')
	} catch {
	} finally {
		exportLoading.value = false
	}
}

/** 点击一审单位进详情  */
const detailRef = ref()
const openDetailForm = (id?: number) => {
	detailRef.value.open(id, queryParams.enType)
}
/** 初始化 **/
onMounted(() => {
	getList()
})
</script>
