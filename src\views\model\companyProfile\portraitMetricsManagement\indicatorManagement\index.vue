<template>
  <ContentWrap class="common-card-search">
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px common-search-form"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="100px"
    >
      <!-- <el-form-item label="所属行业" prop="name">
        <el-select
          v-model="queryParams.name"
          placeholder="请选择是否销号"
          clearable
          class="!w-200px"
        >
          <el-option label="督办中" value="0" />
          <el-option label="未销号" value="1" />
          <el-option label="已销号" value="2" />
        </el-select>
      </el-form-item> -->
      <el-form-item label="所属年度" prop="pointYear">
        <el-input
          v-model="queryParams.pointYear"
          placeholder="所属年度"
          clearable
          @keyup.enter="handleQuery"
          class="!w-200px"
        />
      </el-form-item>
    </el-form>
    <div class="right-search-btn">
      <el-button type="primary" @click="handleQuery">
        <Icon icon="ep:search" class="mr-5px" />搜索
      </el-button>
      <el-button @click="resetQuery"> <Icon icon="ep:refresh" class="mr-5px" />重置 </el-button>
    </div>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <div class="mb-10px flex_center">
      <el-button type="primary" plain @click="openCreateForm">新增</el-button>
    </div>
    <el-table border v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="序号" width="60" align="center">
        <template #default="{ $index }">{{
          (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1
        }}</template>
      </el-table-column>
      <el-table-column label="年度" align="center" prop="pointYear" min-width="120" />
      <!-- <el-table-column label="行业" align="center" prop="industry" min-width="120" /> -->
      <el-table-column label=" 创建日期" align="center" prop="createTime" min-width="120">
        <template #default="scope">
          <span>{{ formatTime(scope.row.createTime, 'yyyy-MM-dd') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="是否启用" align="center" prop="status" min-width="120">
        <template #default="scope">
          <el-switch
            style="--el-switch-on-color: #13ce66"
            v-model="scope.row.status"
            :active-value="0"
            :inactive-value="1"
            @change="handleStatusChange(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="160" fixed="right">
        <template #default="scope">
          <el-button link type="primary" @click="openForm(scope.row.id)">编辑</el-button>
          <el-button link type="danger" @click="handleDelete(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>
  <!-- 编辑弹窗 -->
  <Form ref="formRef" @success="getList" />
  <!-- 新增弹窗 -->
  <CreateForm ref="createFormRef" @success="getList" />
</template>

<script setup lang="ts">
// import { getIntDictOptions, getStrDictOptions, DICT_TYPE, NumberDictDataType } from '@/utils/dict'
import { IndicatorManagementApi } from '@/api/model/companyProfile/portraitMetricsManagement/indicatorManagement'
import { formatTime } from '@/utils'
import Form from './form.vue'
import CreateForm from './createForm.vue'
// import download from '@/utils/download'
// import { DialogFlie } from '@/components/DialogFlie'
defineOptions({ name: 'IndicatorManagement' })

const message = useMessage() // 消息弹窗

const loading = ref(true) // 列表的加载中
const list = ref([]) // 列表的数据
const queryParams = reactive({
  pageSize: 10,
  pageNo: 1,
  pointYear: undefined
})
const queryFormRef = ref() // 搜索的表单
const total = ref(0)
/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await IndicatorManagementApi.getEvalStandardsList(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}
/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

// 新增
const createFormRef = ref()
const openCreateForm = () => {
  createFormRef.value.open()
}
// 删除按钮操作
const handleDelete = async (id: any) => {
  await message.delConfirm()
  await IndicatorManagementApi.deleteEvalStandards(id)
  await message.success('操作成功！')
  getList()
}
// 编辑
const formRef = ref()
const handleStatusChange = async (row: any) => {
  console.log(row)
  const data = row
  await IndicatorManagementApi.updateEvalStandards(data)
  message.success('操作成功！')
  getList()
}
const openForm = (id: any) => {
  console.log(id)
  formRef.value.open(id)
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
