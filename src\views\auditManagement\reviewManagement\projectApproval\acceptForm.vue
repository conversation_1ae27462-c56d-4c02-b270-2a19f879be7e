<!--
* @Author: li<PERSON><PERSON>g
* @Date: 2024-09-21 09:10:50
* @Description: 文书接受迎审文件=>
-->
<template>
  <div v-loading="formLoading">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="130px"
    >
      <el-card class="mb-10px">
        <div class="mb-10px flex_center common-border-left-blue">
          迎审文件接收
        </div>
        <el-table border :data="list" :stripe="true" :show-overflow-tooltip="true">
          <el-table-column label="序号" type="index" width="60" align="center"/>
          <el-table-column label="文档类型" align="center" prop="publishMain" min-width="120"/>
          <el-table-column label="文档名称" align="left" prop="docNum" min-width="120" />
          <el-table-column label="编制单位" align="left" prop="deptName" min-width="180" header-align="center"/>
          <el-table-column label="上传人" align="center" prop="publishMain" min-width="120"/>
          <el-table-column label="上传时间" key="publishDate" align="center" min-width="120">
            <template #default="scope">
              <span>{{ formatTime(scope.row.publishDate, 'yyyy-MM-dd') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="是否接收" align="center" prop="publishMain" min-width="120"/>
          <el-table-column label="接收时间" key="receiveTime" align="center" min-width="120">
            <template #default="{row}">
              <span>{{ formatTime(row.receiveTime, 'yyyy-MM-dd') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="反馈意见" align="left" prop="publishMain" min-width="180"/>
          <el-table-column label="操作" align="center" width="120">
            <template #default="scope">
              <el-button link type="primary" @click="handleView(scope.row)">查看</el-button>
              <el-button
                link
                type="danger"
                @click="handleDownload(scope.row?.fileUrl,scope.row?.fileName)"
              >下载</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      <el-card class="mb-10px">
        <div class="mb-10px flex_center">
          <div class="flex_center fles_interval common-border-left-blue">
            迎审小组管理
          </div>
          <div>
            <el-button type="primary">一键初始化</el-button>
            <el-button type="primary">新增</el-button>
          </div>
        </div>
        <el-table border :data="list" :stripe="true" :show-overflow-tooltip="true">
          <el-table-column label="序号" type="index" width="60" align="center"/>
          <el-table-column label="姓名" align="left" prop="publishMain" min-width="180" />
          <el-table-column label="联系方式" align="center" prop="docNum" min-width="120" />
          <!-- <el-table-column label="座机" align="center" prop="publishMain" min-width="120"/> -->
          <!-- <el-table-column label="职务" align="left" prop="publishMain" min-width="180"/>
          <el-table-column label="部门" align="left" prop="publishMain" min-width="180"/> -->
          <el-table-column label="单位" align="left" prop="publishMain" min-width="180"/>
          <el-table-column label="添加人" align="center" prop="publishMain" min-width="120"/>
          <el-table-column label="添加时间" key="publishDate" align="center" min-width="120">
            <template #default="scope">
              <span>{{ formatTime(scope.row.publishDate, 'yyyy-MM-dd') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="100">
            <template #default="scope">
              <el-button
                link
                type="danger"
                @click="handleDownload(scope.row.id)"
              >删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      <el-card class="mb-10px">
        <el-form-item label="截止时间" prop="endTime">
          <el-date-picker
            v-model="formData.endTime"
            clearable
            placeholder="请选择截止时间"
            type="datetime"
            value-format="x"
          />
        </el-form-item>
        <el-form-item label="相关要求" prop="reason">
          <el-input v-model="formData.reason" placeholder="请输入相关要求" type="textarea" />
        </el-form-item>
      </el-card>
      <el-card class="mb-10px">
        <div class="mb-10px flex_center">
          <div class="flex_center fles_interval common-border-left-blue">
            资料清单填报
          </div>
          <div>
            <el-button type="primary" plain @click="showTeamDetail">分配</el-button>
            <el-button type="primary" plain @click="showTeamDetail">一键催办</el-button>
          </div>
        </div>
        <el-table border :data="list" :stripe="true" :show-overflow-tooltip="true">
          <el-table-column label="序号" type="index" width="60" align="center"/>
          <el-table-column label="资料名称" align="left" prop="publishMain" min-width="120" />
          <el-table-column label="资料类型" align="center" prop="docNum" min-width="120" />
          <el-table-column label="是否有模版" align="center" prop="publishMain" />
          <el-table-column label="是否提供" align="center" prop="publishMain" />
          <el-table-column label="未提供原因说明" align="left" prop="publishMain" min-width="180"/>
          <el-table-column label="相关联系人" align="center" prop="publishMain" />
          <el-table-column label="电话" align="center" prop="publishMain" />
        </el-table>
      </el-card>
      <el-card class="mb-10px">
        <div class="mb-10px flex_center common-border-left-blue">
          项目基本信息
        </div>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="项目名称">{{ formData.auditRoleCode }}</el-descriptions-item>
          <el-descriptions-item label="项目编号">{{ formData.auditRoleCode }}</el-descriptions-item>
          <el-descriptions-item label="审计类型">{{ formData.auditRoleName }}</el-descriptions-item>
          <el-descriptions-item label="立项年度">{{ formData.auditRoleName }}</el-descriptions-item>
          <el-descriptions-item label="组织方式">{{ formData.sort }}</el-descriptions-item>
          <el-descriptions-item label="时间计划">{{ formData.adder }}</el-descriptions-item>
          <el-descriptions-item label="审计期间">{{ formData.adder }}</el-descriptions-item>
          <!-- <el-descriptions-item label="开展专项">{{ formData.adder }}</el-descriptions-item> -->
          <el-descriptions-item label="整体报告">{{ formData.adder }}</el-descriptions-item>
          <el-descriptions-item label="公司领导">{{ formData.adder }}</el-descriptions-item>
          <el-descriptions-item label="曾任职务">{{ formData.adder }}</el-descriptions-item>

          <el-descriptions-item label="委托书文号">{{ formData.adder }}</el-descriptions-item>
          <el-descriptions-item label="是否境外">{{ formData.adder }}</el-descriptions-item>
          <el-descriptions-item label="是否重要">{{ formData.adder }}</el-descriptions-item>

          <el-descriptions-item label="实施单位">{{ formData.adder }}</el-descriptions-item>
          <el-descriptions-item label="立项依据">{{ formData.adder }}</el-descriptions-item>
        </el-descriptions>
      </el-card>
      <el-card class="mb-10px">
        <div class="mb-10px flex_center">
          <div class="flex_center fles_interval common-border-left-blue">
            审计对象信息
          </div>
          <div>
            <el-button type="primary" plain @click="showTeamDetail">查看小组信息</el-button>
          </div>
        </div>
        <el-table border :data="list" :stripe="true" :show-overflow-tooltip="true">
          <el-table-column label="序号" type="index" width="60" align="center"/>
          <el-table-column label="项目编号" align="left" prop="publishMain" min-width="180"/>
          <el-table-column label="项目名称" align="left" prop="docNum" min-width="200" />
          <el-table-column label="审计对象" align="left" prop="publishMain" min-width="180"/>
          <el-table-column label="组长" align="center" prop="publishMain" min-width="120"/>
          <el-table-column label="项目状态" align="center" prop="publishMain" min-width="120"/>
          <el-table-column label="操作" align="center" width="150">
            <template #default="scope">
              <el-button link type="primary" @click="handleView(scope.row)">小组信息</el-button>
              <el-button
                link
                type="danger"
                @click="handleDownload(scope.row.id)"
              >删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      <el-card class="mb-10px">
        <div class="mb-10px flex_center">
          <div class="flex_center fles_interval common-border-left-blue">
            迎审资料清单
          </div>
          <div>
            <el-button type="primary" plain @click="showTeamDetail">附件上传</el-button>
          </div>
        </div>
        <el-table border :data="list" :stripe="true" :show-overflow-tooltip="true">
          <el-table-column label="序号" type="index" width="60" align="center"/>
          <el-table-column label="文档名称" align="left" prop="docNum" min-width="120" />
          <el-table-column label="文档类型" align="center" prop="publishMain" min-width="120"/>
          <el-table-column label="编制单位" align="left" prop="deptName" min-width="180" header-align="center"/>
          <el-table-column label="上传人" align="center" prop="publishMain" min-width="120"/>
          <el-table-column label="上传时间" key="publishDate" align="center" min-width="120">
            <template #default="scope">
              <span>{{ formatTime(scope.row.publishDate, 'yyyy-MM-dd') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="150">
            <template #default="scope">
              <el-button link type="primary" @click="handleView(scope.row)">查看</el-button>
              <el-button
                link
                type="danger"
                @click="handleDownload(scope.row?.fileUrl,scope.row?.fileName)"
              >下载</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <el-col v-if="startUserSelectTasks.length > 0">
        <el-card class="mb-10px">
          <template #header>指定审批人</template>
          <el-form
            :model="startUserSelectAssignees"
            :rules="startUserSelectAssigneesFormRules"
            ref="startUserSelectAssigneesFormRef"
          >
            <el-form-item
              v-for="userTask in startUserSelectTasks"
              :key="userTask.id"
              :label="`任务【${userTask.name}】`"
              :prop="userTask.id"
            >
              <el-select
                v-model="startUserSelectAssignees[userTask.id]"
                multiple
                placeholder="请选择审批人"
              >
                <el-option
                  v-for="user in userList"
                  :key="user.id"
                  :label="user.nickname"
                  :value="user.id"
                />
              </el-select>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
      <el-form-item>
        <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
      </el-form-item>
    </el-form>
  </div>
  <!-- 查看小组信息 -->
  <TeamDetail ref="teamDetailRef" />
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import * as LeaveApi from '@/api/bpm/leave'
import { useTagsViewStore } from '@/store/modules/tagsView'
import * as DefinitionApi from '@/api/bpm/definition'
import * as UserApi from '@/api/system/user'
import { formatTime } from '@/utils'
import download from '@/utils/download'
import TeamDetail from '@/views/auditManagement/projectAssignment/PreTrialPreparation/ProjectInitiation/TeamDetail.vue'

defineOptions({ name: '‌acceptForm' })

const message = useMessage() // 消息弹窗
const { delView } = useTagsViewStore() // 视图操作
const { push, currentRoute } = useRouter() // 路由

const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formData = ref({
  type: undefined,
  reason: undefined,
  startTime: undefined,
  endTime: undefined
})
const formRules = reactive({
  type: [{ required: true, message: '请假类型不能为空', trigger: 'blur' }],
  reason: [{ required: true, message: '请假原因不能为空', trigger: 'change' }],
  startTime: [{ required: true, message: '请假开始时间不能为空', trigger: 'change' }],
  endTime: [{ required: true, message: '请假结束时间不能为空', trigger: 'change' }]
})
const formRef = ref() // 表单 Ref

// 整改过程材料
const list = ref<RegulationsVO[]>([
  {
    docNum: '1'
  }
]) // 整改通知书问题信息列表的数据
const handleView = () => {}
const handleDownload = (url: string,name: string) => {
  download.downFileByFileUrl(url,name)
}

// 查看小组信息
const teamDetailRef = ref()
const showTeamDetail = () => {
  teamDetailRef.value.open()
}

// 指定审批人
const processDefineKey = 'oa_leave' // 流程定义 Key
const startUserSelectTasks = ref([]) // 发起人需要选择审批人的用户任务列表
const startUserSelectAssignees = ref({}) // 发起人选择审批人的数据
const startUserSelectAssigneesFormRef = ref() // 发起人选择审批人的表单 Ref
const startUserSelectAssigneesFormRules = ref({}) // 发起人选择审批人的表单 Rules
const userList = ref<any[]>([]) // 用户列表

/** 提交表单 */
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 校验指定审批人
  if (startUserSelectTasks.value?.length > 0) {
    await startUserSelectAssigneesFormRef.value.validate()
  }

  // 提交请求
  formLoading.value = true
  try {
    const data = { ...formData.value } as unknown as LeaveApi.LeaveVO
    // 设置指定审批人
    if (startUserSelectTasks.value?.length > 0) {
      data.startUserSelectAssignees = startUserSelectAssignees.value
    }
    await LeaveApi.createLeave(data)
    message.success('发起成功')
    // 关闭当前 Tab
    delView(unref(currentRoute))
    await push({ name: 'BpmOALeave' })
  } finally {
    formLoading.value = false
  }
}

/** 初始化 */
onMounted(async () => {
  const processDefinitionDetail = await DefinitionApi.getProcessDefinition(
    undefined,
    processDefineKey
  )
  if (!processDefinitionDetail) {
    message.error('OA 请假的流程模型未配置，请检查！')
    return
  }
  startUserSelectTasks.value = processDefinitionDetail.startUserSelectTasks
  // 设置指定审批人
  if (startUserSelectTasks.value?.length > 0) {
    // 设置校验规则
    for (const userTask of startUserSelectTasks.value) {
      startUserSelectAssignees.value[userTask.id] = []
      startUserSelectAssigneesFormRules.value[userTask.id] = [
        { required: true, message: '请选择审批人', trigger: 'blur' }
      ]
    }
    // 加载用户列表
    userList.value = await UserApi.getSimpleUserList()
  }
})
</script>
<style scoped>
.flex_center {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
}

.fles_interval {
  flex: 1;
}
</style>