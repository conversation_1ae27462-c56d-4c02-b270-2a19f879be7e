<!--
* @Author: li<PERSON>liang
* @Date: 2024-09-11 09:12:36
* @Description: 内部规章制度=>
-->
<template>
	<el-row :gutter="16">
		<el-col :span="5" :xs="24">
			<ContentWrap class="h-1/1">
				<JobmangerLeftTree @node-click="handleDeptNodeClick" :createUrl="createUrl" :editUrl="editUrl"
					:searchUrl="searchUrl" :delUrl="delUrl" :detailsUrl="detailsUrl" :type='1' :showBut='true'/>
				<!-- <TreeList @node-click="handleDeptNodeClick" searchUrl="/system/dept/simple-list?id=" /> -->
			</ContentWrap>
		</el-col>
		<el-col :span="19" :xs="24">
			<ContentWrap class="common-card-search">
				<!-- 搜索工作栏 -->
				<el-form class="-mb-15px common-search-form" :model="queryParams" ref="queryFormRef" :inline="true" label-width="auto" >
					<el-form-item label="公司名称" prop="companyId">
						<el-tree-select
							v-model="queryParams.companyId"
							ref="treeRef"
							filterable
							clearable
							placeholder="请选择公司名称"
							:data="deptList"
							check-strictly
							:expand-on-click-node="false"
							:check-on-click-node="true"
							:default-expand-all="false"
							highlight-current
							node-key="id"
							@node-click="handleNodeClickCorporation"
							:load="loadNode"
							:default-expanded-keys="defaultExpandedKeys"
							:filter-node-method="filterNode"
							lazy
							class="!w-240px"
							>
							<template #default="{ data: { name } }">{{ name }}</template>
						</el-tree-select>
					</el-form-item>
					<el-form-item label="分类名称" prop="classificationName">
						<el-input v-model="queryParams.classificationName" placeholder="请输入分类名称" clearable @keyup.enter="handleQuery"
							class="!w-240px" />
					</el-form-item>
					<el-form-item label="标准编号" prop="standarNum">
						<el-input v-model="queryParams.standarNum" placeholder="请输入标准编号" clearable @keyup.enter="handleQuery"
							class="!w-240px" />
					</el-form-item>
					<el-form-item label="标准体系" prop="standardsysCode">
						<el-select v-model="queryParams.standardsysCode" placeholder="请选择标准体系" clearable class="!w-240px">
							<el-option v-for="dict in getIntDictOptions('institutionalsy_stem')" :key="dict.value"
								:label="dict.label" :value="dict.value" />
						</el-select>
					</el-form-item>
					<el-form-item label="执行日期" prop="exeDate">
						<el-date-picker v-model="queryParams.exeDate" type="date" clearable placeholder="选择某一天" value-format="x" format="YYYY-MM-DD" class="!w-240px" />
					</el-form-item>
					<el-form-item label="归口部门" prop="deptId">
						<el-tree-select
							v-model="queryParams.deptId"
							ref="treeRef"
							filterable
							clearable
							placeholder="请选择归口部门"
							:data="deptList"
							check-strictly
							:expand-on-click-node="false"
							:check-on-click-node="true"
							:default-expand-all="false"
							highlight-current
							node-key="id"
							@node-click="handleNodeClick"
							:load="loadNode"
							:default-expanded-keys="defaultExpandedKeys"
							:filter-node-method="filterNode"
							lazy
							class="!w-240px"
							>
							<template #default="{ data: { name } }">{{ name }}</template>
						</el-tree-select>
					</el-form-item>
					<el-form-item label="标准状态" prop="status">
						<el-select v-model="queryParams.status" placeholder="请选择标准状态" clearable class="!w-240px">
							<el-option v-for="dict in getIntDictOptions('Internalst_andard_state')" :key="dict.value"
								:label="dict.label" :value="dict.value" />
						</el-select>
					</el-form-item>
					<el-form-item label="修订/制订" prop="amend">
						<el-select v-model="queryParams.amend" placeholder="请选择修订/制订" clearable class="!w-240px">
							<el-option v-for="dict in getIntDictOptions('internalre_vision')" :key="dict.value"
								:label="dict.label" :value="dict.value" />
						</el-select>
					</el-form-item>
					<el-form-item label="标准名称" prop="standarName">
						<el-input v-model="queryParams.standarName" placeholder="请输入标准名称" clearable @keyup.enter="handleQuery"
							class="!w-240px" />
					</el-form-item>
				</el-form>
				<div class="right-search-btn">
					<el-button  type="primary"  @click="handleQuery">
							<Icon icon="ep:search" class="mr-5px" />搜索
						</el-button>
						<el-button @click="resetQuery">
							<Icon icon="ep:refresh" class="mr-5px" />重置
						</el-button>
				</div>
			</ContentWrap>
			<!-- 列表 -->
			<ContentWrap>
			<div class="button_margin15">
			<el-button v-hasPermi="['audit:tank-internal-rule:create']" type="primary" plain @click="openForm('create', '', queryParams.typeId)">
				<Icon icon="ep:plus" class="mr-5px" />新增规章制度
			</el-button>
			<el-button :loading="exportLoading" plain
			 	v-hasPermi="['audit:tank-internal-rule:export']"
				@click="handleExport">
				<Icon class="mr-5px" icon="ep:download" />
				导出
			</el-button>
			</div>
				<el-table v-loading="loading" :data="list" :stripe="true" border :show-overflow-tooltip="true">
					<el-table-column label="序号" width="60" align="center">
						<template
							#default="{ $index }">{{ (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1 }}</template>
					</el-table-column>
					<el-table-column label="公司名称" align="left" prop="companyName" min-width="260" />
					<el-table-column label="分类" align="left" prop="classificationName" min-width="180" />
					<el-table-column label="标准编号" align="left" prop="standarNum" min-width="260"/>
					<el-table-column label="标准体系" align="center" prop="standardsysCode" min-width="120">
						<template #default="scope">
							<dict-tag type="institutionalsy_stem" :value="scope.row.standardsysCode" />
						</template>
					</el-table-column>
					<el-table-column label="标准名称" align="left" prop="standarName" min-width="260"/>
					<el-table-column label="归口部门" align="left" prop="deptName" min-width="260"/>
					<el-table-column label="标准状态" align="center" prop="status" min-width="100">
						<template #default="scope">
							<dict-tag type="Internalst_andard_state" :value="scope.row.status" />
						</template>
					</el-table-column>
					<el-table-column label="修订/制订" align="center" prop="amend" min-width="100">
						<template #default="scope">
							<dict-tag type="internalre_vision" :value="scope.row.amend" />
						</template>
					</el-table-column>
					<el-table-column label="执行日期" key="exeDate" align="center" min-width="120">
						<template #default="scope">
							<span>{{ formatTime(scope.row.exeDate, 'yyyy-MM-dd') }}</span>
						</template>
					</el-table-column>
					<el-table-column label="备注" align="left" prop="remark" min-width="180" header-align="center"/>
					<el-table-column label="操作" align="center" fixed="right" width="200">
						<template #default="scope">
							<el-button v-hasPermi="['audit:tank-internal-rule:get']" link type="primary" @click="openDetailForm(scope.row.id)">查看</el-button>
							<el-button v-hasPermi="['audit:tank-internal-rule:update']" link type="primary" @click="openForm('update', scope.row.id)">编辑</el-button>
							<el-button v-hasPermi="['audit:tank-internal-rule:delete']" link type="danger" @click="handleDelete(scope.row.id)">删除</el-button>
						</template>
					</el-table-column>
				</el-table>
				<!-- 分页 -->
				<Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
					@pagination="getList" />
			</ContentWrap>
		</el-col>
	</el-row>
	<!-- 表单弹窗：添加/修改 -->
	<Form ref="formRef" @success="getList" />
	<!-- 查看详情弹窗 -->
	<Detail ref="detailRef" />
</template>

<script setup lang="ts">
	import { getIntDictOptions, getStrDictOptions, DICT_TYPE, NumberDictDataType } from '@/utils/dict'

	import { handleTree } from '@/utils/tree'
	import download from '@/utils/download'
	import { RulesRegulationsApi, RulesRegulationsVO, RulesRegulationsDetailVO } from '@/api/jobmanger/regulations/innerregulations'
	import Form from './form.vue'
	import { formatTime } from '@/utils'
	import Detail from './Detail.vue'
	import { ElementPlusInfoType } from '@/types/elementPlus'
	import * as DeptApi from '@/api/system/dept'
	import { defaultProps, handleLazyTree } from '@/utils/tree'
	import { TimeSplicing } from '@/utils/formatTime'
	import JobmangerLeftTree from '@/views/jobmanger/itemLibrary/components/JobmangerLeftTree.vue'
	import TreeList from '@/views/jobmanger/audittarget/TreeList.vue'
	defineOptions({ name: 'Innerregulations' })

	const message = useMessage() // 消息弹窗
	const { t } = useI18n() // 国际化
	const { query } = useRoute() //接收路由传参
	const loading = ref(true) // 列表的加载中
	const list = ref<RulesRegulationsVO[]>([]) // 列表的数据
	// const list = ref([])
	const queryParams = reactive({
		id: undefined,
		auditRoleName: undefined,
		pageSize: 10,
		pageNo: 1,
		projectPhase: undefined,
		date: undefined,
		status: undefined,
		exeDate: undefined,
		typeId: undefined, //分类id
		deptId: undefined, //归口部门id
		classificationName: undefined, //分类名称
	})
	const queryFormRef = ref() // 搜索的表单
	const exportLoading = ref(false) // 导出的加载中
	const total = ref(0)
	const searchUrl = '/audit/tank-trees-type/get?type=1'
	// const searchUrl = '/system/dept/simple-list?id=0'
	const createUrl = '/audit/tank-trees-type/create'
	const editUrl = '/audit/tank-trees-type/update'
	const delUrl = '/audit/tank-trees-type/delete'
	const detailsUrl = '/audit/tank-trees-type/get-tree'
	/** 查询列表 */
	const getList = async () => {
		loading.value = true
		try {
			const data = await RulesRegulationsApi.getRulesRegulationsList(queryParams)
			list.value = data.list
			total.value = data.total
		} finally {
			loading.value = false
		}
	}

	const detailRef = ref()
	const openDetailForm = (id: number) => {
		detailRef.value.open(id)
	}
	/** 搜索按钮操作 */
	const handleQuery = () => {
		queryParams.pageNo = 1
		getList()
	}

	/** 重置按钮操作 */
	const resetQuery = () => {
		queryFormRef.value.resetFields()
		handleQuery()
	}

	/** 添加/修改操作 */
	const formRef = ref()
	const openForm = (type : string, id ?: number, matterId: number) => {
		formRef.value.open(type, id, matterId, deptList)
	}

	/** 删除按钮操作 */
	const handleDelete = async (id : number) => {
		try {
			// 删除的二次确认
			await message.delConfirm()
			// 发起删除
			await RulesRegulationsApi.deleteRulesRegulations(id)
			message.success(t('common.delSuccess'))
			// 刷新列表
			await getList()
		} catch { }
	}

	/** 导出按钮操作 */
	const handleExport = async () => {
		try {
			// 导出的二次确认
			await message.exportConfirm()
			// 发起导出
			exportLoading.value = true
			const data = await RulesRegulationsApi.exportRulesRegulations(queryParams)
			const time = TimeSplicing(new Date())
    		download.excel(data, `内部规章制度${time}.xls`)
		} catch {
		} finally {
			exportLoading.value = false
		}
	}

	/** 树形被点击 */
	const handleDeptNodeClick = async (row) => {
		queryParams.typeId = row.id
		// queryParams.deptId = row.id
		await getList()
	}

	// 填报单位树结构
	interface DeptNode {
		id: number
		masterOrgId?: number | string
		name: string
		parentId: number | string
		children?: DeptNode[]
		isLeaf?: boolean
	}
	const deptList = ref<DeptNode[]>([]) // 树形结构
	const defaultExpandedKeys = ref<(string | number)[]>([])
	const getTree = async (id) => {
		deptList.value = []
		const res = await DeptApi.getSimpleDeptList(id)
		deptList.value.push(...handleLazyTree(res, 'id', 'parentId'))
		defaultExpandedKeys.value = deptList.value.map((node) => node.id)
		console.log(deptList.value)
	}
	const loadNode = async (node: Node, resolve: (data: any[]) => void) => {
		try {
			const nodeId = node.data.id
			if (nodeId == undefined || nodeId == null) {
				return
			}
			const res = await DeptApi.getSimpleDeptList(nodeId)
			const children = handleLazyTree(res, 'id', 'parentId', 'children')
			resolve(children)
		} catch (error) {
			resolve([])
		}
	}

	//智库首页跳转-查询详情
	const projectId = ref({id: query.id as unknown as number})
	const getDateil = async() => {
		if(projectId.value.id){
			await openDetailForm(projectId.value.id)
			// 使用一次后销毁传参
			const newUrl = await window.location.href.replace(/\?.*$/, '') //获取当前路径，并去除参数
			await history.replaceState({}, '', newUrl) //更新浏览器历史记录，不触发页面重新加载
		}
	}

	/** 初始化 **/
	onMounted(() => {
		getTree(0)
		getList()
		getDateil()
	})
</script>

