<!--
* @Author: li<PERSON><PERSON>g
* @Date: 2024-09-12 17:30:58
* @Description: 审计对象库详情=>
-->
<template>
	<Dialog v-model="dialogVisible" :scroll="true" title="详情" width="70%">
        <el-row>
            <el-col :span="24" :xs="24">
            <!-- 查询 -->
            <ContentWrap>
                <el-form
                class="-mb-15px"
                :model="queryParams"
                ref="queryFormRef"
                :inline="true"
                label-width="76px"
                >
                <el-form-item label="项目编号" prop="projectNo">
                    <el-input
                    v-model="queryParams.projectNo"
                    placeholder="请输入项目编号"
                    clearable
                    @keyup.enter="handleQuery"
                    class="!w-200px"
                    />
                </el-form-item>
                <el-form-item label="项目名称" prop="projectName">
                    <el-input
                    v-model="queryParams.projectName"
                    placeholder="请输入项目名称"
                    clearable
                    @keyup.enter="handleQuery"
                    class="!w-200px"
                    />
                </el-form-item>

                <el-form-item label="项目年度" prop="auditYear">
                    <el-date-picker
                    v-model="queryParams.auditYear"
                    type="year"
                    value-format="YYYY"
                    class="!w-200px"
                    placeholder="请选择项目年度"
                    />
                </el-form-item>
                <el-form-item label="项目阶段" prop="projStage">
                    <el-select
                    v-model="queryParams.projStage"
                    placeholder="请选择项目阶段"
                    clearable
                    class="!w-200px"
                    >
                    <el-option
                        v-for="dict in getIntDictOptions('proj_parent_project_stage')"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                    />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button @click="handleQuery">
                    <Icon icon="ep:search" />查询
                    </el-button>
                    <el-button @click="resetQuery">
                    <Icon icon="ep:refresh" />重置
                    </el-button>
                </el-form-item>
                </el-form>
            </ContentWrap>
            <ContentWrap>
                <el-table v-loading="loading" border :data="list">
                    <el-table-column label="序号" width="60" align="center">
                        <template
                        #default="{ $index }"
                        >{{ (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1 }}</template>
                    </el-table-column>
                    <el-table-column
                        label="项目年度"
                        align="left"
                        prop="auditYear"
                        :show-overflow-tooltip="true"
                        min-width="180"
                    />
                    <el-table-column
                        label="项目编码"
                        align="left"
                        prop="projectNo"
                        :show-overflow-tooltip="true"
                        min-width="180"
                    />
                    <el-table-column label="项目名称" align="left" min-width="180" :show-overflow-tooltip="true">
                        <template #default="{row}">
                        <span
                            class="click-pointer"
                            @click="showProjectDetail(row.id)"
                        >{{row.projectName}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="审计对象" align="center" prop="auditTarget" min-width="120" />
                    <el-table-column
                        label="实施单位"
                        align="left"
                        prop="implementDeptName"
                        min-width="180"
                    />
                    <el-table-column label="项目阶段" key="projStage" align="center">
                        <template #default="scope">
                        <dict-tag :type="'proj_parent_project_stage'" :value="scope.row.projStage" />
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" align="center" :width="120" fixed="right">
                        <template #default="scope">
                        <el-button type="primary" link @click="openDetailForm(scope.row.planProjectId)">查看</el-button>
                        </template>
                    </el-table-column>
                </el-table>

                <Pagination
                :total="total"
                v-model:page="queryParams.pageNo"
                v-model:limit="queryParams.pageSize"
                @pagination="getList"
                />
            </ContentWrap>
            </el-col>
        </el-row>

        <!-- 项目详情 -->
        <DetailMessage ref="detailMessageRef" />

        <!-- 查看详情弹窗 -->
	    <Detail ref="detailRef" />
	</Dialog>
</template>

<script lang="ts" setup>
	import { DICT_TYPE, NumberDictDataType, getIntDictOptions } from '@/utils/dict'
	import { formatTime } from '@/utils'
	import { AuditRoleDetailVO } from '@/api/basicData/auditRole'
    import { ProjectInitiationApi, searchListVO } from '@/api/auditManagement/projectAssignment/PreTrialPreparation/projectInitiation'
	import DetailMessage from '@/views/auditManagement/projectAssignment/PreTrialPreparation/ProjectInitiation/message/DetailMessage.vue'
    import Detail from './Detail.vue'
    import { ObjectbaseApi} from '@/api/jobmanger/audittarget'
    defineOptions({ name: 'audList' })

    const message = useMessage() // 消息弹窗
    const { t } = useI18n() // 国际化
    const loading = ref(true) // 列表的加载中
    const total = ref(0) // 列表的总页数
    const list = ref([]) // 列表的数
    const queryParams = reactive({
        pageNo: 1,
        pageSize: 10,
        projectName: undefined,
        projectNo: undefined,
        orgType: undefined,
        auditObject: undefined,
        auditType: undefined,
        auditYear: undefined,
        projStage: undefined,
        overseasFlag: undefined,
        auditCompanyId:undefined, //公司id
        companyId: undefined,
        auditTargetId: undefined,
    })
    const projectName = ref('1')

	const dialogVisible = ref(false) // 弹窗的是否展示
	// const detailLoading = ref(false) // 表单地加载中
	const queryFormRef = ref() // 搜索的表单
	/** 打开弹窗 */
	const open = async (id: any) => {
		dialogVisible.value = true
        // queryParams.auditCompanyId = id //根据审计对象查询项目
        // queryParams.companyId = id
        queryParams.auditTargetId = id
		// 设置数据
        await getList()
	}
	defineExpose({ open }) // 提供 open 方法，用于打开弹窗

    /** 查询列表 */
    const getList = async () => {
        loading.value = true
        try {
            const data = await getListMessage(queryParams)
            list.value = data.list
            total.value = data.total
        } finally {
            loading.value = false
        }
    }
    const getListMessage = async (queryParams) => {
        return await ObjectbaseApi.getItemList(queryParams)
        if (projectName.value === '1') {
            return await ProjectInitiationApi.getProjPlanProjectList(queryParams)
        } else {
            return await ProjectInitiationApi.getProjParentProjectList(queryParams)
        }
    }

    /** 搜索按钮操作 */
    const handleQuery = () => {
        queryParams.pageNo = 1
        getList()
    }

    /** 重置按钮操作 */
    const resetQuery = () => {
        queryFormRef.value?.resetFields()
        handleQuery()
    }

    // 点击项目名称操作
    const detailMessageRef = ref()
        const showProjectDetail = (id: number) => {
        detailMessageRef.value.open(id)
    }

    // 查看操作按钮
	const detailRef = ref()
	const openDetailForm = (id: number) => {
		detailRef.value.open('item' ,id)
	}

	// const activeName = ref('0')
	const handleClick = () => {

	}
	const handleWbView = (val) => { }
	const handleNbView = () => { }
	const handleZlView = () => { }
	const handleMxView = () => { }
</script>
