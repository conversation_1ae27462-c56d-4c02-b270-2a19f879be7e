
<template>
  <!-- 审计方案库 -->
  <ContentWrap class="common-card-search">
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px common-search-form"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="110px"
    >
      <el-form-item label="方案名称" prop="progName">
        <el-input
          v-model="queryParams.progName"
          placeholder="请输入方案名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item label="审计类型" prop="auditType">
        <el-select v-model="queryParams.auditType" placeholder="请选择审计类型" filterable clearable class="!w-200px">
          <el-option
            v-for="dict in auditTypeList"
            :key="dict.id"
            :label="dict.auditTypeName"
            :value="dict.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="所属公司" prop="companyUnit">
        <el-input
          v-model="queryParams.companyUnit"
          placeholder="请输入所属公司"
          clearable
          @keyup.enter="handleQuery"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item label="方案状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择方案状态" clearable class="!w-200px">
          <el-option
            v-for="dict in getIntDictOptions('audit_document_status')"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="方案内容" prop="progInfo">
        <el-input
          v-model="queryParams.progInfo"
          placeholder="请输入方案内容"
          clearable
          @keyup.enter="handleQuery"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item label="审计内容和重点" prop="progKeynote">
        <el-input
          v-model="queryParams.progKeynote"
          placeholder="请输入审计内容和重点"
          clearable
          @keyup.enter="handleQuery"
          class="!w-200px"
        />
      </el-form-item> -->
      <!-- <el-form-item>
        <el-button
        type="primary" @click="handleQuery">
          <Icon icon="ep:search" class="mr-5px" />搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon icon="ep:refresh" class="mr-5px" />重置
        </el-button>
      </el-form-item> -->
    </el-form>
		<div class="right-search-btn">
			<el-button  type="primary"  @click="handleQuery">
					<Icon icon="ep:search" class="mr-5px" />搜索
				</el-button>
				<el-button @click="resetQuery">
					<Icon icon="ep:refresh" class="mr-5px" />重置
				</el-button>
		</div>
  </ContentWrap>
  <!-- 列表 -->
  <ContentWrap>
    <div class="button_margin15">
        <el-button
          v-hasPermi="['audit:tank-programmes:create']"
          type="primary"
          plain
          @click="openForm('create')"
        >
          <Icon icon="ep:plus" class="mr-5px" />新增审计方案
        </el-button>
        <el-button
          v-hasPermi="['audit:tank-programmes:export']"
          :loading="exportLoading"
          plain
          @click="handleExport"
        >
          <Icon class="mr-5px" icon="ep:download" />导出
        </el-button>
      </div>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="序号" width="60" align="center">
        <template
          #default="{ $index }"
        >{{ (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1 }}</template>
      </el-table-column>
      <el-table-column label="方案名称" align="left" prop="progName" min-width="180" />
      <el-table-column label="审计类型" align="center" prop="auditName" />
      <el-table-column label="所属公司" align="left" prop="companyUnit" />
      <el-table-column label="方案状态" align="center" key="status">
        <template #default="scope">
          <dict-tag type="audit_document_status" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="300">
        <template #default="scope">
          <el-button v-hasPermi="['audit:tank-programmes:get']" link type="primary" @click="openDetailForm(scope.row.id)" v-if="scope.row.status === 1">查看</el-button>
          <el-button
            v-hasPermi="['audit:tank-programmes:update']"
            link
            type="primary"
            v-if="scope.row.status === 2 || scope.row.status === 0"
            @click="openForm('update', scope.row.id)"
          >编辑</el-button>
          <!-- <el-button
            link
            type="primary"
            @click="handleCopy(scope.row.id)"
            v-hasPermi="['system:audit-role:update']"
          >复制</el-button> -->
          <el-button
            v-hasPermi="['audit:tank-programmes:on-off']"
            link
            type="primary"
            v-if="scope.row.status === 2 || scope.row.status === 0"
            @click="handleChange(scope.row)"
          >启用</el-button>
          <el-button
            v-hasPermi="['audit:tank-programmes:on-off']"
            link
            type="danger"
            @click="handleChange(scope.row)"
            v-if="scope.row.status === 1"
          >停用</el-button>
          <el-button
            v-hasPermi="['audit:tank-programmes:delete']"
            link
            type="danger"
            v-if="scope.row.status !== 1"
            @click="handleDelete(scope.row.id)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <Form ref="formRef" @success="getList" :auditTypeList="auditTypeList" />
  <!-- 查看详情弹窗 -->
  <Detail ref="detailRef" />
  <!-- 预览附件 -->
	<DialogFlie ref="DialogFlieRef" />
</template>

<script setup lang="ts">
import { getIntDictOptions, getStrDictOptions, DICT_TYPE, NumberDictDataType } from '@/utils/dict'

import { handleTree } from '@/utils/tree'
import download from '@/utils/download'
import { AuditRoleApi, AuditRoleVO, AuditRoleDetailVO } from '@/api/basicData/auditRole'
import Form from './form.vue'
import { formatTime } from '@/utils'
import Detail from './Detail.vue'
import { AuditTypeApi } from '@/api/basicData/auditType'
import { TankProgrammesApi } from '@/api/jobmanger/solutionRepository'
import { TimeSplicing } from '@/utils/formatTime'
defineOptions({ name: 'SolutionRepository‌‌' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const { query } = useRoute() //接收路由传参
const loading = ref(true) // 列表的加载中
const list = ref<AuditRoleVO[]>([]) // 列表的数据
const queryParams = reactive({
  id: undefined,
  pageNo: 1,
  pageSize: 10,
  progName: undefined,
  auditType: undefined,
  companyUnit: undefined,
  status: undefined,
  // progKeynote: undefined,
  // progInfo: undefined
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const total = ref(0)

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await TankProgrammesApi.getTankProgrammesList(queryParams)
    list.value = data.list
    total.value = data.total
    // list.value = handleTree(data, 'id', 'id')
  } finally {
    loading.value = false
  }
}
const handleChange = async (row: any) => {
  // 修改状态的二次确认-草稿0/启用1/停用2
  const text = row.status === 2 ? '启用' : '停用'
  await message.confirm('请确认' + text + '"' + row.progName + '"，如果您不想'+ text +'此数据，请点击“取消”')
  // 发起修改
  await TankProgrammesApi.changeTankProgrammes(row.id)
  message.success(t('操作成功'))
  await getList()
}

// 查看按钮操作
const detailRef = ref()
const openDetailForm = (id: number) => {
  detailRef.value.open(id)
}

// 预览附件
const DialogFlieRef = ref()
const openDFlie = async(id: number) => {
  const fileId = await TankProgrammesApi.getFileData(id)
  if(fileId){
    DialogFlieRef.value.open('预览','VIEW',fileId)
  }else {
    message.warning('没有可查看附件。')
  }

}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}
const auditTypeList = ref()
const getTypeList = async () => {
  const data = await AuditTypeApi.getAuditTypeFirst(0)
  auditTypeList.value = handleTree(data, 'id', 'parentId')
  console.log(auditTypeList.value)
}
/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await TankProgrammesApi.deleteTankProgrammes(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await TankProgrammesApi.exportExcel(queryParams)
    const time = TimeSplicing(new Date())
    download.excel(data, `审计方案库${time}.xls`)
  } catch {
  } finally {
    exportLoading.value = false
  }
}
const handleCopy = async (id: number) => {
  await message.confirm('确定复制吗？')
  await TankProgrammesApi.copyTankProgramme(id)
  message.success(t('复制成功'))
  getList()
}

//智库首页跳转-查询详情
const projectId = ref({id: query.id as unknown as number})
const getDateil = async() => {
  if(projectId.value.id){
    await openDetailForm(projectId.value.id)
    // 使用一次后销毁传参
		const newUrl = await window.location.href.replace(/\?.*$/, '') //获取当前路径，并去除参数
		await history.replaceState({}, '', newUrl) //更新浏览器历史记录，不触发页面重新加载
  }
}

/** 初始化 **/
onMounted(() => {
  getTypeList()
  getList()
  getDateil()
})
</script>
