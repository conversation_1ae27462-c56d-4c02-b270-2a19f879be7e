<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="70%" :scroll="true">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      v-loading="formLoading"
      class="common-submit-form"
    >
      <el-row>
        <!-- <el-col :span="8"> -->
          <el-form-item label="方案名称" prop="progName">
            <el-input v-model="formData.progName" placeholder="请输入方案名称" class="!w-240px" />
          </el-form-item>
        <!-- </el-col> -->

        <!-- <el-col :span="8"> -->
          <el-form-item label="审计类型" prop="auditType">
            <!-- @visible-change="typeVisible"
            @change="typeChange" -->
            <el-select
              v-model="formData.auditType"
              filterable
              placeholder="请选择审计类型"
              clearable
              class="!w-240px"
            >
              <el-option
                v-for="dict in auditTypeList"
                :key="dict.id"
                :label="dict.auditTypeName"
                :value="dict.id"
              />
            </el-select>
          </el-form-item>
        <!-- </el-col> -->
        <!-- <el-col :span="8"> -->
          <el-form-item label="方案状态" prop="status">
            <el-select v-model="formData.status" placeholder="请选择方案状态" clearable class="!w-240px">
              <el-option
                v-for="dict in getIntDictOptions('audit_document_status')"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        <!-- </el-col> -->
        <!-- <el-col :span="8">
          <el-form-item label="方案编码" prop="">
            <el-input v-model="formData.progName" disabled placeholder="系统自动生成" class="!w-240px" />
          </el-form-item>
        </el-col> -->
      </el-row>
      <!-- <el-form-item label="方案内容" prop="progInfo">
        <Editor v-model="formData.progInfo" height="150px" />
      </el-form-item>
      <el-form-item label="审计内容和重点" prop="progKeynote">
        <Editor v-model="formData.progKeynote" height="150px" />
      </el-form-item> -->
      <div class="del_hover">
        <el-form-item label="模版列表" prop="attachments">
          <el-button type="primary" plain @click="handleImport('file')">
            <Icon icon="ep:upload" class="mr-5px" />选择文件
          </el-button>
        </el-form-item>
      </div>

			<el-table :data="formData.fileApiVOS" border :stripe="true" :show-overflow-tooltip="true" max-height="500px">
				<el-table-column label="版本号" align="center" prop="version" width="120" />
				<el-table-column label="文档名称" align="left" prop="docName"  min-width="120">
          <template #default="scope">
            <el-link :underline="false" type="primary" @click="handleView('预览','VIEW', scope.row.fileId)">{{ scope.row.docName }}</el-link>
          </template>
        </el-table-column>
				<el-table-column label="创建人" align="center" prop="creatorName" width="120" />
				<el-table-column label="创建时间" key="createTime" align="center" width="200">
					<template #default="{row}">
						<span>{{ formatDate(row.createTime) }}</span>
					</template>
				</el-table-column>
				<el-table-column label="状态" prop="status" align="center" width="120">
					<template #default="scope">
						<el-switch v-model="scope.row.status" inline-prompt :active-value="1" :inactive-value="0" active-text="有效" inactive-text="无效" @change="getChangData(scope.row, scope.$index)" />
					</template>
				</el-table-column>
				<el-table-column label="操作" align="center" width="170">
					<template #default="{row}">
						<el-button link type="primary" @click="handleDownload(row?.fileUrl,row?.docName)">下载</el-button>
						<el-button link type="danger" @click="listDeleteFile(row.id)" >删除</el-button>
					</template>
				</el-table-column>
			</el-table>
      <div class="del_hover">
        <el-form-item label="审计事项" prop="attachments" class="del_hover">
          <el-button type="primary" plain @click="showChoiceMatter">选择审计事项</el-button>
        </el-form-item>
      </div>

      <el-table
        :data="formData.treesResListVOS"
        border
        :stripe="true"
        :show-overflow-tooltip="true"
        max-height="500px"
      >
        <el-table-column type="index" label="序号" width="80" align="center"/>
        <el-table-column label="事项名称" align="left" prop="name" min-width="180" />
        <el-table-column label="上级事项" align="left" prop="parentName" min-width="180"/>
        <!-- <el-table-column label="事项描述" align="left" prop="mattersDesc" min-width="180">
          <template #default="{row}">
            <span v-html="row.mattersDesc"></span>
          </template>
        </el-table-column>
        <el-table-column label="审计步骤和方法" align="left" prop="stepMethod" min-width="180">
          <template #default="{row}">
            <span v-html="row.stepMethod"></span>
          </template>
        </el-table-column> -->
        <el-table-column label="操作" align="center" width="100">
          <template #default="scope">
            <el-button
              link
              type="danger"
              @click="listDelete(scope.row.id)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- <el-form-item label="进度安排" prop="scheduling">
        <Editor v-model="formData.scheduling" height="150px" />
      </el-form-item>
      <el-form-item label="审计要求" prop="auditAsk">
        <Editor v-model="formData.auditAsk" height="150px" />
      </el-form-item> -->
    </el-form>

    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>

  <!-- 选择审计事项 -->
  <AuditMatters ref="AuditMattersRef" @success="successMatterList" />
  <!-- 文件上传 -->
	<FileForm ref="formImgRef" @success="handleUploadSuccess" :type="fileType" :limit="fileLimit" />
  <!-- 预览附件 -->
	<DialogFlie ref="DialogFlieRef" />
</template>
<script setup lang="ts">
import { getIntDictOptions, getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { AuditRoleApi, AuditRoleVO } from '@/api/basicData/auditRole'
import FileForm from '@/views/infra/file/FileForm.vue'
import { TankProgrammesApi, AddVO, matterVo, fileListVo } from '@/api/jobmanger/solutionRepository'
import AuditMatters from './AuditMatters.vue'
import { formatDate } from '@/utils/formatTime'
import { downFile } from '@/utils/fileName'
/** 审计角色 表单 */
defineOptions({ name: 'solutionRepositoryForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  progName: undefined,
  status: undefined,
  progInfo: undefined,
  progKeynote: undefined,
  auditType: undefined,
  scheduling: undefined,
  auditAsk: undefined,
  treesResListVOS: [] as matterVo[],
  fileApiVOS: [] as fileListVo,
})
// 选择审计事项
const AuditMattersRef = ref()
const showChoiceMatter = () => {
  const auditType = formData.value.auditType
  AuditMattersRef.value.open(auditType)
}
const formRules = reactive({
  progName: [{ required: true, message: '不能为空', trigger: 'blur' }],
  progInfo: [{ required: true, message: '不能为空', trigger: 'blur' }],
  progKeynote: [{ required: true, message: '不能为空', trigger: 'blur' }],
  scheduling: [{ required: true, message: '不能为空', trigger: 'blur' }],
  auditAsk: [{ required: true, message: '不能为空', trigger: 'blur' }],
  status: [{ required: true, message: '不能为空', trigger: 'change' }],
})
const formRef = ref() // 表单 Ref
const props = defineProps({
  auditTypeList: {
    default: () => [],
    type: Array
  }
})
const successMatterList = (data) => {
  formData.value.treesResListVOS = formData.value.treesResListVOS??[]
  formData.value.treesResListVOS.push(...data)
  console.log(data, 'data')
}
const handleDelete = async (type: string, index: number = 0) => {
  await message.delConfirm()
  formData.value[type].splice(index, 1)
  await message.success(t('common.delSuccess'))
}

const typeVisible = async (visible: boolean) => {
  console.log(visible)

  if (visible && formData.value.treesResListVOS?.length > 0) {
    await message.confirm('当前列表已存在事项，确定切换审计类型吗？')
    formData.value.treesResListVOS = []
  }
}
const typeChange = async (value: any) => {
  if (value) {
    formData.value.treesResListVOS = await TankProgrammesApi.getMatterByAuditType(value)
  }
}

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await TankProgrammesApi.getDetail(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

// 删除模版列表
const listDeleteFile = async (id: number) => {
  await message.delConfirm()
  formData.value.fileApiVOS = formData.value.fileApiVOS.filter(
    (item) => item.id !== id
  )
}

// 删除审计事项
const listDelete = async (id: number) => {
  await message.delConfirm()
  formData.value.treesResListVOS = formData.value.treesResListVOS.filter(
    (item) => item.id !== id
  )
}

const listView = (val) => {}

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as AddVO
    if (formType.value === 'create') {
      await TankProgrammesApi.createTankProgrammes(data)
      message.success(t('common.createSuccess'))
    } else {
      await TankProgrammesApi.updateTankProgrammes(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

  const formImgRef = ref()
	const fileType = ref('img')
	const fileLimit = ref(1)
	// 上传方法
	const handleImport = (type : string) => {
		fileType.value = type
		// fileLimit.value = type === 'file' ? 5 : 1
		fileLimit.value = 1
		formImgRef.value.open()
	}
	const handleUploadSuccess = (fileList) => {
		console.log(fileList)
		formData.value.fileApiVOS = formData.value.fileApiVOS ?? []
		if (fileList && fileList.length > 0) {
			let currentVersion = 'V1.0'

			fileList.forEach((item, index) => {
				let res = item.response.data

				if (formData.value.fileApiVOS.length === 0) {
					currentVersion = 'V1.0'
				} else {
					// 获取最后一个文件的版本号
					const lastFileVersion = formData.value.fileApiVOS[0].version

					const versionParts = lastFileVersion.replace('V', '').split('.').map(Number)
					let major = versionParts[0]
					let minor = (versionParts[1] || 0) + 1
					console.log(minor)

					// 检查是否需要进位到下一个大版本
					if (minor >= 10) {
						major += 1
						minor = 0
					}

					currentVersion = `V${major}.${minor}`
				}
				// 创建新的文件对象并设置版本号
				const fileObj = {
					id: res.id, // 假设每个文件对象都有一个 'id' 属性
					version: currentVersion,
					docName: res.fileName,
					fileUrl: res.fileUrl,
					status: index === formData.value.fileApiVOS.length - 1 ? 1 : 0,
					creator: res.creatorName,
          creatorName: res.creatorName,
					createTime: res.createTime,
          fileId: res.id, //文件id
				}
				// 追加到 fileApiVOS 数组
				formData.value.fileApiVOS.unshift(fileObj)
				formData.value.fileApiVOS.forEach((file, index) => {
					file.status = index === 0 ? 1 : 0
				})
				console.log(formData.value.fileApiVOS)
			})
		}
	}

  // 设置有效文件
	const getChangData = (row,index) =>{
		console.log(row.status, index)
		if(row.status == 1){
			formData.value.fileApiVOS.map(item => {
				if(item.status == 1 && item.id != row.id){
					item.status = 0
				}
			})
		}
	}

  // 附件预览
	const DialogFlieRef = ref()
	const handleView = async (name: string, type: string, id: number) => {
		await DialogFlieRef.value.open(name, type, id)
	}

  //下载
	const handleDownload = async(url : string, name: string) => {
		// 下载的二次确认
    	await message.confirm('是否确认下载？')
		  downFile(url, name)
	}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    progName: undefined,
    status: undefined,
    progInfo: undefined,
    progKeynote: undefined,
    auditType: undefined,
    scheduling: undefined,
    auditAsk: undefined,
    treesResListVOS: [] as matterVo[],
    fileApiVOS: [] as fileListVo,
  }
  formRef.value?.resetFields()
}
</script>
