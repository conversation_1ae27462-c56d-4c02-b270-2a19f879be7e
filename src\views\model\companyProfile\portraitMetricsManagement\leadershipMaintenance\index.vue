<template>
  <ContentWrap class="common-card-search">
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px common-search-form"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="100px"
    >
      <el-form-item label="查询单位" prop="name">
        <el-select
          v-model="queryParams.name"
          placeholder="请选择查询单位"
          clearable
          class="!w-200px"
        >
          <el-option label="督办中" value="0" />
          <el-option label="未销号" value="1" />
          <el-option label="已销号" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="所属年度" prop="code">
        <el-input
          v-model="queryParams.code"
          placeholder="所属年度"
          clearable
          @keyup.enter="handleQuery"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item label="指标名称" prop="code">
        <el-input
          v-model="queryParams.code"
          placeholder="指标名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-200px"
        />
      </el-form-item>
    </el-form>
    <div class="right-search-btn">
      <el-button type="primary" @click="handleQuery">
        <Icon icon="ep:search" class="mr-5px" />搜索
      </el-button>
      <el-button @click="resetQuery"> <Icon icon="ep:refresh" class="mr-5px" />重置 </el-button>
    </div>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
        <div class="mb-10px flex_center">
            <el-button type="primary" plain >新增</el-button>
          </div>
    <el-table border v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="序号" width="60" align="center">
        <template #default="{ $index }">{{
          (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1
        }}</template>
      </el-table-column>
      <el-table-column label="年度" align="center" prop="projectYear" min-width="120" />
      <el-table-column label="公司" align="center" prop="projectYear" min-width="120" />
      <el-table-column label="指标名称" align="center" prop="projectYear" min-width="120" />
      <el-table-column label="实际值" align="center" prop="projectYear" min-width="120" />
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>
  <!-- 查看详情弹窗 -->
  <!-- 预览文件 -->
  <DialogFlie ref="dialogFlieRef" />
</template>

<script setup lang="ts">
// import { getIntDictOptions, getStrDictOptions, DICT_TYPE, NumberDictDataType } from '@/utils/dict'
// import { reformingStandingBookApi } from '@/api/auditManagement/auditRectifica/reformingStandingBook'
// import { formatTime } from '@/utils'
// import download from '@/utils/download'
import { DialogFlie } from '@/components/DialogFlie'
defineOptions({ name: 'LeadershipMaintenance' })

// const message = useMessage() // 消息弹窗

const loading = ref(true) // 列表的加载中
const list = ref([]) // 列表的数据
const queryParams = reactive({
  pageSize: 10,
  pageNo: 1,
  name: undefined,
  code: undefined
})
const queryFormRef = ref() // 搜索的表单
const total = ref(0)
/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    // const data = await reformingStandingBookApi.getRectificationLedgerList(queryParams)
    // list.value = data.list
    // total.value = data.total
    list.value = [
      {
        projectYear: '1'
      }
    ]
    total.value = list.value.length
  } finally {
    loading.value = false
  }
}
/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}
//预览文件
const dialogFlieRef = ref()
const handleShowMessage = (title: string, flietype: string, id: number) => {
  dialogFlieRef.value.open(title, flietype, id)
}
const detailRef = ref()
const handleStatusChange = () => {}
const openDetailForm = (data: any) => {
  console.log(data)
  return
  detailRef.value.open(data)
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
