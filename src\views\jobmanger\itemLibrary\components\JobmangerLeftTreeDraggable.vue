<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-12-04 18:24:15
 * @Description: 树形结构可拖动 =>
-->
<template>
	<div class="head-container">
		<el-row :gutter="8" :align="'middle'">
			<el-col :span="showEdit ? 18 : 24">
				<el-input v-model="name" class="mb-20px" clearable placeholder="请输入名称" v-if="showSearch">
					<template #prefix>
						<Icon icon="ep:search" />
					</template>
				</el-input>
			</el-col>
			<el-col :span="6" v-if="showEdit">
				<el-row :gutter="8" class="mb-16px">
					<el-col :span="8" class="cursor-pointer">
						<Icon icon="carbon:add-filled" :size="20" @click="openForm('create')" />
					</el-col>
					<!-- <el-col :span="8" class="cursor-pointer">
						<Icon icon="iconoir:edit" :size="20" @click="openForm('update', currentRow?.id)" />
					</el-col>
					<el-col :span="8" class="cursor-pointer">
						<Icon icon="weui:delete-outlined" :size="20" @click="handleDelete(currentRow?.id)" />
					</el-col> -->
				</el-row>
			</el-col>
		</el-row>

		<el-scrollbar height="calc(100vh - 90px)">
			<el-tree ref="treeRef" :data="deptList" :expand-on-click-node="false" :filter-node-method="filterNode"
				:props="defaultProps" :default-expand-all="false" highlight-current node-key="id" draggable
				@node-click="handleNodeClick" :default-expanded-keys="defaultExpandedKeys" :show-checkbox='checkbox'
    			@check-change="handleCheckChange" @node-drop="handleNodeDrop">
				<template #default="{ node, data }">
					<div style="display: flex;">
						<div class="custom-tree-node" @click="handleNodeClick(data)">
							<Icon :size="18" :icon="levelFil(node.level)" style="margin: 0 5px;"/>
          					<!-- <Icon icon="f7:doc" :size="12" /> -->
							<i class="custom-iconguanbi-quxiao-guanbi"></i>
							{{ node.label + (data.status == 2?'(停用)' : '')}}
						</div>
						<div class="but_right" v-if="showBut">
							<!-- <el-col :span="8" class="cursor-pointer">
								<Icon icon="carbon:add-filled" :size="20" @click="openForm('create', '', data?.id)" />
							</el-col> -->
							<el-col :span="8" class="cursor-pointer" v-if="data.status == 1">
								<Icon icon="line-md:circle-to-confirm-circle-transition" :size="20" @click="openEdit('update', data)" />
							</el-col>
							<el-col :span="8" class="cursor-pointer" v-else-if="data.status == 2">
								<Icon icon="line-md:alert-circle" :size="20" @click="openEdit('update', data)" />
							</el-col>
							<el-col :span="8" class="cursor-pointer">
								<Icon icon="iconoir:edit" :size="20" @click="openForm('update', data?.id)" />
							</el-col>
							<el-col :span="8" class="cursor-pointer">
								<Icon icon="weui:delete-outlined" :size="20" @click="handleDelete(data?.id)" />
							</el-col>
						</div>
					</div>
					
				</template>
			</el-tree>
		</el-scrollbar>
	</div>
	<JobmangerLeftTreeForm ref="formRef" @success="getTree" :searchUrl="searchUrl" :createUrl="createUrl"
		:editUrl="editUrl" :delUrl="delUrl" :detailsUrl="detailsUrl" :type="type" />
</template>

<script lang="ts" setup>
	import { ElTree } from 'element-plus'
	import * as DeptApi from '@/api/system/dept'
	import { defaultProps, handleTree, findPath, treeToList } from '@/utils/tree'
	import type Node from 'element-plus/es/components/tree/src/model/node'
	import { string } from 'vue-types'
	import JobmangerLeftTreeForm from './JobmangerLeftTreeForm.vue'
	import * as JobmangerLeftTreeApi from '@/api/jobmanger/jobmangerLeftTree'
	import type { AllowDropType, NodeDropType } from 'element-plus/es/components/tree/src/tree.type'
	import type { DragEvents } from 'element-plus/es/components/tree/src/model/useDragNode'
	defineOptions({ name: 'jobmangerLeftTree' })
	const { t } = useI18n() // 国际化
	const message = useMessage() // 消息弹窗
	interface TreeNode {
		id : number
		name : string
		parentId : number | string
		children ?: TreeNode[]
		isLeaf ?: boolean
	}
	// interface typeNode {
	// 	children: undefined
 	// 	id: "1857677474362277889"
	// 	label: "公司治理"
	// 	name: "公司治理"
	// 	parentId: null
	// 	parentName:  null
	// 	sort: null
	// 	treeOfType: null
	// 	typeCode: null
	// 	typeDesc: null
	// 	value: "1857677474362277889"
	// }
	const name = ref('')
	const deptList = ref<TreeNode[]>([]) // 树形结构
	const treeRef = ref<InstanceType<typeof ElTree>>()
	const queryParams = reactive({
		id: 0
	})
	const defaultExpandedKeys = ref<(string | number)[]>([])
	const levelFil = (level:number) => {
  const list = ['clarity:layers-solid','oui:layers','mynaui:layers-two','fluent:organization-48-regular','proicons:branch']
  return list[level - 1]
    }
  /** 获得部门树 */
	const getTree = async (id) => {
		currentRow.value = {}
		deptList.value = []
		const res = await JobmangerLeftTreeApi.getJobLeftTreeList(props.searchUrl)
		deptList.value.push(...handleTree(res, 'id', 'parentId'))
		defaultExpandedKeys.value = deptList.value.map((node) => node.id)
	}
	defineExpose({ getTree })
	const props = defineProps({
		showSearch: {
			default: true,
			type: Boolean
		},
		searchUrl: {
			default: '/',
			type: String
		},
		createUrl: {
			default: '/',
			type: String
		},
		editUrl: {
			default: '/',
			type: String
		},
		delUrl: {
			default: '/',
			type: String
		},
		detailsUrl: {
			default: '/',
			type: String
		},
		typeName: {
			default: '名称',
			type: String
		},
		showEdit: {
			default: true,
			type: Boolean
		},
		type: {
			default: '',
			type: String
		},
		checkbox: {
			default: false,
			type: Boolean
		},
		showBut:  {
			default: false,
			type: Boolean
		},
	})
	/** 基于名字过滤 */
	const filterNode = (name : string, data : TreeNode) => {
		if (!name) return true
		return data.name.includes(name)
	}
	const currentRow = ref()
	/** 处理部门被点击 */
	const handleNodeClick = async (row : { [key : string] : any }) => {
		currentRow.value = row
		emits('node-click', row)
	}

	// 删除
	const handleDelete = async (id : number) => {
		if (!id) {
			message.error('请选择需要删除的节点')
			return
		}
		await message.confirm('确定删除该节点吗？')
		await JobmangerLeftTreeApi.delJobLeftTreeList(props.delUrl, id)
		message.success(t('common.delSuccess'))
		// 刷新列表
		await getTree()
	}

	// 新增/修改
	const formRef = ref()
	const openForm = (type : string, id ?: number, parent: number) => {
		if (type === 'update') {
			if (!id) {
				message.error('请先选择节点')
				return
			}
		}
		formRef.value.open(type, id, parent)
	}

	// 是否启用
	const openEdit = async (type, data) => {
		if (!data?.id) {
			message.error(`请选择需要操作的节点`)
			return
		}
		if(data.status == 1){
			await message.confirm('确定停用该节点吗？停用该节点其所有子节点也将停用。')
		}else if(data.status == 2){
			await message.confirm('确定启用该节点吗？停用该节点其所有子节点也将启用。')
		}
		await JobmangerLeftTreeApi.updateJobLeftTreeList('/audit/tank-trees-type/update-state', {id:data.id, status: data.status==1?2:1})
		message.success('操作成功！')
		// 刷新列表
		await getTree()
	}

	const emits = defineEmits(['node-click', 'checkedGet'])
	/** name */
	watch(name, (val) => {
		treeRef.value!.filter(val)
	})

	// 选择树形结构触发事件
	const checkedIds = ref([])
	const handleCheckChange = (data: Tree, checked: boolean, indeterminate: boolean) => {
		if (checked) {
			// 添加到已选择节点 ID 数组
			checkedIds.value.push(data)
		} else {
			// 从已选择节点 ID 数组中移除
			checkedIds.value = checkedIds.value.filter((id) => id !== data.id)
		}
		emits('checkedGet', checkedIds.value)
	}

	// 拖拽成功完成时触发的事件
	// 拖动完成时获取拖动节点数据、被拖动位置以及拖动位置对应的数据，根据拖动位置，修改拖动节点父节点及排序
	// 被拖拽节点对应的 draggingNode、结束拖拽时最后进入的节点dropNode、被拖拽节点的放置位置（before、after、inner）、event
	const handleNodeDrop = async(draggingNode: any, dropNode: any, dropType: any, event: any) => {
		if(dropType == 'before'){
			if(dropNode.data){
				draggingNode.data.sort = (dropNode.data.sort&&dropNode.data.sort>0)? dropNode.data.sort - 1:0
				draggingNode.data.parentId = dropNode.data.parentId
			}
		}else if(dropType == 'after'){
			if(dropNode.data){
				draggingNode.data.sort = (dropNode.data.sort&&dropNode.data.sort>=0)? dropNode.data.sort + 1:0
				draggingNode.data.parentId = dropNode.data.parentId
			}
		}else if(dropType == 'inner'){
			if(dropNode.data){
				draggingNode.data.parentId = dropNode.data.id
			}
		}
		// 修改时，设置数据
		draggingNode.data.treeOfType = props.type
        try {
			await JobmangerLeftTreeApi.updateJobLeftTreeList(props.editUrl, draggingNode.data)
		    await message.success(t('操作成功！'))
		}catch{
        }
         finally {
			await getTree(queryParams.id)
		}
	}
 
	/** 初始化 */
	onMounted(async () => {
		await getTree(queryParams.id)
	})
</script>
<style scoped>
.el-tree-node__content {
	position: relative;
}
.but_right {
	display: none;
	position: absolute;
	right: 0;
	z-index: 999;
}
.el-tree-node__content:hover .but_right{
	display: flex;
	justify-content: center;
}
.cursor-pointer {
    padding-left: 8px !important;
}
.but_right .cursor-pointer {
    padding-left: 0px !important;
}
</style>
