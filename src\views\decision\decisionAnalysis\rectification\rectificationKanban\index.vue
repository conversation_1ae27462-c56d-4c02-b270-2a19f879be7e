<!-- 整改看板 -->
<template>
    <div class="header">
        <div class="header_title">
            <div class="header_title_size">
                整改问题统计
            </div>
            <div class="flex items-center">
                <div class="header_date mr-12px">
                    <div class="pr-14px">审计年度</div>
                    <el-date-picker v-model="queryParams.startYear" type="year" placeholder="开始" class="!w-90px"
                        @change="handleQuery" value-format="YYYY" />
                </div>
                <div class="header_date !w-270px">
                    <div class="pr-14px">公司名称</div>
                    <el-input v-model="queryParams.projectName" placeholder="请输入公司名称" clearable
                        @keyup.enter="handleQuery" class="!w-180px" />
                </div>
            </div>
        </div>
        <div class="flex items-center pt-12px pr-20px pl-20px">
            <div class="header_card1">
                <div class="header_card_title">
                    整改问题数
                </div>
                <el-row class="flex items-center pl-20px pt-10px">
                    <el-col :span="10" :xs="24">
                        <el-image style="width: 64px; height: 64px" :src="url1" fit="cover" />
                    </el-col>
                    <el-col :span="14" :xs="24">
                        <div class="header_card_number text-center">
                            {{topNumData.questionNum||0}}
                        </div>
                        <div class="header_card_text text-center">
                            整改问题总数
                        </div>
                    </el-col>
                </el-row>
            </div>
            <div class="header_card2">
                <div class="header_card_title">
                    未发起整改
                </div>
                <el-row class="flex items-center pl-20px pt-10px">
                    <el-col :span="6" :xs="24">
                        <el-image style="width: 64px; height: 64px" :src="url2" fit="cover" />
                    </el-col>
                    <el-col :span="9" :xs="24">
                        <div class="header_card_number text-center">
                            {{topNumData.unRunQuestionNum||0}}
                        </div>
                        <div class="header_card_text text-center">
                            未发起整改数
                        </div>
                    </el-col>
                    <el-col :span="9" :xs="24">
                        <div class="header_card_number text-center">
                            {{topNumData.unRunQuestionNumRate||0}}
                        </div>
                        <div class="header_card_text text-center">
                            占比
                        </div>
                    </el-col>
                </el-row>
            </div>
            <div class="header_card2">
                <div class="header_card_title">
                    已发起整改
                </div>
                <el-row class="flex items-center pl-20px pt-10px">
                    <el-col :span="6" :xs="24">
                        <el-image style="width: 64px; height: 64px" :src="url3" fit="cover" />
                    </el-col>
                    <el-col :span="9" :xs="24">
                        <div class="header_card_number text-center">
                            {{topNumData.executeQuestionNum||0}}
                        </div>
                        <div class="header_card_text text-center">
                            已发起整改数
                        </div>
                    </el-col>
                    <el-col :span="9" :xs="24">
                        <div class="header_card_number text-center">
                            {{topNumData.executeQuestionNumRate||0}}
                        </div>
                        <div class="header_card_text text-center">
                            占比
                        </div>
                    </el-col>
                </el-row>
            </div>
            <div class="header_card2">
                <div class="header_card_title">
                    整改中
                </div>
                <el-row class="flex items-center pl-20px pt-10px">
                    <el-col :span="6" :xs="24">
                        <el-image style="width: 64px; height: 64px" :src="url4" fit="cover" />
                    </el-col>
                    <el-col :span="9" :xs="24">
                        <div class="header_card_number text-center">
                            {{topNumData.executeIngQuestionNum||0}}
                        </div>
                        <div class="header_card_text text-center">
                            整改中的问题
                        </div>
                    </el-col>
                    <el-col :span="9" :xs="24">
                        <div class="header_card_number text-center">
                            {{topNumData.executeIngQuestionNumRate||0}}
                        </div>
                        <div class="header_card_text text-center">
                            占比
                        </div>
                    </el-col>
                </el-row>
            </div>
            <div class="header_card3">
                <div class="header_card_title">
                    整改完成
                </div>
                <el-row class="flex items-center pl-20px pt-10px">
                    <el-col :span="6" :xs="24">
                        <el-image style="width: 64px; height: 64px" :src="url5" fit="cover" />
                    </el-col>
                    <el-col :span="9" :xs="24">
                        <div class="header_card_number text-center">
                            {{topNumData.executedQuestionNum||0}}
                        </div>
                        <div class="card_text text-center">
                            整改完成数量
                        </div>
                    </el-col>
                    <el-col :span="9" :xs="24">
                        <div class="header_card_number text-center">
                            {{topNumData.executedQuestionNumRate||0}}
                        </div>
                        <div class="card_text text-center">
                            占比
                        </div>
                    </el-col>
                </el-row>
            </div>
        </div>
    </div>
    <el-row :gutter="8" class="pt-16px">
        <el-col :span="6" :xs="24">
            <ContentWrap>
                <div class="flex_title">
                    <div class="title-left"></div>
                    <div class="pl-8px">审计整改问题占比</div>
                </div>
                <div :class="oneChart" ref="oneChartRef" style="height:288px;width:100%;"></div>
            </ContentWrap>
        </el-col>
        <el-col :span="6" :xs="24">
            <ContentWrap>
                <div class="flex_title">
                    <div class="title-left"></div>
                    <div class="pl-8px">整改问题类型分布</div>
                </div>
                <div :class="twoChart" ref="twoChartRef" style="height:288px;width:100%;"></div>
            </ContentWrap>
        </el-col>
        <el-col :span="6" :xs="24">
            <ContentWrap>
                <div class="flex_title">
                    <div class="title-left"></div>
                    <div class="pl-8px">总体销号情况</div>
                </div>
                <div class="flex">
                    <div :class="threeChart" ref="threeChartRef" style="height:288px;width:100%;"></div>
                    <div :class="fiveChart" ref="fiveChartRef" style="height:288px;width:100%;"></div>
                </div>
            </ContentWrap>
        </el-col>
        <el-col :span="6" :xs="24">
            <ContentWrap>
                <div class="flex_title">
                    <div class="title-left"></div>
                    <div class="pl-8px">整改问题统计</div>
                </div>
                <div :class="fourChart" ref="fourChartRef" style="height:288px;width:100%;"></div>
            </ContentWrap>
        </el-col>
    </el-row>
    <el-row :gutter="8">
        <el-col :span="12" :xs="24">
            <ContentWrap>
                <div>
                    <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
                        <el-tab-pane v-for="item in editableTabs" :key="item.name" :label="item.title"
                            :name="item.name" />
                    </el-tabs>
                </div>
                <el-row v-show="activeName == '0'" :gutter="12">
                    <el-col v-for="(item, index) in datacard" :key="index" :span="8" :xs="24" class="pb-14px">
                        <div class="foot_cardflex">
                            <div class="">
                                <el-image style="width: 65px; height: 65px" :src="item.url" fit="cover" />
                            </div>
                            <div class="">
                                <div class="card_number">
                                    {{ item.number }} <span class="foot_cardflex_size">{{ item.unit }}</span>
                                </div>
                                <div class="card_text">
                                    {{ item.name }}
                                </div>
                            </div>
                        </div>
                    </el-col>
                </el-row>
                <el-row v-show="activeName == '1'" :gutter="12">
                    <el-col v-for="(item, index) in datacard1" :key="index" :span="8" :xs="24" class="pb-14px">
                        <div class="foot_cardflex">
                            <div class="">
                                <el-image style="width: 65px; height: 65px" :src="item.url" fit="cover" />
                            </div>
                            <div class="">
                                <div class="card_number">
                                    {{ item.number }} <span class="foot_cardflex_size">{{ item.unit }}</span>
                                </div>
                                <div class="card_text">
                                    {{ item.name }}
                                </div>
                            </div>
                        </div>
                    </el-col>
                </el-row>
                <el-row v-show="activeName == '2'" :gutter="12">
                    <el-col v-for="(item, index) in datacard2" :key="index" :span="8" :xs="24" class="pb-14px">
                        <div class="foot_cardflex">
                            <div class="">
                                <el-image style="width: 65px; height: 65px" :src="item.url" fit="cover" />
                            </div>
                            <div class="">
                                <div class="card_number">
                                    {{ item.number }} <span class="foot_cardflex_size">{{ item.unit }}</span>
                                </div>
                                <div class="card_text">
                                    {{ item.name }}
                                </div>
                            </div>
                        </div>
                    </el-col>
                </el-row>
            </ContentWrap>
        </el-col>
        <el-col :span="12" :xs="24">
            <ContentWrap>
                <div class="flex_title">
                    <div class="title-left"></div>
                    <div class="pl-8px">整改完成率排名</div>
                </div>
                <el-table :data="tableData" height="288px" :stripe="true">
                    <el-table-column prop="rank" label="排名" width="70" align="center">
                        <template #default="scope">
                            <el-image v-if="scope.$index == 0" style="width: 32px; height: 26px" :src="rankurl1"
                                fit="cover" />
                            <el-image v-else-if="scope.$index ==1" style="width: 32px; height: 26px" :src="rankurl2"
                                fit="cover" />
                            <el-image v-else-if="scope.$index == 2" style="width: 32px; height: 26px" :src="rankurl3"
                                fit="cover" />
                            <span v-else>{{ scope.$index+1 }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="companyName" label="公司名称" min-width="200" />
                    <el-table-column prop="rate" label="整改率(%)" align="center" width="120" />
                    <el-table-column prop="questionTotal" label="总问题数(个)" align="center" width="120" />
                    <el-table-column prop="normalNum" label="期内整改(个)" align="center" width="120" />
                    <el-table-column prop="timeOutNum" label="逾期整改(个)" align="center" width="120" />
                    <el-table-column prop="finishNum" label="完成整改(个)" align="center" width="120" />
                </el-table>
            </ContentWrap>
        </el-col>
    </el-row>
</template>

<script lang="ts" setup>
import * as echarts from 'echarts';
import demoviewIcon1 from '@/assets/imgs/decision/icon-zhenggai.png';
import demoviewIcon2 from '@/assets/imgs/decision/icon-weilixiang.png';
import demoviewIcon3 from '@/assets/imgs/decision/icon-yifaqi.png';
import demoviewIcon4 from '@/assets/imgs/decision/icon-zhixingzhong.png';
import demoviewIcon5 from '@/assets/imgs/decision/icon-yiwancheng.png';
// 金额类
import demoviewIcon6 from '@/assets/imgs/decision/icon-jinelei.png';
import demoviewIcon7 from '@/assets/imgs/decision/icon-shuifei.png';
import demoviewIcon8 from '@/assets/imgs/decision/icon-huishou.png';
import demoviewIcon9 from '@/assets/imgs/decision/icon-kuaijikemu.png';
import demoviewIcon10 from '@/assets/imgs/decision/icon-guihuanzijin.png';
import demoviewIcon11 from '@/assets/imgs/decision/icon-sunshi.png';
// 非金额类
import demoviewIcon12 from '@/assets/imgs/decision/icon-feijine.png';
import demoviewIcon13 from '@/assets/imgs/decision/icon-xinzhiding.png';
import demoviewIcon14 from '@/assets/imgs/decision/icon-wanshan.png';
import demoviewIcon15 from '@/assets/imgs/decision/icon-yinhangzhagnhu.png';
import demoviewIcon16 from '@/assets/imgs/decision/icon-xiuding.png';
import demoviewIcon17 from '@/assets/imgs/decision/icon-zhaiwu.png';
// 追责问责
import demoviewIcon18 from '@/assets/imgs/decision/icon-zhuize.png';
import demoviewIcon19 from '@/assets/imgs/decision/icon-yijiao.png';
import demoviewIcon20 from '@/assets/imgs/decision/icon-jiwei.png';
import demoviewIcon21 from '@/assets/imgs/decision/icon-wenze.png';
import demoviewIcon22 from '@/assets/imgs/decision/icon-niandu.png';
import demoviewIcon23 from '@/assets/imgs/decision/icon-xingzheng.png';
// 排名
import demoviewIcon24 from '@/assets/imgs/decision/icon-top1.png';
import demoviewIcon25 from '@/assets/imgs/decision/icon-top2.png';
import demoviewIcon26 from '@/assets/imgs/decision/icon-top3.png';
import { colorList } from '../../index'
import { RectificationApi, TopNumVO, FinishRateVO } from '@/api/decision/rectification'
defineOptions({
    name: 'RectificationKanban'
})
const queryParams = reactive({
    startYear: undefined,
    projectName: undefined,
})
const loading = ref(true) // 列表的加载中

const url1 = ref(demoviewIcon1)
const url2 = ref(demoviewIcon2)
const url3 = ref(demoviewIcon3)
const url4 = ref(demoviewIcon4)
const url5 = ref(demoviewIcon5)

const rankurl1 = ref(demoviewIcon24)
const rankurl2 = ref(demoviewIcon25)
const rankurl3 = ref(demoviewIcon26)

const topNumData = ref({} as TopNumVO)
interface DataCardItem {
    url: string;
    number: string;
    name: string;
    unit: string;
}
const datacard = ref<DataCardItem[]>([])
const datacard1 = ref<DataCardItem[]>([])
const datacard2 = ref<DataCardItem[]>([])

const activeName = ref('0')
const editableTabs = ref([
    {
        title: '金额类',
        name: '0',
    },
    {
        title: '非金额类',
        name: '1',
    },
    {
        title: '追责问责',
        name: '2',
    }
])
const handleClick = () => {

}
const color = ['#2A71C0', '#FF9C01', '#AC3C52', '#C96F37', '#95C2ED', '#E6E8F4', '#ABEDD8']
const getAllApi = async () => {
    topNumData.value = await RectificationApi.getTopNum(queryParams)
    const questionChartData  = await RectificationApi.getQuestionChart(queryParams)
    const typePineData = await RectificationApi.getTypePine(queryParams)
    const questionFinishData  = await RectificationApi.getQuestionFinish(queryParams)
    const questionNumChartData  = await RectificationApi.getQuestionNumChart(queryParams)
    const totalData = await RectificationApi.getTotal(queryParams)
    tableData.value = await RectificationApi.getFinishRate(queryParams)
    console.log(questionNumChartData,questionChartData);
    await Promise.all([getOneChart(questionChartData)])
    await Promise.all([getTwoChart(typePineData)])
    await Promise.all([getThreeChart(questionFinishData)])
    await Promise.all([getFourChart(questionNumChartData)])
    await Promise.all([getFiveChart(questionFinishData)])

    datacard.value = [{
        url: demoviewIcon6,
        number: totalData.money,
        name: '金额类总计',
        unit: '万元',
    }, {
        url: demoviewIcon7,
        number: totalData.taxMoney,
        name: '补齐税费',
        unit: '万元',
    }, {
        url: demoviewIcon8,
        number: totalData.recycleMoney,
        name: '回收资金',
        unit: '万元',
    }, {
        url: demoviewIcon9,
        number: totalData.accountingMoney,
        name: '调整会计科目',
        unit: '万元',
    }, {
        url: demoviewIcon10,
        number: totalData.backMoney,
        name: '归还原资金渠道',
        unit: '万元',
    }, {
        url: demoviewIcon11,
        number: totalData.lossMoney,
        name: '挽回损失',
        unit: '万元',
    }]
    datacard1.value = [{
        url: demoviewIcon12,
        number: totalData.noTeeTotal,
        name: '非金额类总计',
        unit: '条',
    }, {
        url: demoviewIcon13,
        number: totalData.newInstitutionTotal,
        name: '新制定制度',
        unit: '条',
    }, {
        url: demoviewIcon14,
        number: totalData.betterBusinessTotal,
        name: '完善业务流程',
        unit: '条',
    }, {
        url: demoviewIcon15,
        number: totalData.clearBankAccountTotal,
        name: '清理银行账户',
        unit: '条',
    }, {
        url: demoviewIcon16,
        number: totalData.betterInstitutionTotal,
        name: '修订完善制度',
        unit: '条',
    }, {
        url: demoviewIcon17,
        number: totalData.clearClaims,
        name: '清理债券债务',
        unit: '条',
    }]
    datacard2.value = [{
        url: demoviewIcon18,
        number: totalData.accountabilityTotal,
        name: '追责问责类总计',
        unit: '人',
    }, {
        url: demoviewIcon19,
        number: totalData.moveDisciplineNum,
        name: '移交纪委',
        unit: '人',
    }, {
        url: demoviewIcon20,
        number: totalData.handleDisciplineNum,
        name: '纪律处分',
        unit: '人',
    }, {
        url: demoviewIcon21,
        number: totalData.handleAccountabilityNum,
        name: '问责处理',
        unit: '人',
    }, {
        url: demoviewIcon22,
        number: totalData.handleExamineNum,
        name: '绩效考核',
        unit: '人',
    }, {
        url: demoviewIcon23,
        number: totalData.handleWarningNum,
        name: '行政警告',
        unit: '人',
    }]
    loading.value = false
}

// 第一个echarts
let oneChart: echarts.ECharts | null = null;
const oneChartRef = ref()
const getOneChart = async (val) => {
    oneChart = echarts.init(oneChartRef.value)
    const dataList = val.rateList.map((item) => item.unRunQuestionNum)
	const dataList1 = val.rateList.map((item) => item.executeIngQuestionNum)
	const dataList2 = val.rateList.map((item) => item.executedQuestionNum)
    oneChart.setOption({
        color: color,
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            }
        },
        legend: {
            data: ['未发起', '整改中', '整改完成'],
            itemWidth: 14,
            top: 'bottom',
            itemHeight: 14,
            textStyle: {
                lineHeight: 14
            }
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '15%',
            top: '10%',
            containLabel: true
        },
        xAxis: {
            type: 'value'
        },
        yAxis: {
            type: 'category',
            axisTick: {
                show: false
            },
            data: val.typeName
        },
        series: [{
            type: 'bar',
            name: '未发起',
            stack: 'total',
            label: {
                show: false,
                color: '#fff',
                // formatter: '{c}%'
            },
            emphasis: {
                focus: 'series'
            },
            data: dataList,
            itemStyle: {
                color:'#1054B1'
            },
            barMaxWidth: 40,
        }, {
            type: 'bar',
            name: '整改中',
            stack: 'total',
            label: {
                show: false,
                color: '#fff',
            },
            emphasis: {
                focus: 'series'
            },
            data: dataList1,
            itemStyle: {
                color:'#A1C7ED'
            },
            barMaxWidth: 40,
        }, {
            type: 'bar',
            name: '整改完成',
            stack: 'total',
            label: {
                show: false,
                color: '#fff',
            },
            emphasis: {
                focus: 'series'
            },
            data: dataList2,
            barMaxWidth: 40,
            itemStyle: {
                borderRadius: [0, 10, 10, 0],
                color:'#DEE8FF'
            }
        }]
    })
}

// 第二个echarts
let twoChart: echarts.ECharts | null = null;
const twoChartRef = ref()
const getTwoChart = async (val) => {
    twoChart = echarts.init(twoChartRef.value)
    const data = val
    data.forEach((item) => {
        item.name = item.typeName
        item.value = item.typeNum
    })
    twoChart.setOption({
        color: color,
        legend: {
            top: 'bottom',
            itemWidth: 14,
            data: data,
            itemHeight: 14,
            textStyle: {
                lineHeight: 14
            }
        },
        series: [
            {
                name: '姓名',
                type: 'pie',
                radius: [20, 100],
				center: ['50%', '40%'],
                roseType: 'area',
                label: {
                    show: false,
                    position: 'center'
                },
                itemStyle: {
					borderRadius: 2,
					normal: {
                        color: function (params) {
                            return colorList[params.dataIndex];
                        },
                    },
				},
				emphasis: {
					itemStyle: {
						shadowBlur: 10,
						shadowOffsetX: 0,
						shadowColor: 'rgba(0, 0, 0, 0.5)'
					}
				},
                data: data
            }
        ]
    })
}

// 第三个echarts
let threeChart: echarts.ECharts | null = null;
const threeChartRef = ref()
const getThreeChart = async (val) => {
    threeChart = echarts.init(threeChartRef.value)
    const data = [{
        value: val.unFinishTimeOutNum,
        name: "逾期整改"
    }, {
        value: val.unFinishNum,
        name: "期内整改"
    }]
    threeChart.setOption({
        color: color,
        legend: {
            top: 'bottom',
            itemWidth: 14,
            data: data,
            itemHeight: 14,
            textStyle: {
                lineHeight: 14
            },
            orient: "vertical"
        },
        series: [
            {
                name: '姓名',
                type: 'pie',
                radius: ['50%', '60%'],
                center: ['50%', '40%'],
                avoidLabelOverlap: false,
                label: {
                    show: false,
                    position: 'center'
                },
                emphasis: {
                    label: {
                        show: true,
                        fontSize: 14,
                        formatter: function (params) {
                            return '{total|' + params.value + '}' + '\n ' + params.name;
                        },
                        rich: {
                            total: {
                                fontSize: 25,
                                color: '#000',
                                lineHeight: 40
                            }
                        }
                    }
                },
                labelLine: {
                    show: false
                },
                data: data
            }
        ]
    })
}

// 第三个echarts
let fiveChart: echarts.ECharts | null = null;
const fiveChartRef = ref()
const getFiveChart = async (val) => {
    fiveChart = echarts.init(fiveChartRef.value)
    const data = [{
        value: val.finishTimeOutNum,
        name: "逾期销号"
    }, {
        value: val.finishNum,
        name: "按期销号"
    }]
    fiveChart.setOption({
        color: color,
        legend: {
            top: 'bottom',
            itemWidth: 14,
            data: data,
            itemHeight: 14,
            textStyle: {
                lineHeight: 14
            },
            orient: "vertical"
        },
        series: [
            {
                name: '姓名',
                type: 'pie',
                radius: ['50%', '60%'],
                center: ['50%', '40%'],
                avoidLabelOverlap: false,
                label: {
                    show: false,
                    position: 'center'
                },
                emphasis: {
                    label: {
                        show: true,
                        fontSize: 14,
                        formatter: function (params) {
                            return '{total|' + params.value + '}' + '\n ' + params.name;
                        },
                        rich: {
                            total: {
                                fontSize: 25,
                                color: '#000',
                                lineHeight: 40
                            }
                        }
                    }
                },
                labelLine: {
                    show: false
                },
                data: data
            }
        ]
    })
}

// 第四个echarts
let fourChart: echarts.ECharts | null = null;
const fourChartRef = ref()
const getFourChart = async (val) => {
    fourChart = echarts.init(fourChartRef.value)
    fourChart.setOption({
        color: color,
        grid: {
            top: "10%"
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross',
                crossStyle: {
                    color: '#999'
                }
            }
        },
        legend: {
            data: ['发现问题', '整改问题'],
            itemWidth: 14,
            top: 'bottom',
            itemHeight: 14,
            textStyle: {
                lineHeight: 14
            }
        },
        xAxis: [
            {
                type: 'category',
                data: val.typeName,
                axisPointer: {
                    type: 'shadow'
                },
                axisLabel: {
                    interval: 0,
                    formatter: function (value) {
                        // 返回简短版本的标签文本
                        return value.substring(0, 1) + '...'; // 截取前8个字符并添加省略号
                    }
                }
            }
        ],
        yAxis: [
            {
                type: 'value',
            }
        ],
        series: [
            {
                name: '发现问题',
                type: 'bar',
                data: val.findQuestionNum,
                barMaxWidth: 40,
            },
            {
                name: '整改问题',
                type: 'bar',
                data: val.questionNum,
                barMaxWidth: 40,
            }
        ]
    })
}

// 第五个echarts
const tableData = ref<FinishRateVO[]>([])

/** 搜索按钮操作 */
const handleQuery = () => {
    console.log('000')
    getAllApi()
}

/** 初始化 **/
onMounted(() => {
    getAllApi()
})
</script>

<style scoped>
/* 去除输入框边框 */
::v-deep .el-date-editor {
    --el-input-border-color: transparent !important;
    --el-input-bg-color: transparent;
    --el-input-hover-border-color: #fff;
}

::v-deep .el-input {
    --el-input-border-color: transparent !important;
    --el-input-bg-color: transparent;
    --el-input-hover-border-color: #fff;
}

::v-deep .el-tabs__nav-wrap::after {
    /* 去掉下划线 */
    position: static !important;
}

.flex_title {
    display: flex;
    align-items: center;
    line-height: 33px;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 700;
    font-size: 16px;
    color: #333333;
    border-bottom: 1px solid #EEEFF0;
}

.title-left {
    width: 5px;
    height: 15px;
    background: #2F4CAD;
    border-radius: 0px 0px 0px 0px;
}

.header {
    height: 199px;
    border: 2px solid rgba(255, 255, 255, 1);
    background: #F7F7F7;
	border-radius: 8px;
}

.header_title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 20px 0;
}

.header_title_size {
    height: 26px;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 500;
    font-size: 18px;
    color: #333333;
    line-height: 26px;
}

.header_date {
    width: 174px;
    height: 34px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 8px 8px 8px 8px;
    border: 2px solid #FFFFFF;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    font-size: 15px;
    color: #999;
    line-height: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.header_card1 {
    flex: 1;
    height: 125px;
    background: linear-gradient(160.24deg, #A0BDF0 13.03%, #07397E 86.79%);
    border-radius: 8px 8px 8px 8px;
    border: 2px solid #FFFFFF;
}

.header_card2 {
    flex: 1.5;
    height: 125px;
    background: linear-gradient(160.24deg, #A0BDF0 13.03%, #07397E 86.79%);
    border-radius: 8px 8px 8px 8px;
    border: 2px solid #FFFFFF;
    margin-left: 6px;
}

.header_card3 {
    flex: 1.5;
    height: 125px;
    background: linear-gradient(160.24deg, #FCC535 13.03%, #FF7F00 86.79%);
    border-radius: 8px 8px 8px 8px;
    border: 2px solid #FFFFFF;
    margin-left: 6px;
}

.header_card_title {
    height: 23px;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    font-size: 16px;
    color: #fff;
    line-height: 23px;
    padding-top: 14px;
    padding-left: 20px;
}

.header_card_item {
    flex: 1;
}
.header_card_number{
    height: 40px;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 700;
    font-size: 26px;
    color: #fff;
    line-height: 40px;
}

.header_card_text{
    height: 21px;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    font-size: 15px;
    color: #fff;
    line-height: 21px;
}

.card_number {
    height: 40px;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 700;
    font-size: 21px;
    color: #333333;
    line-height: 40px;
}

.card_text {
    height: 21px;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    font-size: 15px;
    color: #999999;
    line-height: 21px;
}

.foot_cardflex {
    display: flex;
    align-items: center;
    height: 119px;
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #EEEFF0;
    justify-content: space-evenly;
}

.foot_cardflex_size {
    font-size: 15px;
}
</style>