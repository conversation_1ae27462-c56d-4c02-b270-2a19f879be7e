<!--
* @Author: lijunliang
* @Date: 2024-09-11 15:22:50
* @Description: 问题定性库=>
-->
<template>
	<el-row :gutter="16">
		<el-col :span="5" :xs="24">
			<ContentWrap class="h-1/1">
				<el-tabs v-model="activeName" class="demo-tabs" @tab-change="handleClick">
					<el-tab-pane label="审计事项" name="2">
						<JobmangerLeftTree ref="JobmangerLeftTreeRef" @node-click="handleDeptNodeClick" :searchUrl="searchUrl" :showEdit='false' />
					</el-tab-pane>
					<el-tab-pane label="定性分类" name="1">
						<JobmangerLeftTree ref="JobmangerLeftTreeRef" @node-click="handleDeptNodeClick" :createUrl="createUrl" :editUrl="editUrl"
					:searchUrl="searchUrl" :delUrl="delUrl" :detailsUrl="detailsUrl" :type='5' :showBut='true' />
					</el-tab-pane>
				</el-tabs>
			</ContentWrap>
		</el-col>
		<el-col :span="19" :xs="24">
			<ContentWrap class="common-card-search">
				<!-- 搜索工作栏 -->
				<el-form class="-mb-15px common-search-form" :model="queryParams" ref="queryFormRef" :inline="true" label-width="110px">
					<el-form-item label="问题定性" prop="quesName">
						<el-input v-model="queryParams.quesName" placeholder="请输入问题定性" clearable
							@keyup.enter="handleQuery" class="!w-200px" />
					</el-form-item>
					<el-form-item label="定性分类" prop="typeName">
						<el-input v-model="queryParams.typeName" placeholder="请输入定性分类" clearable
							@keyup.enter="handleQuery" class="!w-200px" />
					</el-form-item>
					<el-form-item label="定性描述" prop="quesDesc">
						<el-input v-model="queryParams.quesDesc" placeholder="请输入问题描述" clearable
							@keyup.enter="handleQuery" class="!w-200px" />
					</el-form-item>
					<el-form-item label="定性状态" prop="status">
						<el-select v-model="queryParams.status" placeholder="请选择问题状态" clearable class="!w-200px">
							<el-option v-for="dict in getIntDictOptions('audit_document_status')" :key="dict.value"
								:label="dict.label" :value="dict.value" />
						</el-select>
					</el-form-item>
				</el-form>
		<div class="right-search-btn">
			<el-button  type="primary"  @click="handleQuery">
					<Icon icon="ep:search" class="mr-5px" />搜索
				</el-button>
				<el-button @click="resetQuery">
					<Icon icon="ep:refresh" class="mr-5px" />重置
				</el-button>
		</div>
			</ContentWrap>
			<!-- 列表 -->
			<ContentWrap>
      <div class="button_margin15">
						<el-button v-hasPermi="['audit:tank-ques-qualitation:create']" type="primary" plain v-if="activeName == 2"
							@click="openForm('create',queryParams.questId)">
							<Icon icon="ep:plus" class="mr-5px" />新增问题定性
						</el-button>
						<!-- <el-button v-show="activeName==='2'" type="primary" plain @click="addForm">
							<Icon icon="ep:plus" class="mr-5px" />新增审计事项
						</el-button> -->

						<el-button type="primary" plain v-if="activeName == 1" :disabled="idList.length == 0"
							@click="handleType('more')">
							<Icon icon="ep:plus" class="mr-5px" />批量调整分类
						</el-button>
						<el-button v-hasPermi="['audit:tank-ques-qualitation:export']" :loading="exportLoading" plain
							@click="handleExport">
							<Icon class="mr-5px" icon="ep:download" />
							导出
						</el-button>
      </div>
				<el-table v-loading="loading" :data="list" :stripe="true" border :show-overflow-tooltip="true" @selection-change="handleSelectionChange">
					<el-table-column v-if="activeName == 1" fixed="left" type="selection" width="50"/>
					<el-table-column label="序号" width="60" align="center">
						<template
							#default="{ $index }">{{ (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1 }}</template>
					</el-table-column>
					<el-table-column v-if="activeName == 1" label="定性分类" align="left" prop="typeName" min-width="200" />
					<el-table-column label="问题定性" align="left" prop="quesName" min-width="200"/>
					<el-table-column label="定性描述" align="left" prop="quesDesc" min-width="260"/>
					<el-table-column label="审计建议" align="left" prop="auditSugg" min-width="260"/>
					<el-table-column v-if="activeName == 2" label="定性分类" align="left" prop="typeName" min-width="200" />
					<el-table-column label="法律法规" align="left" prop="statute" min-width="180"/>
					<el-table-column label="监督规定" align="left" prop="stipulate" min-width="180"/>
					<el-table-column v-if="activeName==='2'" label="审计事项" align="left" prop="auditMattersName" min-width="180"/>
					<el-table-column label="定性状态" align="center" prop="status" min-width="100">
						<template #default="scope">
							<dict-tag :type="'audit_document_status'" :value="scope.row.status" />
						</template>
					</el-table-column>
					<el-table-column label="创建人" align="center" prop="createrName" min-width="100"/>
					<el-table-column label="备注" align="left" prop="remark" min-width="180"/>
					<el-table-column label="操作" align="center" :width="activeName == 2?220:activeName == 1?120:''" fixed="right">
						<template #default="scope">
							<div v-if="activeName == 2">
								<el-button v-hasPermi="['audit:tank-ques-qualitation:get']" link type="primary" v-show="scope.row.status == 1" @click="openDetailForm(scope.row.id, scope.row.quesId)">查看</el-button>
								<el-button v-hasPermi="['audit:tank-ques-qualitation:update']" link type="primary" v-show="scope.row.status != 1"
									@click="openForm('update', queryParams.questId,scope.row.id)">编辑</el-button>
								<el-button v-hasPermi="['audit:tank-ques-qualitation:on-off']" link type="primary" v-show="scope.row.status !== 1"
									@click="handleStatus(scope.row,1)">启用</el-button>
								<el-button v-hasPermi="['audit:tank-ques-qualitation:on-off']" link type="danger" v-show="scope.row.status == 1"
									@click="handleStatus(scope.row,2)">停用</el-button>
								<el-button v-hasPermi="['audit:tank-ques-qualitation:delete']" v-show="scope.row.status != 1 " link type="danger" @click="handleDelete(scope.row)">删除</el-button>
							</div>
							<div v-if="activeName == 1">
								<el-button link type="primary"
									@click="handleType('one', [scope.row.id])">调整分类</el-button>
							</div>
						</template>
					</el-table-column>
				</el-table>
				<!-- 分页 -->
				<Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
					@pagination="getList" />
			</ContentWrap>
		</el-col>
	</el-row>

	<!-- 表单弹窗：添加/修改 -->
	<Form ref="formRef" @success="handleRefresh()" />
	<!-- 查看详情弹窗 -->
	<Detail ref="detailRef" />
	<Disposition ref="DispositionRef" @success="getList" />
	<!-- 调整分类 -->
	<Controls ref="controlsRef" @success="getList" />
</template>

<script setup lang="ts">
	import { getIntDictOptions, getStrDictOptions, DICT_TYPE, NumberDictDataType } from '@/utils/dict'
	import { handleTree } from '@/utils/tree'
	import download from '@/utils/download'
	import { QualitativeAnalysisApi, QualitativeAnalysisVO, QualitativeAnalysisDetailVO } from '@/api/jobmanger/problem/qualitativeAnalysis'
	import Form from './form.vue'
	import { formatTime } from '@/utils'
	import Detail from './Detail.vue'
	// import JobmangerLeftTree from '@/views/jobmanger/components/JobmangerLeftTree.vue'
	import JobmangerLeftTree from '@/views/jobmanger/itemLibrary/components/JobmangerLeftTree.vue'
	import Disposition from './disposition.vue'
	import { TimeSplicing } from '@/utils/formatTime'
	import Controls from './Controls.vue'
	defineOptions({ name: 'QualitativeAnalysis‌‌' })

	const message = useMessage() // 消息弹窗
	const { t } = useI18n() // 国际化
	const { query } = useRoute() //接收路由传参
	const loading = ref(true) // 列表的加载中
	const list = ref<QualitativeAnalysisVO[]>([]) // 列表的数据
	const queryParams = reactive({
		id: undefined,
		pageSize: 10,
		pageNo: 1,
		quesName: undefined,
		quesDesc: undefined,
		status: undefined,
		typeId: undefined,
		auditMattersId: undefined,
		questId: undefined,
		typeName: undefined,
	})
	const queryFormRef = ref() // 搜索的表单
	const exportLoading = ref(false) // 导出的加载中
	const total = ref(0)

	// 问题分类接口
	const JobmangerLeftTreeRef = ref()
	var searchUrl = ref('/audit/tank-trees-type/get?type=4')
	const createUrl = '/audit/tank-trees-type/create'
	const editUrl = '/audit/tank-trees-type/update'
	const delUrl = '/audit/tank-trees-type/delete'
	const detailsUrl = '/audit/tank-trees-type/get-tree'
	const activeName = ref('2')
	const handleClick = async (type) => {
		if (activeName.value == '1') {
			await (searchUrl.value = '/audit/tank-trees-type/get?type=5')
			JobmangerLeftTreeRef.value.getTree()
		} else {
			await (searchUrl.value = '/audit/tank-trees-type/get?type=4')
			JobmangerLeftTreeRef.value.getTree()
		}
		queryParams.auditMattersId = undefined
		queryParams.typeId = undefined
		queryParams.questId = undefined
		await getList()
	}
	const handleRefresh = async () => {
		if (activeName.value == '1') {
			await (searchUrl.value = '/audit/tank-trees-type/get?type=5')
			JobmangerLeftTreeRef.value.getTree()
		} else {
			await (searchUrl.value = '/audit/tank-trees-type/get?type=4')
			JobmangerLeftTreeRef.value.getTree()
		}
		await getList()
	}

	/** 查询列表 */
	const getList = async () => {
		loading.value = true
		try {
			let data = {}
			data = await QualitativeAnalysisApi.getQualitativeAnalysisList(queryParams)
			list.value = data.list
			total.value = data.total
		} finally {
			loading.value = false
		}
	}

	// 查看
	const detailRef = ref()
	const openDetailForm = ( id:number, quesId : number) => {
		detailRef.value.open(activeName.value, id, quesId)
	}

	/** 搜索按钮操作 */
	const handleQuery = () => {
		queryParams.pageNo = 1
		getList()
	}

	/** 重置按钮操作 */
	const resetQuery = () => {
		queryFormRef.value.resetFields()
		handleQuery()
	}

	/** 处理部门被点击 */
	const handleDeptNodeClick = async (row) => {
		if(activeName.value == 2){
			queryParams.typeId = undefined
			queryParams.auditMattersId = row.id
		}else if(activeName.value == 1) {
			queryParams.typeId = row.id
			queryParams.auditMattersId = undefined
		}
		queryParams.questId = row.id
		await getList()
	}

	/** 添加/修改操作 */
	const formRef = ref()
	const openForm = (type : string, matterId : any, id ?: number) => {
		formRef.value.open(type, matterId, id, activeName.value)
	}

	// 启用/作废
	const handleStatus = async (row : WritlibraryVO, statu : number) => {
		try {
			// 修改状态的二次确认-草稿0/启用1/停用2
			const text = row.status === 2 ? '启用' : '停用'
			await message.confirm('请确认' + text + '"' + row.quesName + '"，如果您不想'+ text +'此数据，请点击“取消”')
			// 发起修改状态
			await QualitativeAnalysisApi.changeQualitativeAnalysisList({ id: row.id, status: statu })
			message.success('操作成功')
			// 刷新列表
			await handleRefresh()
		} catch { }
	}

	/** 删除按钮操作 */
	const handleDelete = async (val) => {
		try {
			// 删除的二次确认
			await message.delConfirm()
			if (activeName.value == '1') {
				// 发起删除
				await QualitativeAnalysisApi.deleteQualitativeAnalysis(val.id)
			} else {
				await QualitativeAnalysisApi.deleteQualitativeAnalysisRelation(val.id)
			}
			message.success(t('common.delSuccess'))
			// 刷新列表
			await handleRefresh()
		} catch { }


	}

	/** 导出按钮操作 */
	const handleExport = async () => {
		try {
			// 导出的二次确认
			await message.exportConfirm()
			// 发起导出
			exportLoading.value = true
			if (activeName.value == '1') {
				const data = await QualitativeAnalysisApi.exportQualitativeAnalysis(queryParams)
				const time = TimeSplicing(new Date())
    			download.excel(data, `问题定性库${time}.xls`)
			} else {
				const data = await QualitativeAnalysisApi.itemExportQualitativeAnalysis(queryParams)
				const time = TimeSplicing(new Date())
    			download.excel(data, `问题定性库1${time}.xls`)
			}
		} catch {
		} finally {
			exportLoading.value = false
		}
	}

	// 新增
	const DispositionRef = ref()
	const addForm = () => {
		DispositionRef.value.open()
	}

	// 列表多选触发事件
	const idList = ref([])
	const handleSelectionChange = async(val) => {
		idList.value = val.map(item => {return item.id})
	}

	//调整分类按钮
	const controlsRef = ref()
	const handleType = async(type: string, id: []) => {
		const data = ref([])
		if(type == 'one'){
			data.value = id
		}else if(type == 'more') {
			data.value = idList.value
		}
		controlsRef.value.open('选择定性分类', 1, data.value)
	}

	//智库首页跳转-查询详情
	const projectId = ref({id: query.id as unknown as number})
	const getDateil = async() => {
		if(projectId.value.id){
			await openDetailForm(projectId.value.id)
			// 使用一次后销毁传参
			const newUrl = await window.location.href.replace(/\?.*$/, '') //获取当前路径，并去除参数
			await history.replaceState({}, '', newUrl) //更新浏览器历史记录，不触发页面重新加载
		}
	}

	/** 初始化 **/
	onMounted(() => {
		getList()
		getDateil()
	})
</script>
