<!-- 问题看板 -->
<template>
    <div class="header">
        <div class="header_title">
            <div class="header_title_size">
                问题发生情况统计
            </div>
            <div class="header_date">
                <div class="pr-14px">年度</div>
                <el-date-picker v-model="queryParams.startYear" type="year" :clearable="false" placeholder="开始" class="!w-90px"
                    @change="handleQuery" value-format="YYYY" format="YYYY"/>
            </div>
        </div>
        <div class="flex items-center scroll-container pt-12px pr-20px pl-20px">
            <el-button class="arrow left-arrow" @click="scrollContent('left')" circle>
                <Icon icon="ep:arrow-left" />
            </el-button>
            <div class="flex items-center scroll-content">
                <div class="header_card header_width">
                    <div class="header_card_title">
                        问题总数{{topNumData.questionNum}}个
                    </div>
                    <el-row class="flex items-center pl-20px pt-10px">
                        <el-col :span="6" :xs="24">
                            <el-image style="width: 64px; height: 64px" :src="url1" fit="cover" />
                        </el-col>
                        <el-col :span="9" :xs="24">
                            <div class="card_number text-center">
                                {{topNumData.innerQuestionNum}}
                            </div>
                            <div class="card_text text-center">
                                内审发现
                            </div>
                        </el-col>
                        <el-col :span="9" :xs="24">
                            <div class="card_number text-center">
                                {{topNumData.outQuestionNum}}
                            </div>
                            <div class="card_text text-center">
                                外审发现
                            </div>
                        </el-col>
                    </el-row>
                </div>
                <div class="header_card ml-6px header_width2">
                    <div class="header_card_title">
                        多发共性问题2种
                    </div>
                    <el-row class="flex items-center pl-20px pt-10px">
                        <el-col :span="4" :xs="24">
                            <el-image style="width: 64px; height: 64px" :src="url2" fit="cover" />
                        </el-col>
                        <el-col :span="5" :xs="24" v-for="(item,index) in topNumData.shareList" :key="index">
                            <div class="card_number text-center">
                                {{item.num}}
                            </div>
                            <div class="card_text text-center">
                                {{item.name}}
                            </div>
                        </el-col>
                        <!-- <el-col :span="9" :xs="24">
                            <div class="card_number text-center">
                                20.00%
                            </div>
                            <div class="card_text text-center">
                                缺少必要建设审批文件
                            </div>
                        </el-col> -->
                    </el-row>
                </div>
                <div class="header_card ml-6px header_width2">
                    <div class="header_card_title">
                        业务领域多发问题
                    </div>
                    <el-row class="flex items-center pl-20px pt-10px">
                        <el-col :span="4" :xs="24">
                            <el-image style="width: 64px; height: 64px" :src="url3" fit="cover" />
                        </el-col>
                        <el-col :span="5" :xs="24" v-for="(item,index) in topNumData.domainList" :key="index">
                            <div class="card_number text-center">
                                {{item.num}}
                            </div>
                            <div class="card_text text-center">
                                {{item.name}}
                            </div>
                        </el-col>
                        <!-- <el-col :span="5" :xs="24">
                            <div class="card_number text-center">
                                10.00%
                            </div>
                            <div class="card_text text-center">
                                帐外物资
                            </div>
                        </el-col>
                        <el-col :span="5" :xs="24">
                            <div class="card_number text-center">
                                20.00%
                            </div>
                            <div class="card_text text-center">
                                利润真实性
                            </div>
                        </el-col>
                        <el-col :span="5" :xs="24">
                            <div class="card_number text-center">
                                5.00%
                            </div>
                            <div class="card_text text-center">
                                围串标
                            </div>
                        </el-col> -->
                    </el-row>
                </div>
                <div class="header_card2 ml-6px header_width2">
                    <div class="header_card_title">
                        问题事项统计
                    </div>
                    <el-row class="flex items-center pl-20px pt-10px">
                        <el-col :span="4" :xs="24">
                            <el-image style="width: 64px; height: 64px" :src="url4" fit="cover" />
                        </el-col>
                        <el-col :span="5" :xs="24" v-for="(item,index) in topNumData.eventList" :key="index">
                            <div class="card_number text-center">
                                {{item.num}}
                            </div>
                            <div class="card_text text-center">
                                {{item.name}}
                            </div>
                        </el-col>
                        <!-- <el-col :span="5" :xs="24">
                            <div class="card_number text-center">
                                20.00%
                            </div>
                            <div class="card_text text-center">
                                资产管理
                            </div>
                        </el-col>
                        <el-col :span="5" :xs="24">
                            <div class="card_number text-center">
                                15.00%
                            </div>
                            <div class="card_text text-center">
                                资金管理
                            </div>
                        </el-col>
                        <el-col :span="5" :xs="24">
                            <div class="card_number text-center">
                                20.00%
                            </div>
                            <div class="card_text text-center">
                                收入管理
                            </div>
                        </el-col> -->
                    </el-row>
                </div>
            </div>
            <el-button class="arrow right-arrow" @click="scrollContent('right')" circle>
                <Icon icon="ep:arrow-right" />
            </el-button>
        </div>
    </div>
    <el-row :gutter="8" class="pt-16px">
        <el-col :span="6" :xs="24">
            <ContentWrap>
                <div class="flex_title">
                    <div class="title-left"></div>
                    <div class="pl-8px">问题类型占比</div>
                </div>
                <div :class="twoChart" ref="twoChartRef" style="height:288px;width:100%;"></div>
            </ContentWrap>
        </el-col>
        <el-col :span="12" :xs="24">
            <ContentWrap>
                <div class="flex_title">
                    <div class="title-left"></div>
                    <div class="pl-8px">近五年问题发生趋势</div>
                </div>
                <div :class="threeChart" ref="threeChartRef" style="height:288px;width:100%;"></div>
            </ContentWrap>
        </el-col>
        <el-col :span="6" :xs="24">
            <ContentWrap>
                <div class="flex_title">
                    <div class="title-left"></div>
                    <div class="pl-8px">问题多发业务领域</div>
                </div>
                <div :class="fourChart" ref="fourChartRef" style="height:288px;width:100%;"></div>
            </ContentWrap>
        </el-col>
        <!-- <el-col :span="6" :xs="24">
            <ContentWrap>
                <div class="flex_title">
                    <div class="title-left"></div>
                    <div class="pl-8px">发现问题途径</div>
                </div>
                <div :class="oneChart" ref="oneChartRef" style="height:288px;width:100%;"></div>
            </ContentWrap>
        </el-col> -->
    </el-row>
    <ContentWrap>
        <div class="flex_title">
            <div class="title-left"></div>
            <div class="pl-8px">问题屡查屡犯</div>
        </div>
        <div :class="fiveChart" ref="fiveChartRef" style="height:288px;width:100%;"></div>
    </ContentWrap>
</template>

<script lang="ts" setup>
import * as echarts from 'echarts';
import demoviewIcon1 from '@/assets/imgs/decision/icon-wentizongshu.png';
import demoviewIcon2 from '@/assets/imgs/decision/icon-duofawenti.png';
import demoviewIcon3 from '@/assets/imgs/decision/icon-yewulingyu.png';
import demoviewIcon4 from '@/assets/imgs/decision/icon-wentishixiang.png';
import { colorList } from '../../index'
import { ProblemApi,TopNumVO } from '@/api/decision/problem'
defineOptions({
    name: 'QuestionBoard'
})
const queryParams = reactive({
    startYear: undefined as unknown as string,
})
const loading = ref(true) // 列表的加载中

const url1 = ref(demoviewIcon1)
const url2 = ref(demoviewIcon2)
const url3 = ref(demoviewIcon3)
const url4 = ref(demoviewIcon4)

const topNumData = ref({} as TopNumVO)
const getAllApi = async () => {
    topNumData.value = await ProblemApi.getTopNum(queryParams)
    const TypeNumData = await ProblemApi.getTypeNum(queryParams)
    const NearNumData = await ProblemApi.getNearNum(queryParams)
    const EventNumData = await ProblemApi.getEventNum(queryParams)
    const UnitNumData = await ProblemApi.getUnitNum(queryParams)
    console.log(EventNumData,NearNumData);
    // await Promise.all([getOneChart()])
    await Promise.all([getTwoChart(TypeNumData)])
    await Promise.all([getThreeChart(NearNumData)])
    await Promise.all([getFourChart(EventNumData)])
    await Promise.all([getFiveChart(UnitNumData)])
    loading.value = false
}

// 左右滑动
const scrollContent = (direction) => {
    const scrollContent = document.querySelector('.scroll-content');
    const scrollAmount = 150; // 每次滚动的距离

    if (direction === 'left') {
        scrollContent.scrollLeft -= scrollAmount;
    } else if (direction === 'right') {
        scrollContent.scrollLeft += scrollAmount;
    }
}
const color = ['#2A71C0', '#FF9C01', '#AC3C52', '#C96F37', '#95C2ED', '#E6E8F4', '#ABEDD8']
// 第一个echarts
// let oneChart: echarts.ECharts | null = null;
// const oneChartRef = ref()
// const getOneChart = async () => {
//     oneChart = echarts.init(oneChartRef.value)
//     const data = [
//         { value: 100, name: '内部审计' },
//         { value: 150, name: '外部审计' },
//         { value: 12, name: '其他' },
//     ]
//     oneChart.setOption({
//         color: color,
//         // tooltip: {
//         //     trigger: 'item',
//         // },
//         legend: {
//             top: 'bottom',
//             itemWidth: 14,
//             data: data
//         },
//         series: [
//             {
//                 name: '姓名',
//                 type: 'pie',
//                 radius: ['50%', '60%'],
//                 center: ['50%', '40%'],
//                 avoidLabelOverlap: false,
//                 padAngle: 5,
//                 itemStyle: {
//                     borderRadius: 10
//                 },
//                 label: {
//                     show: false,
//                     position: 'center'
//                 },
//                 emphasis: {
//                     label: {
//                         show: true,
//                         fontSize: 18,
//                         fontWeight: 500,
//                         formatter: function (params) {
//                             return '{total|' + params.value + '}' + '\n ' + params.name;
//                         },
//                         rich: {
//                             total: {
//                                 fontSize: 25,
//                                 color: '#000',
//                                 lineHeight: 40
//                             }
//                         }
//                     }
//                 },
//                 labelLine: {
//                     show: false
//                 },
//                 data: data
//             }
//         ]
//     })
// }

// 第二个echarts
let twoChart: echarts.ECharts | null = null;
const twoChartRef = ref()
const getTwoChart = async (val) => {
    twoChart = echarts.init(twoChartRef.value)
    twoChart.setOption({
        color: color,
        tooltip: {
            trigger: 'item',
        },
        legend: {
            top: 'bottom',
            itemWidth: 14,
            data: val
        },
        series: [
            {
                name: '姓名',
                type: 'pie',
                radius: [20, 100],
                center: ['50%', '40%'],
                roseType: 'area',
                data: val,
                itemStyle: {
                    borderRadius: 2,
                    normal: {
                        color: function (params) {
                            return colorList[params.dataIndex];
                        },
                    },
                },
                label: {
                    show: false
                },
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }
        ]
    })
}

// 第三个echarts
let threeChart: echarts.ECharts | null = null;
const threeChartRef = ref()
const getThreeChart = async (val) => {
    threeChart = echarts.init(threeChartRef.value)
    const dataList = val.nums.map((item) => item.mattersName)
    // 动态生成 series 数组
const generateSeries = (data, total) => {
    return data.map(item => ({
        name: item.mattersName,
        type: 'bar',
        data: item.count,
        barMaxWidth: 40,
        itemStyle: {
            borderRadius: [10, 10, 0, 0]
        }
    })).concat([{
        name: '问题总数',
        type: 'line',
        yAxisIndex: 1,
        data: total,
    }]);
};

// 使用函数生成 series
const series = generateSeries(val.nums, val.total);
    threeChart.setOption({
        color: color,
        grid: {
            top: "10%"
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross',
                crossStyle: {
                    color: '#999'
                }
            }
        },
        legend: {
            data: dataList,
            itemWidth: 14,
            top: 'bottom',
        },
        xAxis: [
            {
                type: 'category',
                data: val.years,
                axisPointer: {
                    type: 'shadow'
                },
                axisLabel: {
                    interval: 0,
                }
            }
        ],
        yAxis: [
            {
                type: 'value',
            },
            {
                type: 'value',
                splitLine: {
					show: false,
				}
            }
        ],
        series: series 
        // [
        //     {
        //         name: '财务管理',
        //         type: 'bar',
        //         data: [87, 67, 46, 35, 20],
        //         barMaxWidth: 40,
        //         itemStyle: {
        //             borderRadius: [10, 10, 0, 0]
        //         }
        //     },
        //     {
        //         name: '三重一大决策',
        //         type: 'bar',
        //         data: [61, 80, 32, 74, 77],
        //         barMaxWidth: 40,
        //         itemStyle: {
        //             borderRadius: [10, 10, 0, 0]
        //         }
        //     },
        //     {
        //         name: '合同管理',
        //         type: 'bar',
        //         data: [72, 29, 44, 53, 79],
        //         barMaxWidth: 40,
        //         itemStyle: {
        //             borderRadius: [10, 10, 0, 0]
        //         }
        //     },
        //     {
        //         name: '采购管理',
        //         type: 'bar',
        //         data: [47, 62, 37, 24, 57],
        //         barMaxWidth: 40,
        //         itemStyle: {
        //             borderRadius: [10, 10, 0, 0]
        //         }
        //     },
        //     {
        //         name: '招投标管理',
        //         type: 'bar',
        //         data: [60, 45, 59, 55, 82],
        //         barMaxWidth: 40,
        //         itemStyle: {
        //             borderRadius: [10, 10, 0, 0]
        //         }
        //     },
        //     {
        //         name: '问题总数',
        //         type: 'line',
        //         yAxisIndex: 1,
        //         data: val.total,
        //     }
        // ]
    })
}

// 第四个echarts
let fourChart: echarts.ECharts | null = null;
const fourChartRef = ref()
const getFourChart = async (val) => {
    fourChart = echarts.init(fourChartRef.value)
    // const data = [
    //     { value: 40, name: '存贷双高' },
    //     { value: 32, name: '账外物资' },
    //     { value: 28, name: '利润真实性' },
    //     { value: 25, name: '围串标' },
    // ]
    fourChart.setOption({
        color: color,
        tooltip: {
            trigger: 'item',
        },
        legend: {
            top: 'bottom',
            itemWidth: 14,
            data: val
        },
        series: [
            {
                name: '姓名',
                type: 'pie',
                radius: '60%',
                center: ['50%', '40%'],
                data: val,
                label: {
                    show: false
                },
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                },
                selectedMode: "single",
            }
        ]
    })
}

// 第五个echarts
let fiveChart: echarts.ECharts | null = null;
const fiveChartRef = ref()
const getFiveChart = async (val) => {
    fiveChart = echarts.init(fiveChartRef.value)
	const dataList1 = val.map((item) => item.unitName)
	const dataList2 = val.map((item) => item.total)
    const dataList3 = val.map((item) => item.recurQuesNum)
    const dataList4 = val.map((item) => item.recurRate)
    fiveChart.setOption({
        color: color,
        grid: {
            top: "10%",
            right: 50,
            left: 50,
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross',
                crossStyle: {
                    color: '#999'
                }
            }
        },
        legend: {
            data: ['问题总数', '屡查屡犯问题', '复发率'],
            itemWidth: 14,
            top: 'bottom',
        },
        xAxis: [
            {
                type: 'category',
                data: dataList1,
                axisPointer: {
                    type: 'shadow'
                },
                axisLabel: {
                    interval: 0,
                    formatter: function (value) {
                        // 返回简短版本的标签文本
                        return value.substring(0, 5) + '...'; // 截取前8个字符并添加省略号
                    }
                }
            }
        ],
        yAxis: [
            {
                type: 'value',
            },
            {
                type: 'value',
                splitLine: {
					show: false,
				}
            }
        ],
        series: [
            {
                name: '问题总数',
                type: 'bar',
                data: dataList2,
                barMaxWidth: 40,
                itemStyle: {
                    borderRadius: [10, 10, 0, 0],
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 1, color: '#BBC8DE' },
                        { offset: 0, color: '#07397E' }
                    ])
                }
            },
            {
                name: '屡查屡犯问题',
                type: 'bar',
                data: dataList3,
                barMaxWidth: 40,
                itemStyle: {
                    borderRadius: [10, 10, 0, 0],
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 1, color: '#75ACE3' },
                        { offset: 0, color: '#82ADD7' }
                    ])
                }
            },
            {
                name: '复发率',
                type: 'line',
                yAxisIndex: 1,
                data: dataList4,
            }
        ]
    })
}

/** 搜索按钮操作 */
const handleQuery = () => {
    console.log('000')
    getAllApi()
}

/** 初始化 **/
onMounted(() => {
    queryParams.startYear =  new Date().getFullYear().toString();
    getAllApi()
})
</script>

<style scoped>
/* 去除输入框边框 */
::v-deep .el-date-editor {
	--el-input-border-color: transparent !important;
	--el-input-bg-color: transparent;
    --el-input-hover-border-color: #fff;
}
.flex_title {
    display: flex;
    align-items: center;
    line-height: 33px;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 700;
    font-size: 16px;
    color: #333333;
    border-bottom: 1px solid #EEEFF0;
}

.title-left {
    width: 5px;
    height: 15px;
    background: #2F4CAD;
    border-radius: 0px 0px 0px 0px;
}

.header {
    height: 199px;
    border: 2px solid rgba(255, 255, 255, 1);
    background: #F7F7F7;
	border-radius: 8px;
}

.header_title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 20px 0;
}

.header_title_size {
    height: 26px;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 500;
    font-size: 18px;
    color: #333333;
    line-height: 26px;
}

.header_date {
    width: 144px;
    height: 34px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 8px 8px 8px 8px;
    border: 2px solid #FFFFFF;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    font-size: 15px;
    color: #999;
    line-height: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.scroll-container {
    /* width: 97%; */
    overflow: hidden;
    position: relative;
}

.arrow {
    min-width: auto !important;
    width: 45px;
    height: 45px;
    cursor: pointer;
    /* position: absolute; */
    z-index: 1;
    opacity: 0.7;
}

.right-arrow {
    right: 20px;
}

.scroll-content {
    scroll-snap-type: x mandatory;
    scroll-behavior: smooth;
    overflow-x: scroll;
    overflow: hidden;
    white-space: nowrap;
    width: 100%;
}

.header_card {
    flex-shrink: 0;
    height: 125px;
    background: linear-gradient(160.24deg, #A0BDF0 13.03%, #07397E 86.79%);
    border-radius: 8px 8px 8px 8px;
    border: 2px solid #FFFFFF;
}

.header_card2 {
    flex-shrink: 0;
    height: 125px;
    background: linear-gradient(160.24deg, #FCC535 13.03%, #FF7F00 86.79%);
    border-radius: 8px 8px 8px 8px;
    border: 2px solid #FFFFFF;
}
.header_width{
    min-width: 384px;
    flex: 1;
}
.header_width1{
    min-width: 504px;
    flex: 2;
}
.header_width2{
    min-width: 624px;
    flex: 3;
}

.header_card_title {
    height: 23px;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    font-size: 16px;
    color: #fff;
    line-height: 23px;
    padding-top: 14px;
    padding-left: 20px;
}

.header_card_item {
    flex: 1;
}

.card_number {
    height: 41px;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 700;
    font-size: 26px;
    color: #fff;
    line-height: 41px;
}

.card_text {
    height: 22px;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    font-size: 15px;
    color: #fff;
    line-height: 22px;
}
</style>