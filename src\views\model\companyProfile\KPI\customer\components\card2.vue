<!--
* @Author: wangk
* @Date: 2024-10-26 17:10:50
* @Description: 领导班子业绩考核=>
-->
<template>
  <div class="flex flex-col">
   <div class="flex w-100% flex-row">
     <ContentWrap class="w-50%" :title="'应收款余额构成'" :shut="true">
        <div>
          <div :class="chartLeft" ref="chartLeftRef" style="height: 250px; width: 100%"></div>
        </div>
     </ContentWrap>
     <ContentWrap class="w-50% ml-15px" :title="'应收款余额对比'" :shut="true">
             <div>
               <div
                 :class="bottomChart"
                 ref="bottomChartRef"
                 style="height: 250px; width: 100%"
               ></div>
             </div>
     </ContentWrap>
   </div>
   <ContentWrap :title="'应收款余额'" :shut="true">
   <!-- 应收款余额表格 -->
   <el-table
     v-loading="loading"
     :data="list"
     :stripe="true"
     :show-overflow-tooltip="true"
   >
     <el-table-column label="序号" width="60" align="center">
       <template #default="{ $index }">{{
         (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1
       }}</template>
     </el-table-column>
     <el-table-column label="指标名称" align="center" prop="questionType" />
     <el-table-column label="上档基础分" align="center" prop="questionTitle" />
     <el-table-column label="本档基础分" align="center" prop="questionTitle" />
     <el-table-column label="调整分" align="center" prop="questionTitle" />
     <el-table-column label="指标得分" align="center" prop="questionTitle" />
   </el-table>
   <!-- 分页 -->
   <Pagination
     :total="total"
     v-model:page="queryParams.pageNo"
     v-model:limit="queryParams.pageSize"
     @pagination="props.getList()"
   />
   </ContentWrap>
  </div>
</template>

<script setup lang="ts">
import { RegulationsVO } from '@/api/jobmanger/regulations/outregulations'
// import { reformingStandingBookApi } from '@/api/auditManagement/auditRectifica/reformingStandingBook'
import { handleLazyTree } from '@/utils/tree'
import * as DeptApi from '@/api/system/dept'
import * as echarts from 'echarts'
import { propTypes } from '@/utils/propTypes'
// import { formatTime } from '@/utils'
defineOptions({ name: 'Card2' })

const loading = ref(false) // 列表的加载中
const list = ref() // 列表的数据
const queryParams = reactive({
  pageSize: 10,
  pageNo: 1
})
const total = ref(0)
// 定义属性
const props = defineProps({
  tableData: propTypes.object.def({}),
  getList: {
    type: Function,
    default: ()=>{}
  }
})
list.value=props.tableData
// 获取单位
interface DeptNode {
  id: number
  masterOrgId?: number | string
  name: string
  parentId: number | string
  children?: DeptNode[]
  isLeaf?: boolean
}
const deptList = ref<DeptNode[]>([]) // 树形结构
const defaultExpandedKeys = ref<(string | number)[]>([])
const getTree = async (id) => {
  deptList.value = []
  const res = await DeptApi.getSimpleDeptList(id)
  deptList.value.push(...handleLazyTree(res, 'id', 'parentId'))
  defaultExpandedKeys.value = deptList.value.map((node) => node.id)
}
// 左边图表
let chartLeft = null
const chartLeftRef = ref<InstanceType<typeof ElTree>>()
const getChartLeft = async () => {
  chartLeft = echarts.init(chartLeftRef.value)
  chartLeft.setOption({
  title: {
    text: 'Weather Statistics',
    subtext: 'Fake Data',
    left: 'center'
  },
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b} : {c} ({d}%)'
  },
  legend: {
    bottom: 10,
    left: 'center',
    data: ['CityA', 'CityB', 'CityD', 'CityC', 'CityE']
  },
  series: [
    {
      type: 'pie',
      radius: '65%',
      center: ['50%', '50%'],
      selectedMode: 'single',
      data: [
        {
          value: 1548,
          name: 'CityE',
          label: {
            // formatter: [
            //   '{title|{b}}{abg|}',
            //   '  {weatherHead|Weather}{valueHead|Days}{rateHead|Percent}',
            //   '{hr|}',
            //   '  {Sunny|}{value|202}{rate|55.3%}',
            //   '  {Cloudy|}{value|142}{rate|38.9%}',
            //   '  {Showers|}{value|21}{rate|5.8%}'
            // ].join('\n'),
            // backgroundColor: '#eee',
            // borderColor: '#777',
            // borderWidth: 1,
            // borderRadius: 4,
            // rich: {
            //   title: {
            //     color: '#eee',
            //     align: 'center'
            //   },
            //   abg: {
            //     backgroundColor: '#333',
            //     width: '100%',
            //     align: 'right',
            //     height: 25,
            //     borderRadius: [4, 4, 0, 0]
            //   },
            //   Sunny: {
            //     height: 30,
            //     align: 'left',
            //     backgroundColor: {
            //       image: weatherIcons.Sunny
            //     }
            //   },
            //   Cloudy: {
            //     height: 30,
            //     align: 'left',
            //     backgroundColor: {
            //       image: weatherIcons.Cloudy
            //     }
            //   },
            //   Showers: {
            //     height: 30,
            //     align: 'left',
            //     backgroundColor: {
            //       image: weatherIcons.Showers
            //     }
            //   },
            //   weatherHead: {
            //     color: '#333',
            //     height: 24,
            //     align: 'left'
            //   },
            //   hr: {
            //     borderColor: '#777',
            //     width: '100%',
            //     borderWidth: 0.5,
            //     height: 0
            //   },
            //   value: {
            //     width: 20,
            //     padding: [0, 20, 0, 30],
            //     align: 'left'
            //   },
            //   valueHead: {
            //     color: '#333',
            //     width: 20,
            //     padding: [0, 20, 0, 30],
            //     align: 'center'
            //   },
            //   rate: {
            //     width: 40,
            //     align: 'right',
            //     padding: [0, 10, 0, 0]
            //   },
            //   rateHead: {
            //     color: '#333',
            //     width: 40,
            //     align: 'center',
            //     padding: [0, 10, 0, 0]
            //   }
            // }
          }
        },
        { value: 735, name: 'CityC' },
        { value: 510, name: 'CityD' },
        { value: 434, name: 'CityB' },
        { value: 335, name: 'CityA' }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }
  ]
})
}
// 右边图表
let bottomChart = null
const bottomChartRef = ref<InstanceType<typeof ElTree>>()
const getBottomChart = async () => {
  bottomChart = echarts.init(bottomChartRef.value)
  bottomChart.setOption({
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      data: ['合同', '采购', '财务', '工程', '其他']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        type: 'bar',
        barWidth: '45%',
        stack: 'total',
        label: {
          show: true
        },
        itemStyle: {
          normal: {
            barBorderRadius: [8, 8, 0, 0],
            color: '#2A8AF7'
          },
          color: '#2A8AF7'
        },
        data: [11, 12, 13, 14, 11]
      }
    ]
  })
}


/** 初始化 **/
onMounted(async () => {
  list.value=props.tableData
  total.value=list.value.length
  console.log(list,'11111')
  await getChartLeft()
  await getBottomChart()
})
</script>
