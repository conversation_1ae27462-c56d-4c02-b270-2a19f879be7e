
<template>
  <el-row :gutter="16">
    <el-col :span="5" :xs="24">
      <ContentWrap class="h-1/1">
        <JobmangerLeftTree @node-click="handleDeptNodeClick" :createUrl="createUrl" :editUrl="editUrl"
					:searchUrl="searchUrl" :delUrl="delUrl" :detailsUrl="detailsUrl" :type='6' :showBut='true'/>
      </ContentWrap>
    </el-col>
    <el-col :span="19" :xs="24">
      <ContentWrap class="common-card-search">
        <!-- 搜索工作栏 -->
        <el-form
          class="-mb-15px common-search-form"
          :model="queryParams"
          ref="queryFormRef"
          :inline="true"
          label-width="110px"
        >
          <el-form-item label="资料名称" prop="materialName">
            <el-input
              v-model="queryParams.materialName"
              placeholder="请输入资料名称"
              clearable
              @keyup.enter="handleQuery"
              class="!w-200px"
            />
          </el-form-item>
          <el-form-item label="分类名称" prop="typeName">
            <el-input
              v-model="queryParams.typeName"
              placeholder="请输入分类名称"
              clearable
              @keyup.enter="handleQuery"
              class="!w-200px"
            />
          </el-form-item>
          <el-form-item label="是否有模版" prop="templateFlag">
            <el-select
              v-model="queryParams.templateFlag"
              placeholder="请选择是否有模版"
              clearable
              class="!w-200px"
            >
              <el-option
                v-for="dict in getIntDictOptions('audit_data_list_template')"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
                @change="handleQuery"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="资料说明" prop="materialDesc">
            <el-input
              v-model="queryParams.materialDesc"
              placeholder="请输入资料说明"
              clearable
              @keyup.enter="handleQuery"
              class="!w-200px"
            />
          </el-form-item>
        </el-form>
		<div class="right-search-btn">
			<el-button  type="primary"  @click="handleQuery">
					<Icon icon="ep:search" class="mr-5px" />搜索
				</el-button>
				<el-button @click="resetQuery">
					<Icon icon="ep:refresh" class="mr-5px" />重置
				</el-button>
		</div>
      </ContentWrap>
      <!-- 列表 -->
      <ContentWrap>
        <div class="button_margin15">
            <el-button
              v-hasPermi="['audit:tank-matters-material:create']"
              type="primary"
              plain
              @click="openForm('create', '', queryParams.typeId)"
            >
              <Icon icon="ep:plus" class="mr-5px" />新增资料清单
            </el-button>
            <el-button
              v-hasPermi="['audit:tank-matters-material:export']"
              :loading="exportLoading"
              plain
              @click="handleExport"
            >
              <Icon class="mr-5px" icon="ep:download" />导出
            </el-button>
          </div>
        <el-table v-loading="loading" :data="list" border :stripe="true" :show-overflow-tooltip="true">
          <el-table-column label="序号" width="60" align="center">
            <template
              #default="{ $index }"
            >{{ (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1 }}</template>
          </el-table-column>
          <el-table-column label="资料名称" align="left" prop="materialName" min-width="180" />
          <el-table-column label="分类名称" align="center" prop="typeName" min-width="150"/>
          <el-table-column label="是否有模版" key="templateFlag" align="center" min-width="100">
            <template #default="{row}">
              <dict-tag :type="'audit_data_list_template'" :value="row.templateFlag" />
            </template>
          </el-table-column>
          <el-table-column label="资料说明" align="left" prop="materialDesc" min-width="180">
            <template #default="{row}">
              <div v-html="row.materialDesc"></div>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" :width='160' fixed="right">
            <template #default="{row}">
              <el-button v-hasPermi="['audit:tank-trees-type:get']" link type="primary" @click="openDetailForm(row?.id)">查看</el-button>
              <el-button
                link
                type="primary"
                @click="openForm('update',row.id)"
                v-hasPermi="['audit:tank-trees-type:update']"
              >编辑</el-button>
              <el-button
                link
                type="danger"
                @click="handleDelete(row.id)"
                v-hasPermi="['audit:tank-matters-material:delete']"
              >删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页 -->
        <Pagination
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </ContentWrap>
    </el-col>
  </el-row>

  <!-- 表单弹窗：添加/修改 -->
  <Form ref="formRef" @success="getList" />
  <!-- 查看详情弹窗 -->
  <Detail ref="detailRef" />
</template>

<script setup lang="ts">
import { getIntDictOptions, getStrDictOptions, DICT_TYPE, NumberDictDataType } from '@/utils/dict'

import { handleTree } from '@/utils/tree'
import download from '@/utils/download'
import { CheckListApi } from '@/api/jobmanger/checklist'
import { AuditRoleApi, AuditRoleVO, AuditRoleDetailVO } from '@/api/basicData/auditRole'
import Form from './form.vue'
import { formatTime } from '@/utils'
import Detail from './Detail.vue'
import JobmangerLeftTree from '@/views/jobmanger/itemLibrary/components/JobmangerLeftTree.vue'
import { TimeSplicing } from '@/utils/formatTime'
defineOptions({ name: 'Checklist‌‌' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref([]) // 列表的数据
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  typeId: undefined,
  materialName: undefined,
  typeName: undefined,
  templateFlag: undefined,
  materialDesc: undefined
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const total = ref(0)

const searchUrl = ref('/audit/tank-trees-type/get?type=6')
const createUrl = '/audit/tank-trees-type/create'
const editUrl = '/audit/tank-trees-type/update'
const delUrl = '/audit/tank-trees-type/delete'
const detailsUrl = '/audit/tank-trees-type/get-tree'

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await CheckListApi.getCheckList(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

const detailRef = ref()
const openDetailForm = (id:number) => {
  detailRef.value.open(id)
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 处理部门被点击 */
const handleDeptNodeClick = async (row) => {
  queryParams.typeId = row.id
  await getList()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number, typeId: any) => {
  formRef.value.open(type, id, typeId)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await CheckListApi.deleteCheckListById(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await CheckListApi.exportCheckList(queryParams)
    const time = TimeSplicing(new Date())
    download.excel(data, `审计资料清单${time}.xls`)
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
