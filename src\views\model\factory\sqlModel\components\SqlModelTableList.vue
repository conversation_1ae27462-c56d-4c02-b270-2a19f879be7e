<script setup lang="ts">

import {ref, onMounted} from "vue";
import {dateFormatter} from "@/utils/formatTime";
import {ElButton} from "element-plus";
import * as modelApi from '@/api/model/info';
import {getSqlModelInfoPage} from "@/api/model/info";

const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const tableLoading = ref(true)
const tableList = ref([])
const total = ref(0) // 列表的总页数
const maxHeight = ref<string>('');

const calculateMaxHeight = () => {
  const windowHeight = window.innerHeight;
  const dialogHeaderHeight = 40; // 假设对话框头部高度为 40px
  const dialogFooterHeight = 40; // 假设对话框底部高度为 40px
  maxHeight.value = `${windowHeight * 0.5 - dialogHeaderHeight - dialogFooterHeight}px`;
};

onMounted(() => {
  calculateMaxHeight();
});


const open = async () => {
  dialogVisible.value = true
  dialogTitle.value = 'SQL模型'
  getSqlModelList()
}
defineExpose({open}) // 提供 open 方法，用于打开弹窗


const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  modelName: null,
})

const handleQuery = () => {
  queryParams.pageNo = 1;
  getSqlModelList();
}


const queryFormRef = ref() // 查询表单
const resetQuery = () => {
  queryParams.modelName = ''
  handleQuery()
}
const getSqlModelList = async () => {
  try {
    tableLoading.value = true;
    const res = await getSqlModelInfoPage(queryParams);
    tableList.value = res.list;
    total.value = res.total;
  } finally {
    tableLoading.value = false;
  }
}


const selectedRadio = ref('') // 用于存储选中的 radio 的 label
const selectRow = ref({})
const handleRadioChange = (index, row) => {
  selectedRadio.value = index; // 更新选中的 radio
  // 你可以在这里处理选中的行数据，例如：
  selectRow.value = row
}


const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const quoteSql = async () => {
  const sql = selectRow.value.executeSql
  const res = await modelApi.saveQuoteRecord(selectRow.value.id)
  if (res) {
    emit('success', sql)
    selectedRadio.value = ''
    dialogVisible.value = false
  }

}


const selectedRows = ref<any[]>([]); // 保存选中的行数据
const selectedRow = ref<any>({}); // 保存选中的行数据
const handleSelectionChange = (rows: any[]) => {
  if (rows.length > 0) {
    // 只保留最新选中的行
    selectedRow.value = rows[0];
    // 清除其他行的选择状态
    tableRef.clearSelection();
    tableRef.toggleRowSelection(rows[0], true);
  } else {
    selectedRow.value = null;
  }
};

const isSelectable = (row: any, index: number) => {
  // 自定义选择逻辑，如果需要
  return true; // 默认允许所有行可选
};

let tableRef: any = null; // 用于保存 el-table 的引用
const clearSelection = () => {
  if (tableRef) {
    tableRef.clearSelection();
  }
};

const cancel = () => {
  dialogVisible.value = false;
  clearSelection(); // 取消时清除选择状态
};

const confirm = () => {
  // 执行确认逻辑
  dialogVisible.value = false;
  clearSelection(); // 确认后清除选择状态
};

</script>

<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" :max-height="maxHeight" width="60%"
             append-to-body :wrapper-closable="false">
    <div class="form-container">
      <el-form
        :model="queryParams"
        class="form-inline"
        ref="queryFormRef"
        :inline="true"
        label-width="68px"
      >
        <el-form-item label="模型名称" prop="modelName">
          <el-input
            v-model="queryParams.modelName"
            placeholder="请输入模型名称"
            clearable
            class="!w-160px"
          />
        </el-form-item>
      </el-form>
      <div class="form-buttons"
           style="display: flex; justify-content: flex-end; margin-top: -17px;">
        <!-- 调整 margin-top 以适应你的布局 -->
        <el-button type="primary" @click="handleQuery">
          <Icon icon="ep:search" class="mr-5px"/>
          查询
        </el-button>
        <el-button @click="resetQuery">
          <Icon icon="ep:refresh" class="mr-5px"/>
          重置
        </el-button>
        <el-button :disabled="selectedRadio === ''" @click="quoteSql">
          引用
        </el-button>
      </div>
    </div>

    <div style="height: 100%; display: flex; flex-direction: column;">
      <el-table border v-loading="tableLoading" :data="tableList" :stripe="true" ref="tableRef"
                :show-overflow-tooltip="true" style="flex: 1;"
                @selection-change="handleSelectionChange">
        <el-table-column
          label="序号"
          align="center"
          width="60"
        >
          <template #default="scope">
            <el-radio
              v-model="selectedRadio"
              :value="scope.$index"
              @change="handleRadioChange(scope.$index, scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column label="序号" width="60">
          <template #default="{ $index }">
            {{ (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column label="模型名称" align="center" prop="modelName"/>
        <el-table-column label="SQL语句" align="center" prop="executeSql"/>
        <el-table-column label="创建人" align="center" prop="creator"/>
        <el-table-column label="引用次数" align="center" prop="quoteNumber"/>
        <el-table-column label="创建时间" align="center" prop="createTime"
                         :formatter="dateFormatter"/>
      </el-table>
      <div style="display: flex; justify-content: flex-end; margin-top: 10px;">
        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="getSqlModelList"

        />
      </div>
    </div>
  </Dialog>
</template>

<style scoped lang="scss">
.form-container {
  display: flex;
  align-items: center; /* 垂直居中 */
}

.form-inline {
  flex: 1; /* 让表单占据剩余空间 */
  display: flex;
  flex-direction: column; /* 默认情况下，我们不需要这个，因为 inline 应该已经处理了 */
  /* 但由于我们可能想要确保内联布局，这里可以不做改变或根据需要调整 */
  /* 注意：如果 inline 属性正常工作，下面的样式可能不是必需的 */
  /* 你可能需要根据实际情况调整或移除这些样式 */
  margin-right: 10px; /* 给按钮组留出一些空间 */
}

.form-buttons {
  /* 使用 Flexbox 将按钮组对齐到右侧 */
  display: flex;
  justify-content: flex-end;
  /* 根据需要调整 margin-top 以确保按钮与表单项之间的间距合适 */
  margin-top: -10px; /* 这个值可能需要根据你的实际布局进行调整 */
}
</style>
