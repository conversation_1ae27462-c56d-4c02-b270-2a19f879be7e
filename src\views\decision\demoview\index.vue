<!-- 全景视图 -->
<template>
	<div class="header_tabs mb-16px">
		<el-tabs v-model="activeName" class="demo-tabs" @tab-change="handleClick">
			<el-tab-pane v-for="item in editableTabs" :key="item.name" :label="item.title" :name="item.name" />
		</el-tabs>
	</div>
	<div v-if="activeName == '0'">
		<div class="flex_title">
			<div class="title-left"></div>
			<div class="pl-8px">审计信息</div>
		</div>
		<div class="header mt-12px">
			<div class="header_title">
				<div class="header_title_size">
					审计信息全景
				</div>
				<div class="header_date">
					<div class="pr-5px pl-5px">时间段</div>
					<el-date-picker v-model="queryParams.data" type="yearrange" range-separator="至"
						start-placeholder="开始" end-placeholder="结束" value-format="YYYY" @change="handleQuery"
						class="!w-185px" />
				</div>
			</div>
			<el-row class="pt-6px pr-20px pl-20px">
				<el-col :span="6" :xs="24">
					<div class="header_card1">
						<div class="header_card_title">
							项目信息
						</div>
						<el-row class="flex items-center pl-18px pt-6px">
							<el-col :span="8" :xs="24">
								<el-image style="width: 72px; height: 72px" :src="url1" fit="cover" />
							</el-col>
							<el-col :span="8" :xs="24">
								<div class="header_card_number text-center">
									{{ dataList.TopNumList.projectNum }}
								</div>
								<div class="header_card_text text-center">
									项目总数
								</div>
							</el-col>
							<el-col :span="8" :xs="24">
								<div class="header_card_number text-center">
									{{ dataList.TopNumList.companyNum }}
								</div>
								<div class="header_card_text text-center">
									涉及公司
								</div>
							</el-col>
						</el-row>
					</div>
				</el-col>
				<el-col :span="12" :xs="24">
					<div class="header_card2">
						<div class="header_card_title">
							审计信息
						</div>
						<div class="flex items-center pl-18px pt-6px">
							<div class="header_card_item">
								<el-image style="width: 72px; height: 72px" :src="url2" fit="cover" />
							</div>
							<div class="header_card_item">
								<div class="header_card_number text-center">
									{{ dataList.TopNumList.questionNum }}
								</div>
								<div class="header_card_text text-center">
									审计问题
								</div>
							</div>
							<div class="header_card_item">
								<div class="header_card_number text-center">
									{{ dataList.TopNumList.rectificationFinishNum }}
								</div>
								<div class="header_card_text text-center">
									整改完成
								</div>
							</div>
							<div class="header_card_item">
								<div class="header_card_number text-center">
									{{ dataList.TopNumList.baseBookNum }}
								</div>
								<div class="header_card_text text-center">
									出具底稿
								</div>
							</div>
							<div class="header_card_item">
								<div class="header_card_number text-center">
									{{ dataList.TopNumList.auditBookNum }}
								</div>
								<div class="header_card_text text-center">
									完成报告
								</div>
							</div>
							<div class="header_card_item">
								<div class="header_card_number text-center">
									{{ dataList.TopNumList.projectFinishNum }}
								</div>
								<div class="header_card_text text-center">
									归档档案
								</div>
							</div>
							<div class="header_card_item">
								<div class="header_card_number text-center">
									{{ dataList.TopNumList.caseNum }}
								</div>
								<div class="header_card_text text-center">
									审计案例
								</div>
							</div>
						</div>
					</div>
				</el-col>
				<el-col :span="6" :xs="24">
					<div class="header_card3">
						<div class="header_card_title">
							其他信息
						</div>
						<el-row class="flex items-center pl-18px pt-6px">
							<el-col :span="8" :xs="24">
								<el-image style="width: 72px; height: 72px" :src="url3" fit="cover" />
							</el-col>
							<el-col :span="8" :xs="24">
								<div class="header_card_number text-center">
									{{ dataList.TopNumList.modelNum }}
								</div>
								<div class="header_card_text text-center">
									模型数据
								</div>
							</el-col>
							<el-col :span="8" :xs="24">
								<div class="header_card_number text-center">
									{{ dataList.TopNumList.domainNum }}
								</div>
								<div class="header_card_text text-center">
									领域数据
								</div>
							</el-col>
						</el-row>
					</div>
				</el-col>
			</el-row>
		</div>
		<div class="flex_title mt-16px">
			<div class="title-left"></div>
			<div class="pl-8px">审计数据总览</div>
		</div>
		<div class="main_card mt-12px">
			<div class="pj_card1">
				<div class="main_card_title pt-16px pl-22px">
					<div class="main_card_title_left main_card_title_left_background"></div>
					<div class="pl-8px">审计项目</div>
				</div>
				<div class="flex items-center pl-9px pr-9px pt-20px">
					<div class="header_card_item">
						<div class="card_number text-center">
							{{ dataList.BottomNumList.projectNum }}
						</div>
						<div class="card_text text-center pb-10px">
							项目总数
						</div>
						<div class="card_border_bottom"></div>
					</div>
					<div class="card_border_right"></div>
					<div class="header_card_item">
						<div class="card_number text-center">
							{{ dataList.BottomNumList.projectFinishNum }}
						</div>
						<div class="card_text text-center pb-10px">
							结项总数
						</div>
						<div class="card_border_bottom"></div>
					</div>
				</div>
				<div class="flex items-center pl-9px pr-9px pt-10px">
					<div class="header_card_item">
						<div class="card_number text-center">
							{{ dataList.BottomNumList.planProjectChildNum }}
						</div>
						<div class="card_text text-center">
							本年计划项目
						</div>
					</div>
					<div class="card_border_right"></div>
					<div class="header_card_item">
						<div class="card_number text-center">
							{{ dataList.BottomNumList.planProjectParentNum }}
						</div>
						<div class="card_text text-center">
							本年启动项目
						</div>
					</div>
				</div>
			</div>
			<div class="pj_card2">
				<div class="main_card_title pt-16px pl-22px">
					<div class="main_card_title_left main_card_title_left_background1"></div>
					<div class="pl-8px">风险地图</div>
				</div>
				<div class="flex items-center pl-9px pr-9px pt-20px">
					<div class="header_card_item">
						<div class="card_number text-center">
							{{ dataList.BottomNumList.domainNum }}
						</div>
						<div class="card_text text-center pb-10px">
							领域分布
						</div>
						<div class="card_border_bottom"></div>
					</div>
					<div class="card_border_right"></div>
					<div class="header_card_item">
						<div class="card_number text-center">
							{{ dataList.BottomNumList.modelNum }}
						</div>
						<div class="card_text text-center pb-10px">
							模型数量
						</div>
						<div class="card_border_bottom"></div>
					</div>
				</div>
				<div class="flex items-center pl-9px pr-9px pt-10px">
					<div class="header_card_item">
						<div class="card_number text-center">
							{{ dataList.BottomNumList.unitNum }}
						</div>
						<div class="card_text text-center">
							企业画像
						</div>
					</div>
					<div class="card_border_right"></div>
					<div class="header_card_item">
						<div class="card_number text-center">
							{{ dataList.BottomNumList.doubtfulNum }}
						</div>
						<div class="card_text text-center">
							疑点数量
						</div>
					</div>
				</div>
			</div>
			<div class="pj_card3">
				<div class="main_card_title pt-16px pl-22px">
					<div class="main_card_title_left main_card_title_left_background2"></div>
					<div class="pl-8px">审计成果</div>
				</div>
				<div class="flex items-center pl-9px pr-9px pt-20px">
					<div class="header_card_item">
						<div class="card_number text-center">
							{{ dataList.BottomNumList.baseBookNum }}
						</div>
						<div class="card_text text-center pb-10px">
							审计底稿
						</div>
						<div class="card_border_bottom"></div>
					</div>
					<div class="card_border_right"></div>
					<div class="header_card_item">
						<div class="card_number text-center">
							{{ dataList.BottomNumList.auditBookNum }}
						</div>
						<div class="card_text text-center pb-10px">
							审计报告
						</div>
						<div class="card_border_bottom"></div>
					</div>
				</div>
				<div class="flex items-center pl-9px pr-9px pt-10px">
					<div class="header_card_item">
						<div class="card_number text-center">
							{{ dataList.BottomNumList.baseBookNum }}
						</div>
						<div class="card_text text-center">
							审计方案
						</div>
					</div>
					<div class="card_border_right"></div>
					<div class="header_card_item">
						<div class="card_number text-center">
							{{ dataList.BottomNumList.betterCaseNum }}
						</div>
						<div class="card_text text-center">
							优秀案例
						</div>
					</div>
				</div>
			</div>
			<div class="pj_card4">
				<div class="main_card_title pt-16px pl-22px">
					<div class="main_card_title_left main_card_title_left_background3"></div>
					<div class="pl-8px">问题整改</div>
				</div>
				<div class="flex items-center pl-9px pr-9px pt-20px">
					<div class="header_card_item">
						<div class="card_number text-center">
							{{ dataList.BottomNumList.questionNum }}
						</div>
						<div class="card_text text-center pb-10px">
							问题台账
						</div>
						<div class="card_border_bottom"></div>
					</div>
					<div class="card_border_right"></div>
					<div class="header_card_item">
						<div class="card_number text-center">
							{{ dataList.BottomNumList.rectificationQuestionNum }}
						</div>
						<div class="card_text text-center pb-10px">
							整改台账
						</div>
						<div class="card_border_bottom"></div>
					</div>
				</div>
				<div class="flex items-center pl-9px pr-9px pt-10px">
					<div class="header_card_item">
						<div class="card_number text-center">
							{{ dataList.BottomNumList.finishQuestionNum || 0 }}
						</div>
						<div class="card_text text-center">
							销号问题
						</div>
					</div>
					<div class="card_border_right"></div>
					<div class="header_card_item">
						<div class="card_number text-center">
							{{ dataList.BottomNumList.constitutionNumb }}
						</div>
						<div class="card_text text-center">
							持续监督
						</div>
					</div>
				</div>
			</div>
			<!-- <div class="pj_card5">
				<div class="main_card_title pt-16px pl-22px">
					<div class="main_card_title_left main_card_title_left_background4"></div>
					<div class="pl-8px">资源分布</div>
				</div>
				<el-row class="flex pl-18px pt-20px">
					<el-col :span="12" :xs="24">
						<div class="card_number text-center">
							10000
						</div>
						<div class="card_text text-center">
							审计单位
						</div>
					</el-col>
					<el-col :span="12" :xs="24">
						<div class="card_number text-center">
							180
						</div>
						<div class="card_text text-center">
							审计人员
						</div>
					</el-col>
				</el-row>
				<el-row class="flex items-center pl-18px pt-20px">
					<el-col :span="12" :xs="24">
						<div class="card_number text-center">
							10000
						</div>
						<div class="card_text text-center">
							中介机构
						</div>
					</el-col>
					<el-col :span="12" :xs="24">
						<div class="card_number text-center">
							180
						</div>
						<div class="card_text text-center">
							中介人员
						</div>
					</el-col>
				</el-row>
			</div> -->
		</div>
		<div class="flex_title mt-16px">
			<div class="title-left"></div>
			<div class="pl-8px">工程审计</div>
		</div>
		<el-row :gutter="8" class="mt-12px">
			<el-col :span="12" :xs="24">
				<ContentWrap>
					<div class="foot_title">
						<div class="foot_title_size">
							<Icon icon="ep:histogram" class="mr-5px" />招标控制价审计
						</div>
						<div class="foot_date">
							<div class="pr-5px">计划年度</div>
							<el-date-picker v-model="queryParams.publishDate" type="year" placeholder="选择"
								class="!w-90px pr-5px" value-format="YYYY"/>
						</div>
					</div>
					<div class="foot_main mt-14px flex items-center">
						<div class="foot_main_item">
							<div class="text-center foot_main_number foot_main_color1">
								{{ dataList.EngineerControlNumList.projectNum }}项</div>
							<div class="text-center card_text foot_main_color1">项目总数</div>
						</div>
						<div class="card_border_right"></div>
						<div class="header_card_item">
							<div ref="oneChartRef" style="height:53px;width:100%;"></div>
						</div>
						<div class="header_card_item">
							<div class="foot_number text-center">
								{{ formatToFixed(dataList.EngineerControlNumList.auditMinusMoney / 10000) }}万
							</div>
							<div class="card_text text-center">
								审减金额
							</div>
						</div>
						<div class="header_card_item">
							<div class="foot_number text-center">
								{{ formatToFixed(dataList?.EngineerControlNumList?.avgAuditMinusRate) }}%
							</div>
							<div class="card_text text-center">
								平均审减率
							</div>
						</div>
						<!-- <div class="header_card_item">
							<div ref="oneChartRef1" style="height:53px;width:100%;"></div>
						</div> -->
						<div class="header_card_item">
							<div class="foot_number text-center">
								{{ formatToFixed(dataList.EngineerControlNumList.reportMoney / 10000) }}万
							</div>
							<div class="card_text text-center">
								报审金额
							</div>
						</div>
						<div class="header_card_item">
							<div class="foot_number text-center">
								{{ dataList.EngineerControlNumList.auditOrgNum }}万
							</div>
							<div class="card_text text-center">
								审定金额
							</div>
						</div>
					</div>
					<el-row :gutter="6" class="mt-12px">
						<el-col :span="7" :xs="24">
							<div class="foot_item flex items-center">
								<div>
									<div class="foot_number text-center ">
										{{ dataList.EngineerControlNumList.auditOrgNum }}家
									</div>
									<div class="card_text text-center">
										委托审计单位
									</div>
								</div>
							</div>
						</el-col>
						<el-col :span="7" :xs="24">
							<div class="foot_item flex items-center">
								<div>
									<div class="foot_number text-center">
										{{ formatToFixed(dataList.EngineerControlNumList.auditFee / 10000) }}万
									</div>
									<div class="card_text text-center">
										审计费用
									</div>
								</div>
							</div>
						</el-col>
						<el-col :span="10" :xs="24">
							<div class="foot_item flex items-center">
								<div class="header_card_item">
									<div ref="oneChartRef2" style="height:53px;width:100%;"></div>
								</div>
								<div class="header_card_item">
									<div class="foot_number text-center">
										{{ formatToFixed(dataList.EngineerControlNumList.auditFinishRate) }}%
									</div>
									<div class="card_text text-center">
										审计完成率
									</div>
								</div>
							</div>
						</el-col>
					</el-row>
				</ContentWrap>
			</el-col>
			<el-col :span="12" :xs="24">
				<ContentWrap>
					<div class="foot_title">
						<div class="foot_title_size">
							<Icon icon="ep:histogram" class="mr-5px" />工程结算审计
						</div>
						<div class="foot_date">
							<div class="pr-5px">计划年度</div>
							<el-date-picker v-model="queryParams.publishDate" type="year" placeholder="选择"
								class="!w-90px pr-5px" value-format="YYYY"/>
						</div>
					</div>
					<div class="foot_main mt-14px flex items-center">
						<div class="foot_main_item">
							<div class="text-center foot_main_number foot_main_color2">
								{{ dataList.EngineerSettleNumList.projectNum }}项</div>
							<div class="text-center card_text foot_main_color2">项目总数</div>
						</div>
						<div class="card_border_right"></div>
						<div class="header_card_item">
							<div ref="oneChartRef3" style="height:53px;width:100%;"></div>
						</div>
						<div class="header_card_item">
							<div class="foot_number text-center">
								{{ formatToFixed(dataList.EngineerSettleNumList.auditMinusMoney / 10000) }}万
							</div>
							<div class="card_text text-center">
								审减金额
							</div>
						</div>
						<div class="header_card_item">
							<div class="foot_number text-center">
								{{ formatToFixed(dataList.EngineerSettleNumList.avgAuditMinusRate) }}%
							</div>
							<div class="card_text text-center">
								平均审减率
							</div>
						</div>
						<!-- <div class="header_card_item">
							<div ref="oneChartRef4" style="height:53px;width:100%;"></div>
						</div> -->
						<div class="header_card_item">
							<div class="foot_number text-center">
								{{ formatToFixed(dataList.EngineerSettleNumList.reportMoney / 10000) }}万
							</div>
							<div class="card_text text-center">
								报审金额
							</div>
						</div>
						<div class="header_card_item">
							<div class="foot_number text-center">
								{{ formatToFixed(dataList.EngineerSettleNumList.auditMoney / 10000) }}万
							</div>
							<div class="card_text text-center">
								审定金额
							</div>
						</div>
					</div>
					<el-row :gutter="6" class="mt-12px">
						<el-col :span="5" :xs="24">
							<div class="foot_item flex items-center">
								<div>
									<div class="foot_number text-center">
										{{ dataList.EngineerSettleNumList.baseBuildEngineerNum }}项
									</div>
									<div class="card_text text-center">
										基建工程
									</div>
								</div>
							</div>
						</el-col>
						<el-col :span="5" :xs="24">
							<div class="foot_item flex items-center">
								<div>
									<div class="foot_number text-center">
										{{ dataList.EngineerSettleNumList.baseBetterEngineerNum }}项
									</div>
									<div class="card_text text-center">
										技改工程
									</div>
								</div>
							</div>
						</el-col>
						<el-col :span="5" :xs="24">
							<div class="foot_item flex items-center">
								<div></div>
								<div>
									<div class="foot_number text-center">
										{{ formatToFixed(dataList.EngineerSettleNumList.auditFee / 10000) }}万
									</div>
									<div class="card_text text-center">
										审计费用
									</div>
								</div>
							</div>
						</el-col>
						<el-col :span="9" :xs="24">
							<div class="foot_item flex items-center">
								<div class="header_card_item">
									<div ref="oneChartRef5" style="height:53px;width:100%;"></div>
								</div>
								<div class="header_card_item">
									<div class="foot_number text-center">
										{{ formatToFixed(dataList.EngineerSettleNumList.auditFinishRate) }}%
									</div>
									<div class="card_text text-center">
										审计完成率
									</div>
								</div>
							</div>
						</el-col>
					</el-row>
				</ContentWrap>
			</el-col>
		</el-row>
	</div>
	<!-- 计划看板 -->
	<PlanningBoard v-if="activeName == '1'" />
	<!-- 项目看板 -->
	<ProjectKanban v-if="activeName == '2'" />
	<!-- 风险看板 -->
	<Risk v-if="activeName == '3'" ref="riskRef" />
	<!-- 问题看板 -->
	<QuestionBoard v-if="activeName == '4'" />
	<!-- 整改看板 -->
	<RectificationKanban v-if="activeName == '5'" />
	<!-- 人员看板 -->
	<PersonnelSignage v-if="activeName == '6'" />
	<!-- 工程看板 -->
	<EngineeringLook v-if="activeName == '7'" />
</template>

<script lang="ts" setup>
import * as echarts from 'echarts';
import Risk from '../decisionAnalysis/risk/index.vue'
import RectificationKanban from '../decisionAnalysis/rectification/rectificationKanban/index.vue'
import PlanningBoard from '../decisionAnalysis/project/planningBoard/index.vue'
import QuestionBoard from '../decisionAnalysis/problem/questionBoard/index.vue'
import PersonnelSignage from '../decisionAnalysis/personnel/personnelSignage/index.vue'
import ProjectKanban from '../decisionAnalysis/item/projectKanban/index.vue'
import EngineeringLook from '../decisionAnalysis/engineering/engineeringLook/index.vue'
import demoviewIcon1 from '@/assets/imgs/demoview/<EMAIL>';
import demoviewIcon2 from '@/assets/imgs/demoview/<EMAIL>';
import demoviewIcon3 from '@/assets/imgs/demoview/<EMAIL>';
import { DemoviewApi, TopNumVO, EngineerSettleNumVO, EngineerControlNumVO, BottomNumVO } from '@/api/decision/demoview'
defineOptions({
	name: 'Demoview'
})
const queryParams = reactive({
	publishDate: undefined,
	startYear: undefined,
	endYear: undefined,
	data: []
})
const loading = ref(true) // 列表的加载中

const activeName = ref('0')
const editableTabs = ref([
	{
		title: '全景视图',
		name: '0',
	},
	// {
	// 	title: '计划看板',
	// 	name: '1',
	// },
	{
		title: '项目看板',
		name: '2',
	},
	{
		title: '风险看板',
		name: '3',
	},
	{
		title: '问题看板',
		name: '4',
	},
	{
		title: '整改看板',
		name: '5',
	},
	// {
	// 	title: '人员看板',
	// 	name: '6',
	// },
	{
		title: '工程看板',
		name: '7',
	}
])

const handleClick = async (type) => {
	console.log(type)
	if (type === '0') {
		nextTick(async () => {
			await getAllApi()
		});
	}
}

const url1 = ref(demoviewIcon1)
const url2 = ref(demoviewIcon2)
const url3 = ref(demoviewIcon3)

const oneChartRef = ref()
// const oneChartRef1 = ref()
const oneChartRef2 = ref()
const oneChartRef3 = ref()
// const oneChartRef4 = ref()
const oneChartRef5 = ref()

const dataList = reactive({
	TopNumList: ref({} as TopNumVO),
	EngineerSettleNumList: ref({} as EngineerSettleNumVO),
	EngineerControlNumList: ref({} as EngineerControlNumVO),
	BottomNumList: ref({} as BottomNumVO)
})

function formatToFixed(num, digits = 2) {
	if (num === null || num === undefined || isNaN(num)) {
		return 'N/A'; // 或者返回其他默认值
	}
	return Number(num).toFixed(digits);
}

const getAllApi = async () => {
	dataList.TopNumList = (await DemoviewApi.getTopNum(queryParams)) as TopNumVO;
	dataList.EngineerSettleNumList = (await DemoviewApi.getEngineerSettleNum(queryParams)) as EngineerSettleNumVO;
	dataList.EngineerControlNumList = (await DemoviewApi.getEngineerControlNum(queryParams)) as EngineerControlNumVO;
	dataList.BottomNumList = (await DemoviewApi.getBottomNum(queryParams)) as BottomNumVO;
	await Promise.all([getRightChart(oneChartRef.value, dataList.EngineerControlNumList.avgAuditMinusRate.toFixed(2), "#9A3C4E")])
	// await Promise.all([getRightChart(oneChartRef1.value, 50, "#FF9C01")])
	await Promise.all([getRightChart(oneChartRef2.value, dataList.EngineerControlNumList.auditFinishRate.toFixed(2), "#1B70C8")])
	await Promise.all([getRightChart(oneChartRef3.value, dataList.EngineerSettleNumList.avgAuditMinusRate.toFixed(2), "#9A3C4E")])
	// await Promise.all([getRightChart(oneChartRef4.value, 50, "#2A8AF7")])
	await Promise.all([getRightChart(oneChartRef5.value, dataList.EngineerSettleNumList.auditFinishRate.toFixed(2), "#1B70C8")])
	loading.value = false
}

const getRightChart = async (ChartRef, data, color) => {
	const chart = echarts.init(ChartRef);
	chart.setOption({
		series: [
			{
				type: 'gauge',
				radius: "100%",
				startAngle: 270,
				endAngle: -90,
				progress: {
					show: true,
					width: 5,
					itemStyle: {
						color: color
					}
				},
				axisLine: {
					lineStyle: {
						width: 5
					}
				},
				axisTick: {
					show: false
				},
				splitLine: {
					show: false
				},
				axisLabel: {
					show: false
				},
				pointer: {
					show: false
				},
				anchor: {
					show: false,
				},
				title: {
					show: false
				},
				detail: {
					valueAnimation: true,
					fontSize: 13,
					offsetCenter: [0, 0],
					formatter: "{value}%"
				},
				data: [
					{
						value: data
					}
				]
			}
		]
	})
}

/** 搜索按钮操作 */
const handleQuery = () => {
	console.log('000')
	queryParams.startYear = queryParams.data[0]
	queryParams.endYear = queryParams.data[1]
	getAllApi()
}

/** 初始化 **/
onMounted(() => {
	getAllApi()
})
</script>

<style scoped>
.header_tabs {
	height: 47px;
	background: #FFFFFF;
}

.demo-tabs {
	padding: 5px 0px;
}

::v-deep .el-tabs__item {
	height: 35px;
	padding: 0 13px;
	padding-left: 13px !important;
	padding-right: 13px !important;
}

::v-deep .el-tabs__active-bar {
	background-color: transparent;
}

::v-deep .el-tabs__item.is-active,
.el-tabs__item:hover {
	background-color: #F3F6FF;
	border-radius: 8px;
	padding-left: 13px !important;
	padding-right: 13px !important;
}

::v-deep.demo-tabs .el-tabs__nav-wrap::after {
	/* 去掉下划线 */
	position: static !important;
}

::v-deep.demo-tabs .el-tabs__header {
	margin: 2px !important;
}

/* 去除输入框边框 */
::v-deep .el-date-editor {
	--el-input-border-color: transparent !important;
	--el-input-bg-color: transparent;
	--el-input-hover-border-color: #fff;
}

.flex_title {
	display: flex;
	align-items: center;
	line-height: 23px;
	font-family: Source Han Sans, Source Han Sans;
	font-weight: 700;
	font-size: 16px;
	color: #333333;
}

.title-left {
	width: 5px;
	height: 15px;
	background: #2F4CAD;
	border-radius: 0px 0px 0px 0px;
}

.header {
	height: 213px;
	background-image: url('@/assets/imgs/demoview/bg-quanjing.png');
	background-repeat: no-repeat;
	background-size: 100% 100%;
}

.header_title {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 16px 20px 0;
}

.header_title_size {
	height: 26px;
	font-family: Source Han Sans, Source Han Sans;
	font-weight: 500;
	font-size: 18px;
	color: #333333;
	line-height: 26px;
	text-align: left;
	font-style: normal;
	text-transform: none;
}

.header_date {
	width: 274px;
	height: 36px;
	background: rgba(255, 255, 255, 0.6);
	border-radius: 8px 8px 8px 8px;
	border: 1px solid #FFFFFF;
	font-family: Source Han Sans, Source Han Sans;
	font-weight: 400;
	font-size: 15px;
	color: #999;
	line-height: 22px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.header_card1 {
	height: 133px;
	background-image: url('@/assets/imgs/demoview/bg-xiangmu.png');
	background-repeat: no-repeat;
	background-size: cover;
	margin-right: 6px;
	border-radius: 5px;
}

.header_card2 {
	height: 133px;
	background-image: url('@/assets/imgs/demoview/bg-shenji.png');
	background-repeat: no-repeat;
	background-size: cover;
	margin: 0 6px;
	border-radius: 5px;
}

.header_card3 {
	height: 133px;
	background-image: url('@/assets/imgs/demoview/bg-qita.png');
	background-repeat: no-repeat;
	background-size: cover;
	margin-left: 6px;
	border-radius: 5px;
}

.header_card_title {
	height: 23px;
	font-family: Source Han Sans, Source Han Sans;
	font-weight: 500;
	font-size: 16px;
	color: #fff;
	line-height: 23px;
	padding-top: 14px;
	padding-left: 22px;
}

.header_card_item {
	flex: 1;
}

.header_card_number {
	height: 42px;
	font-family: Source Han Sans, Source Han Sans;
	font-weight: 700;
	font-size: 26px;
	color: #fff;
	line-height: 42px;
}

.header_card_text {
	height: 21px;
	font-family: Source Han Sans, Source Han Sans;
	font-weight: 400;
	font-size: 15px;
	color: #fff;
	line-height: 21px;
}

.card_number {
	height: 43px;
	font-family: Source Han Sans, Source Han Sans;
	font-weight: 700;
	font-size: 26px;
	color: #333333;
	line-height: 43px;
}

.card_text {
	height: 22px;
	font-family: Source Han Sans, Source Han Sans;
	font-weight: 400;
	font-size: 15px;
	color: #999999;
	line-height: 22px;
}

.main_card {
	display: flex;
	align-items: center;
}

.pj_card1 {
	flex: 1;
	height: 246px;
	background-image: url('@/assets/imgs/demoview/bg-shenjixiangmu.png');
	background-repeat: no-repeat;
	background-size: 100% 100%;
	margin-right: 16px;
	background-color: #fff;
}

.pj_card2 {
	flex: 1;
	height: 246px;
	background-image: url('@/assets/imgs/demoview/bg-fengxianditu.png');
	background-repeat: no-repeat;
	background-size: 100% 100%;
	margin-right: 16px;
	background-color: #fff;
}

.pj_card3 {
	flex: 1;
	height: 246px;
	background-image: url('@/assets/imgs/demoview/bg-shenjichegnguo.png');
	background-repeat: no-repeat;
	background-size: 100% 100%;
	margin-right: 16px;
	background-color: #fff;
}

.pj_card4 {
	flex: 1;
	height: 246px;
	background-image: url('@/assets/imgs/demoview/bg-wentizhenggai.png');
	background-repeat: no-repeat;
	background-size: 100% 100%;
	/* margin-right: 16px; */
	background-color: #fff;
}

.pj_card5 {
	flex: 1;
	height: 246px;
	background-image: url('@/assets/imgs/demoview/bg-ziyuanfenbu.png');
	background-repeat: no-repeat;
	background-size: cover;
}

.main_card_title {
	display: flex;
	align-items: center;
	font-family: Source Han Sans, Source Han Sans;
	font-weight: 500;
	font-size: 18px;
	color: #333333;
	line-height: 26px;
}

.card_border_bottom {
	width: 90px;
	border-bottom: 1px solid rgba(238, 239, 240, 1);
	margin-left: 35px;
}

.card_border_right {
	height: 50px;
	border-right: 1px solid rgba(238, 239, 240, 1);
}

.main_card_title_left {
	width: 8px;
	height: 8px;
	border-radius: 5px;
}

.main_card_title_left_background {
	background: #216CC0;
}

.main_card_title_left_background1 {
	background: #2A8AF7;
}

.main_card_title_left_background2 {
	background: #FF9C01;
}

.main_card_title_left_background3 {
	background: #D0DAFF;
}

.main_card_title_left_background4 {
	background: #3582F7;
}

.foot_title {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.foot_title_size {
	font-family: Source Han Sans, Source Han Sans;
	font-weight: 500;
	font-size: 18px;
	color: #333333;
	line-height: 26px;
}

.foot_date {
	width: 174px;
	height: 38px;
	background: #F9FAFC;
	border-radius: 8px 8px 8px 8px;
	font-family: Source Han Sans, Source Han Sans;
	font-weight: 400;
	font-size: 15px;
	color: #999;
	line-height: 22px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.foot_main {
	height: 101px;
	background: #FFFFFF;
	box-shadow: 0px 12px 30px 0px rgba(0, 0, 0, 0.04);
	border-radius: 8px 8px 8px 8px;
}

.foot_item {
	height: 101px;
	border-radius: 8px 8px 8px 8px;
	border: 1px solid #EEEFF0;
	justify-content: center;
}

.foot_number {
	font-family: Source Han Sans, Source Han Sans;
	font-weight: 700;
	font-size: 16px;
	color: #333333;
	line-height: 23px;
}

.foot_main_item {
	flex: 2;
}

.foot_main_number {
	font-family: Source Han Sans, Source Han Sans;
	font-weight: 700;
	font-size: 32px;
	line-height: 49px;
}

.foot_main_color2 {
	color: #053A7E;
}

.foot_main_color1 {
	color: #053A7E;
}
</style>