<template>
	<Dialog v-model="dialogVisible" :scroll="true" :title="dialogTitle" width="65%">
		<ContentWrap v-if="showType === 0">
			<!-- 搜索工作栏 -->
			<el-form class="-mb-15px" :model="queryParams" ref="queryFormRef" :inline="true" label-width="100px">
				<el-form-item label="文号" prop="docNum">
					<el-input v-model="queryParams.docNum" placeholder="请输入文号" clearable @keyup.enter="handleQuery"
						class="!w-200px" />
				</el-form-item>
				<!-- <el-form-item label="发布主体" prop="status">
					<el-select v-model="queryParams.status" placeholder="请选择发布主体" clearable class="!w-200px">
						<el-option v-for="dict in getIntDictOptions(DICT_TYPE.AUDIT_TYPE_STATUS)" :key="dict.value"
							:label="dict.label" :value="dict.value" />
					</el-select>
				</el-form-item> -->
				<el-form-item label="法律法规名称" prop="lawName">
					<el-input v-model="queryParams.lawName" placeholder="请输入法律法规名称" clearable @keyup.enter="handleQuery"
						class="!w-200px" />
				</el-form-item>
				<!-- <el-form-item label="法规状态" prop="status">
					<el-select v-model="queryParams.status" placeholder="请选择法规状态" clearable class="!w-200px">
						<el-option v-for="dict in getIntDictOptions(DICT_TYPE.AUDIT_TYPE_STATUS)" :key="dict.value"
							:label="dict.label" :value="dict.value" />
					</el-select>
				</el-form-item>
				<el-form-item label="发布日期">
					<el-date-picker v-model="queryParams.date" type="date" clearable placeholder="选择发布日期"
						class="!w-200px" />
				</el-form-item>
				<el-form-item label="生效日期">
					<el-date-picker v-model="queryParams.date" type="date" clearable placeholder="选择生效日期"
						class="!w-200px" />
				</el-form-item> -->
				<el-form-item>
					<el-button @click="handleQuery">
						<Icon icon="ep:search" class="mr-5px" />搜索
					</el-button>
					<el-button @click="resetQuery">
						<Icon icon="ep:refresh" class="mr-5px" />重置
					</el-button>

					<!-- <el-button :loading="exportLoading" plain type="success"
						@click="handleExport">
						<Icon class="mr-5px" icon="ep:download" />导出
					</el-button> -->
				</el-form-item>
			</el-form>
		</ContentWrap>
		<ContentWrap v-if="showType === 0">
			<el-table v-loading="loading" :data="list" border row-key="id" ref="multipleTableRef"
				@selection-change="handleSelectionChange" :stripe="true" :show-overflow-tooltip="true">
				<el-table-column type="selection" width="55" align="center"/>
				<el-table-column label="序号" width="60" align="center">
					<template
						#default="{ $index }">{{ (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1 }}</template>
				</el-table-column>
				<el-table-column label="文号" align="left" prop="docNum" min-width="180" />
				<el-table-column label="发布主体" align="center" prop="publishMain">
					<template #default="scope">
						<dict-tag :type="DICT_TYPE.EXTERNAL_LAWS_REGULATIONS_SUBJECT" :value="scope.row.publishMain" />
					</template>
				</el-table-column>
				<el-table-column label="法律法规名称" align="left" prop="lawName" min-width="180"/>
				<!-- <el-table-column label="分类" align="center" prop="adder" /> -->
				<el-table-column label="发文日期" key="publishDate" align="center" min-width="100" >
					<template #default="scope">
						<span>{{ formatTime(scope.row.publishDate, 'yyyy-MM-dd') }}</span>
					</template>
				</el-table-column>
				<el-table-column label="生效日期" align="center" prop="effeDate" min-width="100">
					<template #default="scope">
						<span>{{ formatTime(scope.row.effeDate, 'yyyy-MM-dd') }}</span>
					</template>
				</el-table-column>
				<el-table-column label="法规状态" align="center" prop="lawStatus" min-width="100">
					<template #default="scope">
						<dict-tag :type="DICT_TYPE.EXTERNAL_LAWS_REGULATIONS_STATE" :value="scope.row.lawStatus" />
					</template>
				</el-table-column>
			</el-table>
			<!-- 分页 -->
			<Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
				@pagination="getList" />
		</ContentWrap>
		<ContentWrap v-if="showType === 1">
			<!-- 搜索工作栏 -->
			<el-form class="-mb-15px" :model="queryParams" ref="queryFormRef" :inline="true" label-width="75px">
				<el-form-item label="公司名称" prop="companyName">
					<el-tree-select
						v-model="queryParams.companyId"
						ref="treeRef"
						filterable
						clearable
						placeholder="请选择公司名称"
						:data="deptList"
						check-strictly
						:expand-on-click-node="false"
						:check-on-click-node="true"
						:default-expand-all="false"
						highlight-current
						node-key="id"
						@node-click="handleNodeClickCorporation"
						:load="loadNode"
						:default-expanded-keys="defaultExpandedKeys"
						:filter-node-method="filterNode"
						lazy
						class="!w-240px"
						>
						<template #default="{ data: { name } }">{{ name }}</template>
					</el-tree-select>
				</el-form-item>
				<el-form-item label="分类名称" prop="classificationName">
					<el-input v-model="queryParams.classificationName" placeholder="请输入分类名称" clearable @keyup.enter="handleQuery"
						class="!w-240px" />
				</el-form-item>
				<el-form-item label="标准编号" prop="standarNum">
					<el-input v-model="queryParams.standarNum" placeholder="请输入标准编号" clearable @keyup.enter="handleQuery"
						class="!w-240px" />
				</el-form-item>
				<el-form-item label="标准体系" prop="standardsysCode">
					<!-- <el-input v-model="queryParams.standardsysName" placeholder="请输入标准体系" clearable @keyup.enter="handleQuery"
						class="!w-240px" /> -->
					<el-select v-model="queryParams.standardsysCode" placeholder="请选择标准体系" clearable class="!w-240px">
						<el-option v-for="dict in getIntDictOptions('institutionalsy_stem')" :key="dict.value"
							:label="dict.label" :value="dict.value" />
					</el-select>
				</el-form-item>
				<el-form-item label="执行日期" prop="exeDate">
					<el-date-picker v-model="queryParams.exeDate" type="date" clearable placeholder="选择某一天" value-format="x" format="YYYY-MM-DD" class="!w-240px" />
				</el-form-item>
				<el-form-item label="归口部门" prop="deptId">
					<el-tree-select
						v-model="queryParams.deptId"
						ref="treeRef"
						filterable
						clearable
						placeholder="请选择归口部门"
						:data="deptList"
						check-strictly
						:expand-on-click-node="false"
						:check-on-click-node="true"
						:default-expand-all="false"
						highlight-current
						node-key="id"
						@node-click="handleNodeClick"
						:load="loadNode"
						:default-expanded-keys="defaultExpandedKeys"
						:filter-node-method="filterNode"
						lazy
						class="!w-240px"
						>
						<template #default="{ data: { name } }">{{ name }}</template>
					</el-tree-select>
				</el-form-item>
				<el-form-item label="标准状态" prop="status">
					<el-select v-model="queryParams.status" placeholder="请选择标准状态" clearable class="!w-240px">
						<el-option v-for="dict in getIntDictOptions('Internalst_andard_state')" :key="dict.value"
							:label="dict.label" :value="dict.value" />
					</el-select>
				</el-form-item>
				<el-form-item label="修订/制订" prop="amend">
					<el-select v-model="queryParams.amend" placeholder="请选择修订/制订" clearable class="!w-240px">
						<el-option v-for="dict in getIntDictOptions('internalre_vision')" :key="dict.value"
							:label="dict.label" :value="dict.value" />
					</el-select>
				</el-form-item>
				<el-form-item label="标准名称" prop="standarName">
					<el-input v-model="queryParams.standarName" placeholder="请输入标准名称" clearable @keyup.enter="handleQuery"
						class="!w-240px" />
				</el-form-item>
				<el-form-item>
					<el-button @click="handleQuery">
						<Icon icon="ep:search" class="mr-5px" />搜索
					</el-button>
					<el-button @click="resetQuery">
						<Icon icon="ep:refresh" class="mr-5px" />重置
					</el-button>

					<!-- <el-button :loading="exportLoading" plain type="success"
						@click="handleExport">
						<Icon class="mr-5px" icon="ep:download" />导出
					</el-button> -->
				</el-form-item>
			</el-form>
		</ContentWrap>
		<ContentWrap v-if="showType === 1">
			<el-table v-loading="loading" :data="list" border @selection-change="handleSelectionChange" :stripe="true"
				:show-overflow-tooltip="true">
				<el-table-column type="selection" width="55" align="center"/>
				<el-table-column label="序号" width="60" align="center">
					<template
						#default="{ $index }">{{ (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1 }}</template>
				</el-table-column>
			<el-table-column label="公司名称" align="left" prop="companyName" min-width="180" />
			<el-table-column label="分类" align="center" prop="classificationName" min-width="120"/>
			<el-table-column label="标准编号" align="left" prop="standarNum" min-width="180"/>
			<el-table-column label="标准体系" align="center" prop="standardsysCode" min-width="100">
				<template #default="scope">
					<dict-tag type="institutionalsy_stem" :value="scope.row.standardsysCode" />
				</template>
			</el-table-column>
			<el-table-column label="标准名称" align="left" prop="standarName" min-width="180"/>
			<el-table-column label="归口部门" align="left" prop="deptName" min-width="180"/>
			<el-table-column label="标准状态" align="center" prop="status" min-width="100">
				<template #default="scope">
					<dict-tag type="Internalst_andard_state" :value="scope.row.status" />
				</template>
			</el-table-column>
			<el-table-column label="修订/制订" align="center" prop="amend" min-width="100">
				<template #default="scope">
					<dict-tag type="internalre_vision" :value="scope.row.amend" />
				</template>
			</el-table-column>
			<!-- <el-table-column label="状态" key="status">
				<template #default="scope">
					<el-switch style="--el-switch-on-color: #13ce66" v-model="scope.row.status" :active-value="1"
						:inactive-value="0" @change="handleStatusChange(scope.row)" />
				</template>
			</el-table-column> -->
			<el-table-column label="发文日期" key="exeDate" align="center" min-width="120">
				<template #default="scope">
					<span>{{ formatTime(scope.row.exeDate, 'yyyy-MM-dd') }}</span>
				</template>
			</el-table-column>
			<el-table-column label="备注" align="left" prop="remark" min-width="180"/>
			</el-table>
			<!-- 分页 -->
			<Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
				@pagination="getList" />
		</ContentWrap>
		<ContentWrap v-if="showType ===2">
			<el-form class="-mb-15px" :model="queryParams" ref="queryFormRef" :inline="true" label-width="110px">
				<el-form-item label="资料名称" prop="materialName">
					<el-input v-model="queryParams.materialName" placeholder="请输入资料名称" clearable
						@keyup.enter="handleQuery" class="!w-200px" />
				</el-form-item>
				<el-form-item label="事项名称" prop="mattersName">
					<el-input v-model="queryParams.mattersName" placeholder="请输入事项名称" clearable
						@keyup.enter="handleQuery" class="!w-200px" />
				</el-form-item>
				<!-- <el-form-item label="是否有模版" prop="status">
					<el-select v-model="queryParams.status" placeholder="请选择是否有模版" clearable class="!w-200px">
						<el-option v-for="dict in getIntDictOptions(DICT_TYPE.AUDIT_TYPE_STATUS)" :key="dict.value"
							:label="dict.label" :value="dict.value" />
					</el-select>
				</el-form-item>
				<el-form-item label="资料说明" prop="auditRoleName">
					<el-input v-model="queryParams.auditRoleName" placeholder="请输入资料说明" clearable
						@keyup.enter="handleQuery" class="!w-200px" />
				</el-form-item> -->
				<el-form-item>
					<el-button @click="handleQuery">
						<Icon icon="ep:search" class="mr-5px" />搜索
					</el-button>
					<el-button @click="resetQuery">
						<Icon icon="ep:refresh" class="mr-5px" />重置
					</el-button>

					<!-- <el-button:loading="exportLoading" plain type="success"
						@click="handleExport">
						<Icon class="mr-5px" icon="ep:download" />导出
					</el-button:loading=> -->
				</el-form-item>
			</el-form>
		</ContentWrap>
		<ContentWrap v-if="showType ===2">
			<el-table v-loading="loading" :data="list" border @selection-change="handleSelectionChange" :stripe="true"
				:show-overflow-tooltip="true">
				<el-table-column type="selection" width="55" align="center"/>
				<el-table-column label="序号" width="60" align="center">
					<template
						#default="{ $index }">{{ (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1 }}</template>
				</el-table-column>
				<el-table-column label="资料名称" align="left" prop="materialName" min-width="120" />
				<el-table-column label="事项名称" align="left" prop="mattersName" min-width="180"/>
				<el-table-column label="是否有模版" key="templateFlag" align="center">
					<template #default="{row}">
						<dict-tag :type="'audit_data_list_template'" :value="row.templateFlag" />
					</template>
				</el-table-column>
				<el-table-column label="资料说明" align="left" prop="materialDesc" min-width="180"/>
			</el-table>
			<!-- 分页 -->
			<Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
				@pagination="getList" />
		</ContentWrap>
		<ContentWrap v-if="showType ===3">
			<el-form class="-mb-15px" :model="queryParams" ref="queryFormRef" :inline="true" label-width="110px">
				<el-form-item label="资料名称" prop="auditRoleName">
					<el-input v-model="queryParams.auditRoleName" placeholder="请输入资料名称" clearable
						@keyup.enter="handleQuery" class="!w-200px" />
				</el-form-item>
				<el-form-item label="事项名称" prop="auditRoleName">
					<el-input v-model="queryParams.auditRoleName" placeholder="请输入事项名称" clearable
						@keyup.enter="handleQuery" class="!w-200px" />
				</el-form-item>
				<el-form-item label="是否有模版" prop="status">
					<el-select v-model="queryParams.status" placeholder="请选择是否有模版" clearable class="!w-200px">
						<el-option v-for="dict in getIntDictOptions(DICT_TYPE.AUDIT_TYPE_STATUS)" :key="dict.value"
							:label="dict.label" :value="dict.value" />
					</el-select>
				</el-form-item>
				<el-form-item label="资料说明" prop="auditRoleName">
					<el-input v-model="queryParams.auditRoleName" placeholder="请输入资料说明" clearable
						@keyup.enter="handleQuery" class="!w-200px" />
				</el-form-item>
				<el-form-item>
					<el-button @click="handleQuery">
						<Icon icon="ep:search" class="mr-5px" />搜索
					</el-button>
					<el-button @click="resetQuery">
						<Icon icon="ep:refresh" class="mr-5px" />重置
					</el-button>
					<!-- <el-button :loading="exportLoading" plain type="success"
						@click="handleExport">
						<Icon class="mr-5px" icon="ep:download" />导出
					</el-button> -->
				</el-form-item>
			</el-form>
		</ContentWrap>
		<ContentWrap v-if="showType ===3">
			<el-table v-loading="loading" :data="list" border @selection-change="handleSelectionChange" :stripe="true"
				:show-overflow-tooltip="true">
				<el-table-column type="selection" width="40"/>
				<el-table-column label="序号" width="60" align="center">
					<template
						#default="{ $index }">{{ (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1 }}</template>
				</el-table-column>
				<el-table-column label="资料名称" align="center" prop="auditRoleCode" min-width="120" />
				<el-table-column label="事项名称" align="center" prop="auditRoleName" />
				<el-table-column label="是否有模版" align="center" prop="sort" />
				<el-table-column label="资料说明" align="center" prop="adder" />
			</el-table>
			<!-- 分页 -->
			<Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
				@pagination="getList" />
		</ContentWrap>
		<template #footer>
			<el-button :disabled="multiple" type="primary" @click="submitForm">确 定</el-button>
			<el-button @click="dialogVisible = false">取 消</el-button>
		</template>
	</Dialog>
</template>
<script lang="ts" setup>
	import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
	import { UserPermissionApi, UserPermissionVO } from '@/api/system/userpermission'
	import { RegulationsApi } from '@/api/jobmanger/regulations/outregulations'
	import { CheckListApi } from '@/api/jobmanger/checklist'
	import { RulesRegulationsApi } from '@/api/jobmanger/regulations/innerregulations'
	import { formatTime } from '@/utils'
	import * as DeptApi from '@/api/system/dept'
	import { defaultProps, handleLazyTree } from '@/utils/tree'
	defineOptions({ name: 'CreateLibraryModal' })

	const { t } = useI18n() // 国际化
	const message = useMessage() // 消息弹窗
	const dialogVisible = ref(false) // 弹窗的是否展示
	const dialogTitle = ref('') // 弹窗的标题
	const formType = ref('') // 表单的类型：create - 新增；update - 修改

	const loading = ref(true) // 列表的加载中
	const total = ref(0) // 列表的总页数
	const list = ref([]) // 列表的数据
	const queryParams = ref({
		pageNo: 1,
		pageSize: 10,

		// 外部法律法规默认现行数据
		lawStatus: 0,

		docNum: undefined,
		lawName: undefined,
		materialName: undefined,
		mattersName: undefined,

		username: undefined,
		nickname: undefined,
		mobile: undefined,
		status: undefined,
	})
	const queryFormRef = ref() // 搜索的表单
	const formRef = ref() // 表单 Ref
	/** 查询角色列表 */
	const getListByType = (type : number) => {
		if (type === 0) {
			return RegulationsApi.getRegulationsList(queryParams.value)
		} else if (type === 1) {
			return RulesRegulationsApi.getRulesRegulationsList(queryParams.value)
		} else if (type === 2) {
			return CheckListApi.getCheckList(queryParams.value)
		} else if (type === 3) {
			return UserPermissionApi.setPermissionByRole({})
		}
	}
	const getList = async () => {
		loading.value = true
		try {
			const data = await getListByType(showType.value)
			list.value = data.list || []
			total.value = data.total || 0
		} finally {
			loading.value = false
		}
	}
	const handleQuery = () => {
		queryParams.value.pageNo = 1
		getList()
	}
	/** 重置按钮操作 */
	const resetQuery = () => {
		queryFormRef.value.resetFields()
		handleQuery()
	}

	/** 打开弹窗 */
	const showType = ref(0)
	const open = async (id ?: number) => {
		getTree(0)
		dialogVisible.value = true
		dialogTitle.value = t('action.create')
		resetForm()
		// 修改时，设置数据
		console.log(id, '111')
		showType.value = id
		getList()
	}
	defineExpose({ open }) // 提供 open 方法，用于打开弹窗

	const emit = defineEmits(['success_receive']) // 定义 success 事件，用于操作成功后的回调
	// const multipleSelection = ref([])
	// const handleSelectionChange = (val) => {
	// 	multipleSelection.value = val
	// }
	const getSubmitApi = (type : number, data) => {
		if (type === 0) {
			return UserPermissionApi.setPermissionByRole(data)
		} else if (type === 1) {
			return UserPermissionApi.setPermissionByRole(data)
		} else if (type === 2) {
			return UserPermissionApi.setPermissionByRole(data)
		} else if (type === 3) {
			return UserPermissionApi.setPermissionByRole(data)
		}
	}
	const submitForm = async () => {
		// if (multipleSelection?.value?.length <= 0) {
		// 	message.error('请选择需要添加的用户')
		// 	return
		// }
		multiple.value = true
		try {
			// let data = multipleSelection?.value.map((item) => {
			//   return {
			//     roleId: queryParams.value.roleId,
			//     userId: item.id
			//   }
			// })
			// await getSubmitApi(showType.value, multipleSelection)
			// await UserPermissionApi.setPermissionByRole(data)
			message.success(t('common.createSuccess'))
			emit('success_receive', showType.value, multipleSelection.value)
			resetForm()
			dialogVisible.value = false
		} finally {
			multiple.value = false
		}
	}
	const addNewRole = ref()
	const addRole = () => {
		addNewRole.value.open()
	}
	/** 重置表单 */
	const resetForm = () => {
		queryParams.value = {
			pageNo: 1,
			pageSize: 10,
			docNum: undefined,
			lawName: undefined,
			materialName: undefined,
			mattersName: undefined,
			username: undefined,
			nickname: undefined,
			mobile: undefined,
			status: undefined,
			lawStatus: 0,
		}
		queryFormRef.value?.resetFields()
	}

	// 项目资料清单多选
	const multipleTableRef = ref<TableInstance>()
	const multipleSelection = ref([])
	const ids = ref([])
	const multiple = ref(true)

	// const toggleSelection = (rows) => {
	// 	if (rows) {
	// 		rows.forEach((el) => {
	// 			const row = tableData.value!.find(e => e.id === el.attTypeId); // row是当前表格数据
	// 			row && multipleTableRef.value!.toggleRowSelection(
	// 				row,
	// 				undefined,
	// 			)
	// 		})
	// 	} else {
	// 		multipleTableRef.value!.clearSelection()
	// 	}
	// }
	const handleSelectionChange = (val : configuraList[]) => {
		multipleSelection.value = val
		ids.value = val.map((item) => item.id)
		multiple.value = !val.length
	}

	// 填报单位树结构
	interface DeptNode {
		id: number
		masterOrgId?: number | string
		name: string
		parentId: number | string
		children?: DeptNode[]
		isLeaf?: boolean
	}
	const deptList = ref<DeptNode[]>([]) // 树形结构
	const defaultExpandedKeys = ref<(string | number)[]>([])
	const getTree = async (id) => {
		deptList.value = []
		const res = await DeptApi.getSimpleDeptList(id)
		deptList.value.push(...handleLazyTree(res, 'id', 'parentId'))
		defaultExpandedKeys.value = deptList.value.map((node) => node.id)
		console.log(deptList.value)
	}
	const loadNode = async (node: Node, resolve: (data: any[]) => void) => {
		try {
			const nodeId = node.data.id
			if (nodeId == undefined || nodeId == null) {
				return
			}
			const res = await DeptApi.getSimpleDeptList(nodeId)
			const children = handleLazyTree(res, 'id', 'parentId', 'children')
			resolve(children)
		} catch (error) {
			resolve([])
		}
	}
</script>
