<!--
* @Author: wangk
* @Date: 2024-10-26 17:10:50
* @Description: 领导班子业绩考核=>
-->
<template>
  <div class="flex">
    <!-- 列表 -->
    <ContentWrap class="w-50%" :title="'应收款余额'" :shut="true">
      <!-- 应收款余额表格 -->
      <el-table border v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
        <el-table-column label="序号" width="60" align="center">
          <template #default="{ $index }">{{
            (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1
          }}</template>
        </el-table-column>
        <el-table-column label="指标名称" align="center" prop="questionType" />
        <el-table-column label="上档基础分" align="center" prop="questionTitle" />
        <el-table-column label="本档基础分" align="center" prop="questionTitle" />
        <el-table-column label="调整分" align="center" prop="questionTitle" />
        <el-table-column label="指标得分" align="center" prop="questionTitle" />
      </el-table>
      <!-- 分页 -->
      <Pagination
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="props.getList()"
      />
    </ContentWrap>
    <!-- 客户贡献 应收款余额 图 -->
    <div class="w-50%">
      <ContentWrap class="m-l-15px" :title="'应收款余额构成'" :shut="true">
        <div>
          <div :class="bottomChart" ref="bottomChartRef" style="height: 500px; width: 100%"></div>
        </div>
      </ContentWrap>
    </div>
  </div>
</template>

<script setup lang="ts">
// import { RegulationsVO } from '@/api/jobmanger/regulations/outregulations'
// import { reformingStandingBookApi } from '@/api/auditManagement/auditRectifica/reformingStandingBook'
import { handleLazyTree } from '@/utils/tree'
import * as DeptApi from '@/api/system/dept'
import * as echarts from 'echarts'
import { propTypes } from '@/utils/propTypes'
// import { formatTime } from '@/utils'
defineOptions({ name: 'Card1' })

const loading = ref(false) // 列表的加载中
const list = ref() // 列表的数据
const queryParams = reactive({
  pageSize: 10,
  pageNo: 1
})
const total = ref(0)
// 定义属性
const props = defineProps({
  tableData: propTypes.object.def({}),
  getList: {
    type: Function,
    default: ()=>{}
  }
})
list.value=props.tableData
// 获取单位
interface DeptNode {
  id: number
  masterOrgId?: number | string
  name: string
  parentId: number | string
  children?: DeptNode[]
  isLeaf?: boolean
}
const deptList = ref<DeptNode[]>([]) // 树形结构
const defaultExpandedKeys = ref<(string | number)[]>([])
const getTree = async (id) => {
  deptList.value = []
  const res = await DeptApi.getSimpleDeptList(id)
  deptList.value.push(...handleLazyTree(res, 'id', 'parentId'))
  defaultExpandedKeys.value = deptList.value.map((node) => node.id)
}
// 右边图表
let bottomChart = null
const bottomChartRef = ref<InstanceType<typeof ElTree>>()
const getBottomChart = async () => {
  bottomChart = echarts.init(bottomChartRef.value)
  bottomChart.setOption({
  legend: {
    top: 'bottom'
  },
  toolbox: {
    show: true,
    // feature: {
    //   mark: { show: true },
    //   dataView: { show: true, readOnly: false },
    //   restore: { show: true },
    //   saveAsImage: { show: true }
    // }
  },
  series: [
    {
      name: 'Nightingale Chart',
      type: 'pie',
      radius: [50, 250],
      center: ['55%', '55%'],
      roseType: 'area',
      itemStyle: {
        borderRadius: 8
      },
      data: [
        { value: 40, name: 'rose 1' },
        { value: 38, name: 'rose 2' },
        { value: 32, name: 'rose 3' },
        { value: 30, name: 'rose 4' },
        { value: 28, name: 'rose 5' },
        { value: 26, name: 'rose 6' },
        { value: 22, name: 'rose 7' },
        { value: 18, name: 'rose 8' }
      ]
    }
  ]
})
}


/** 初始化 **/
onMounted(async () => {
  list.value=props.tableData
  total.value=list.value.length
  console.log(list,'11111')
  getTree(0)
  await getBottomChart()
})
</script>
