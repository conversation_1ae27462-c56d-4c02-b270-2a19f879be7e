<template>
  <el-row v-if="topType == 1">
    <el-col :span="24">
      <ContentWrap>
        <el-radio-group v-model="roleType" @change="handleTypeChange">
          <el-radio value="1" size="large">按用户</el-radio>
          <!-- <el-radio value="2" size="large">按组织</el-radio> -->
          <el-radio value="3" size="large">按角色</el-radio>
        </el-radio-group>
        <div class="common-card-search flex flex-justify-around">
          <!-- 搜索工作栏 -->
          <el-form
            class="-mb-15px common-search-form"
            :model="queryParams"
            ref="queryFormRef"
            :inline="true"
            label-width="88px"
          >
            <el-form-item label="账号" prop="username">
              <el-input
                v-model="queryParams.username"
                placeholder="请输入账号"
                clearable
                @keyup.enter="handleQuery"
                class="!w-200px"
              />
            </el-form-item>
            <el-form-item label="姓名" prop="nickname">
              <el-input
                v-model="queryParams.nickname"
                placeholder="请输入姓名"
                clearable
                @keyup.enter="handleQuery"
                class="!w-200px"
              />
            </el-form-item>
            <el-form-item label="手机号码" prop="mobile">
              <el-input
                v-model="queryParams.mobile"
                placeholder="请输入手机号码"
                clearable
                @keyup.enter="handleQuery"
                class="!w-200px"
              />
            </el-form-item>
            <el-form-item label="状态" prop="status">
              <el-select v-model="queryParams.status" placeholder="用户状态" clearable class="!w-200px">
                <el-option
                  v-for="dict in getIntDictOptions(DICT_TYPE.SYSTEM_USER_STATUS)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <!-- <el-form-item>
              <el-button type="primary" @click="handleQuery">
                <Icon icon="ep:search" />查询
              </el-button>
              <el-button @click="resetQuery">
                <Icon icon="ep:refresh" />重置
              </el-button>
            </el-form-item> -->
          </el-form>
      <div class="right-search-btn">
        <el-button  type="primary"  @click="handleQuery">
            <Icon icon="ep:search" class="mr-5px" />搜索
          </el-button>
          <el-button @click="resetQuery">
            <Icon icon="ep:refresh" class="mr-5px" />重置
          </el-button>
      </div>
        </div>
      </ContentWrap>

      <!-- 列表 -->
      <ContentWrap>
        <div class="button_margin15">
            <el-button
              plain
              @click="handleExport"
              :loading="exportLoading"
              v-hasPermi="['system:user:export']"
            >
              <Icon icon="ep:download" />导出
            </el-button>
          </div>
        <el-table border v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
          <el-table-column label="序号" width="60" align="center">
            <template
              #default="{ $index }"
            >{{ (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1 }}</template>
          </el-table-column>
          <el-table-column
            label="账号"
            align="center"
            prop="username"
            :show-overflow-tooltip="true"
            min-width="120"
          />
          <el-table-column
            label="姓名"
            align="center"
            prop="nickname"
            :show-overflow-tooltip="true"
            min-width="120"
          />
          <el-table-column label="手机号码" align="center" prop="mobile" min-width="120" />
          <el-table-column
            label="所属部门"
            align="center"
            key="deptName"
            prop="deptName"
            :show-overflow-tooltip="true"
          />
          <el-table-column label="状态" key="status"  align="center" width="80">
            <template #default="scope">
              <dict-tag :type="DICT_TYPE.SYSTEM_USER_STATUS" :value="scope.row.status" />
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" :width="200">
            <template #default="scope">
              <el-button
                link
                type="primary"
                size="small"
                @click="showRightModal('role', scope.row.id)"
                v-hasPermi="['system:user-permission:update']"
              >角色权限</el-button>
              <el-button
                link
                size="small"
                type="danger"
                @click="showRightModal('data', scope.row.id)"
                v-hasPermi="['system:user-permission:delete']"
              >数据权限</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页 -->
        <Pagination
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </ContentWrap>
    </el-col>
    <!-- <el-col :span="11" v-if="rightType == 'role'">
      <ContentWrap>
        <el-form
          class="-mb-15px"
          :model="rightQueryParams"
          ref="queryRightFormRef"
          :inline="true"
          label-width="68px"
        >
          <el-form-item label="角色编码" prop="code">
            <el-input
              v-model="rightQueryParams.code"
              placeholder="请输入角色编码"
              clearable
              @keyup.enter="handleRightQuery"
              class="!w-170px"
            />
          </el-form-item>
          <el-form-item label="角色名称" prop="name">
            <el-input
              v-model="rightQueryParams.name"
              placeholder="请输入角色名称"
              clearable
              @keyup.enter="handleRightQuery"
              class="!w-170px"
            />
          </el-form-item>
          <el-form-item label="角色描述" prop="remark">
            <el-input
              v-model="rightQueryParams.remark"
              placeholder="请输入角色描述"
              clearable
              @keyup.enter="handleRightQuery"
              class="!w-170px"
            />
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select v-model="queryParams.status" placeholder="用户状态" clearable class="!w-170px">
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button @click="handleRightQuery">
              <Icon icon="ep:search" class="mr-5px" />查询
            </el-button>
            <el-button @click="resetRightQuery">
              <Icon icon="ep:refresh" class="mr-5px" />重置
            </el-button>
          </el-form-item>
        </el-form>
      </ContentWrap>

      <ContentWrap>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['system:user:export']"
        >导出当前数据</el-button>
        <el-button @click="openAssignMenuForm()">新增分配角色</el-button>
        <el-button @click="resetQuery">取消角色授权</el-button>
        <el-table
          v-loading="rightLoading"
          :data="rightList"
          :stripe="true"
          :show-overflow-tooltip="true"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="40"/>
          <el-table-column label="角色编码" align="center" prop="code" />
          <el-table-column label="角色名称" align="center" prop="name" />
          <el-table-column label="角色描述" align="center" prop="remark" />
          <el-table-column label="角色状态" key="status">
            <template #default="scope">
              <dict-tag :type="DICT_TYPE.SYSTEM_USER_STATUS" :value="scope.row.status" />
            </template>
          </el-table-column>
        </el-table>
        <Pagination
          :total="RightTotal"
          v-model:page="rightQueryParams.pageNo"
          v-model:limit="rightQueryParams.pageSize"
          @pagination="getRightList"
        />
      </ContentWrap>
    </el-col>
    <el-col :span="11" v-else>
      <ContentWrap>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['system:user:export']"
        >导出当前数据</el-button>
        <el-button @click="openAssignMenuForm()">新增数据权限</el-button>
        <el-button @click="resetQuery">取消数据授权</el-button>

        <el-table
          v-loading="rightLoading"
          :data="rightList"
          :stripe="true"
          :show-overflow-tooltip="true"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="40"/>
          <el-table-column label="父级编码" align="center" prop="parentCode" />
          <el-table-column label="父级名称" align="center" prop="parentName" />
          <el-table-column label="组织编码" align="center" prop="code" />
          <el-table-column label="组织名称" align="center" prop="name" />
          <el-table-column label="授予时间" align="center" prop="createTime" />
        </el-table>
        <Pagination
          :total="RightTotal"
          v-model:page="rightQueryParams.pageNo"
          v-model:limit="rightQueryParams.pageSize"
          @pagination="getRightList"
        />
      </ContentWrap>
    </el-col>-->
  </el-row>
  <!-- <el-row v-if="topType == 2">
    <el-col :span="6" :xs="24">
      <ContentWrap class="h-1/1">
        <el-radio-group v-model="roleType" @change="handleTypeChange">
          <el-row>
            <el-col :span="8">
              <el-radio value="1" size="small">按用户</el-radio>
            </el-col>
            <el-col :span="8">
              <el-radio value="2" size="small">按组织</el-radio>
            </el-col>
            <el-col :span="8">
              <el-radio value="3" size="small">按角色</el-radio>
            </el-col>
          </el-row>
        </el-radio-group>
        <el-row style="text-align: right">
          <el-col :span="24">
            <el-button
              size="small"
              type="success"
              plain
              @click="handleExport"
              :loading="exportLoading"
              v-hasPermi="['system:user:export']"
            >导出全部数据</el-button>
          </el-col>
        </el-row>
        <DeptTree @node-click="handleDeptNodeClick" />
      </ContentWrap>
    </el-col>
    <el-col :span="18" :xs="24">
      <ContentWrap>
        <el-form
          class="-mb-15px"
          :model="queryParams"
          ref="queryFormRef"
          :inline="true"
          label-width="68px"
        >
          <el-form-item label="姓名" prop="nickname">
            <el-input
              v-model="queryParams.nickname"
              placeholder="请输入姓名"
              clearable
              @keyup.enter="handleQuery"
              class="!w-240px"
            />
          </el-form-item>
          <el-form-item label="手机号码" prop="mobile">
            <el-input
              v-model="queryParams.mobile"
              placeholder="请输入手机号码"
              clearable
              @keyup.enter="handleQuery"
              class="!w-240px"
            />
          </el-form-item>
          <el-form-item label="工号" prop="mobile">
            <el-input
              v-model="queryParams.mobile"
              placeholder="请输入工号"
              clearable
              @keyup.enter="handleQuery"
              class="!w-240px"
            />
          </el-form-item>
          <el-form-item label="岗位" prop="postName">
            <el-input
              v-model="queryParams.mobile"
              placeholder="请输入岗位"
              clearable
              @keyup.enter="handleQuery"
              class="!w-240px"
            />
          </el-form-item>

          <el-form-item>
            <el-button @click="handleQuery">
              <Icon icon="ep:search" />查询
            </el-button>
            <el-button @click="resetQuery">
              <Icon icon="ep:refresh" />重置
            </el-button>
          </el-form-item>
        </el-form>
      </ContentWrap>
      <ContentWrap>
        <el-row>
          <el-col :span="12">已分配用户</el-col>
          <el-col :span="12" style="text-align: right">
            <el-button
              type="success"
              plain
              @click="handleExport"
              :loading="exportLoading"
              v-hasPermi="['system:user:export']"
            >导出当前数据</el-button>
            <el-button @click="openAddRoleTransfer">新增分配用户</el-button>
            <el-button @click="resetQuery">取消授权</el-button>
          </el-col>
        </el-row>
        <el-table border v-loading="loading" :data="list" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="40"/>
          <el-table-column
            label="用户姓名"
            align="center"
            prop="nickname"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="所属组织"
            align="center"
            key="deptName"
            prop="unitName"
            :show-overflow-tooltip="true"
          />
          <el-table-column label="部门" align="center" prop="deptName" width="120" />
          <el-table-column label="岗位" align="center" prop="postName" width="120" />
          <el-table-column label="用户状态" key="status" />
        </el-table>
        <Pagination
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </ContentWrap>
    </el-col>
  </el-row>-->
  <el-row v-if="topType == 3">
    <el-col :span="24">
      <ContentWrap>
        <el-radio-group v-model="roleType" @change="handleTypeChange">
          <el-radio value="1" size="large">按用户</el-radio>
          <!-- <el-radio value="2" size="large">按组织</el-radio> -->
          <el-radio value="3" size="large">按角色</el-radio>
        </el-radio-group>
        <!-- 搜索工作栏 -->
        <div class="common-card-search flex flex-justify-around">
          <el-form
            class="-mb-15px  common-search-form"
            :model="queryParams"
            ref="queryFormRef"
            :inline="true"
            label-width="68px"
          >
            <el-form-item label="角色编码" prop="code">
              <el-input
                v-model="queryParams.code"
                placeholder="请输入角色编码"
                clearable
                @keyup.enter="handleQuery"
                class="!w-180px"
              />
            </el-form-item>
            <el-form-item label="角色名称" prop="name">
              <el-input
                v-model="queryParams.name"
                placeholder="请输入角色名称"
                clearable
                @keyup.enter="handleQuery"
                class="!w-180px"
              />
            </el-form-item>
            <el-form-item label="角色描述" prop="remark">
              <el-input
                v-model="queryParams.remark"
                placeholder="请输入角色描述"
                clearable
                @keyup.enter="handleQuery"
                class="!w-180px"
              />
            </el-form-item>
            <el-form-item label="状态" prop="status">
              <el-select v-model="queryParams.status" placeholder="用户状态" clearable class="!w-180px">
                <el-option
                  v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <!-- <el-form-item>
              <el-button @click="handleQuery">
                <Icon icon="ep:search" />查询
              </el-button>
              <el-button @click="resetQuery">
                <Icon icon="ep:refresh" />重置
              </el-button>
              <el-button
                type="success"
                plain
                @click="handleExport"
                :loading="exportLoading"
                v-hasPermi="['system:user:export']"
              >
                <Icon icon="ep:download" />导出
              </el-button>
            </el-form-item> -->
          </el-form>
          <div class="right-search-btn">
            <el-button  type="primary"  @click="handleQuery">
                <Icon icon="ep:search" class="mr-5px" />搜索
              </el-button>
              <el-button @click="resetQuery">
                <Icon icon="ep:refresh" class="mr-5px" />重置
              </el-button>
          </div>
        </div>
      </ContentWrap>

      <!-- 列表 -->
      <ContentWrap>
        <div class="button_margin15">
            <el-button
              plain
              @click="handleExport"
              :loading="exportLoading"
              v-hasPermi="['system:user:export']"
            >
              <Icon icon="ep:download" />导出
            </el-button>
          </div>
        <el-table border v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
          <el-table-column label="序号" width="60" align="center">
            <template
              #default="{ $index }"
            >{{ (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1 }}</template>
          </el-table-column>
          <el-table-column align="center" label="角色编号" prop="id" />
          <el-table-column align="center" label="角色名称" prop="name" />
          <el-table-column align="center" label="角色编码" prop="code" />
          <el-table-column label="角色类型" align="center" prop="type">
            <template #default="scope">
              <dict-tag :type="DICT_TYPE.SYSTEM_ROLE_TYPE" :value="scope.row.type" />
            </template>
          </el-table-column>
          <el-table-column align="center" label="描述" prop="remark" />
          <el-table-column label="角色状态" key="status" width="80"  align="center">
            <template #default="scope">
              <dict-tag :type="DICT_TYPE.SYSTEM_USER_STATUS" :value="scope.row.status" />
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center">
            <template #default="scope">
              <el-button
                link
                type="primary"
                size="small"
                @click="showRightModal('authorized', scope.row.id)"
                v-hasPermi="['system:user-permission:update']"
              >已授权用户</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页 -->
        <Pagination
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </ContentWrap>
    </el-col>
    <!-- <el-col :span="12">
      <ContentWrap>
        <el-form
          class="-mb-15px"
          :model="rightQueryParams"
          ref="queryRightFormRef"
          :inline="true"
          label-width="68px"
        >
          <el-form-item label="用户姓名" prop="nickname">
            <el-input
              v-model="rightQueryParams.nickname"
              placeholder="请输入用户姓名"
              clearable
              @keyup.enter="handleRightQuery"
              class="!w-180px"
            />
          </el-form-item>
          <el-form-item label="手机号" prop="mobile">
            <el-input
              v-model="rightQueryParams.mobile"
              placeholder="请输入手机号"
              clearable
              @keyup.enter="handleRightQuery"
              class="!w-180px"
            />
          </el-form-item>

          <el-form-item>
            <el-button @click="handleRightQuery">
              <Icon icon="ep:search" class="mr-5px" />查询
            </el-button>
            <el-button @click="resetRightQuery">
              <Icon icon="ep:refresh" class="mr-5px" />重置
            </el-button>
          </el-form-item>
        </el-form>
      </ContentWrap>

      <ContentWrap>
        <el-row>
          <el-col :span="8">已分配用户</el-col>
          <el-col :span="16" style="text-align: right">
            <el-button
              type="success"
              plain
              @click="handleExport"
              :loading="exportLoading"
              v-hasPermi="['system:user:export']"
            >导出当前数据</el-button>
            <el-button @click="openAddRoleTransfer">新增分配用户</el-button>
            <el-button @click="resetQuery">取消授权</el-button>
          </el-col>
        </el-row>
        <el-table
          v-loading="rightLoading"
          :data="rightList"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="40"/>
          <el-table-column
            label="用户姓名"
            align="center"
            prop="nickname"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="所属组织"
            align="center"
            key="deptName"
            prop="unitName"
            :show-overflow-tooltip="true"
          />
          <el-table-column label="部门" align="center" prop="deptName" width="120" />
          <el-table-column label="用户状态" key="status">
            <template #default="scope">
              <dict-tag :type="DICT_TYPE.SYSTEM_USER_STATUS" :value="scope.row.status" />
            </template>
          </el-table-column>
        </el-table>
        <Pagination
          :total="RightTotal"
          v-model:page="rightQueryParams.pageNo"
          v-model:limit="rightQueryParams.pageSize"
          @pagination="getRightList"
        />
      </ContentWrap>
    </el-col>-->
  </el-row>

  <!-- 表单弹窗：添加/修改 -->
  <UserPermissionForm ref="formRef" @success="getList" />
  <!-- 按用户维度 新增分配角色弹窗 -->
  <RoleAssignModal ref="roleAssignModal" @success="getList" />
  <!-- 按组织和按角色维度 新增分配用户 -->
  <AddRoleTransfer ref="addRoleTransfer" @success="getList" />
  <!-- 按用户维度 角色权限弹窗-->
  <AddRolePermissionByUser ref="addRolePermissionByUser" />
  <!-- 按用户维度 数据权限弹窗-->
  <AddDataPermissionByUser ref="addDataPermissionByUser" />
  <!-- 按角色维度  已授权用户 -->
  <AddUserByRole ref="addUserByRole" />
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { UserPermissionApi, UserPermissionVO } from '@/api/system/userpermission'
import UserPermissionForm from './UserPermissionForm.vue'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import DeptTree from '@/views/system/user/DeptTree.vue'
import * as UserApi from '@/api/system/user'
import RoleAssignModal from '@/views/system/userpermission/RoleAssignModal.vue'
import * as RoleApi from '@/api/system/role'
import AddRoleTransfer from '@/views/system/userpermission/AddRoleTransfer.vue'
import AddRolePermissionByUser from './child/AddRolePermissionByUser.vue'
import AddDataPermissionByUser from './child/AddDataPermissionByUser.vue'
import AddUserByRole from './child/AddUserByRole.vue'
/** 用户数据权限 列表 */
defineOptions({ name: 'UserPermission' })
interface User {
  date: string
  name: string
  address: string
}
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<UserPermissionVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  userId: undefined,
  nickname: undefined,
  mobile: undefined,
  qdJobNo: undefined,
  identNo: undefined,
  status: undefined,
  id: undefined,
  remark: undefined,
  holdDeptId: undefined,
  createTime: [],
  deptId: null,
  name: undefined
})
const queryFormRef = ref() // 搜索的表单
const queryRightFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const roleType = ref('1')
const apiMethods = {
  1: UserApi.getUserPage,
  2: UserPermissionApi.getUserPageByDeptPermission,
  3: RoleApi.getRolePage
}
/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const apiMethod = apiMethods[topType.value]
    const data = (await apiMethod(queryParams)) || []
    list.value = data.list || []
    total.value = data.total
  } finally {
    loading.value = false
  }
}

const rightLoading = ref(false)
const rightList = ref([]) // 列表的数据
const RightTotal = ref(0)
const rightQueryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  userId: -1,
  roleId: -1,
  nickname: undefined,
  mobile: undefined,
  identNo: undefined,
  status: undefined,
  id: undefined,
  remark: undefined,
  holdDeptId: undefined,
  createTime: [],
  deptId: -1
})
const rightType = ref('role')
const addRolePermissionByUser = ref()
const addDataPermissionByUser = ref()
const addUserByRole = ref()
const showRightModal = (type: String, id: number) => {
  rightType.value = type
  if (type == 'authorized') {
    rightQueryParams.roleId = id
    addUserByRole.value.open(id)
  } else if (type == 'role') {
    addRolePermissionByUser.value.open(id)
  } else if (type == 'data') {
    addDataPermissionByUser.value.open(id)
  } else {
    rightQueryParams.userId = id
  }
  // getRightList()
}
const rightApiMethods = {
  data: UserPermissionApi.getDeptPermissionByUserId,
  role: UserPermissionApi.getRolePermissionByUserId,
  authorized: UserPermissionApi.getUserPageByRolePermission
}
const getRightList = async () => {
  rightLoading.value = true
  try {
    const apiMethod = rightApiMethods[rightType.value]
    const data = (await apiMethod(rightQueryParams)) || []
    rightList.value = data.list || []
    RightTotal.value = data.total
  } finally {
    rightLoading.value = false
  }
}
const handleDeptNodeClick = async (row) => {
  queryParams.deptId = row.id
  await getList()
}
const multipleSelection = ref<User[]>([])
const handleSelectionChange = (val: User[]) => {
  multipleSelection.value = val
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}
const handleRightQuery = () => {
  rightQueryParams.pageNo = 1
  getRightList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

const resetRightQuery = () => {
  queryRightFormRef.value.resetFields()
  handleRightQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}
const roleAssignModal = ref()
const openAssignMenuForm = async () => {
  roleAssignModal.value.open()
}
const addRoleTransfer = ref()
const openAddRoleTransfer = async () => {
  addRoleTransfer.value.open()
}
/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await UserPermissionApi.deleteUserPermission(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await UserPermissionApi.exportUserPermission(queryParams)
    download.excel(data, '用户数据权限.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}
const topType = ref(1)
const handleTypeChange = (value: string | number | boolean) => {
  topType.value = value
  getList()
}
/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
