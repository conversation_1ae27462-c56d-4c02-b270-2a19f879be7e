<!--
* @Author: li<PERSON><PERSON>g
* @Date: 2024-10-22 17:10:50
* @Description: 工程结算-审计费=>
-->
<template>
	<ContentWrap>
		<!-- 搜索工作栏 -->
		<el-form class="-mb-15px" :model="queryParams" ref="queryFormRef" :inline="true" label-width="100px">
			<el-form-item label="投资单位" prop="investmentUnit">
				<el-input v-model="queryParams.investmentUnit" placeholder="请输入投资单位" clearable @keyup.enter="handleQuery"
					class="!w-200px" />
			</el-form-item>
			<el-form-item label="管理单位" prop="managementUnit">
				<el-input v-model="queryParams.managementUnit" placeholder="请输入管理单位" clearable @keyup.enter="handleQuery"
					class="!w-200px" />
			</el-form-item>
			<el-form-item label="中介机构" prop="auditUnit">
				<el-input v-model="queryParams.auditUnit" placeholder="请输入审计单位" clearable @keyup.enter="handleQuery"
					class="!w-200px" />
			</el-form-item>
			<el-form-item label="施工单位" prop="constructionUnit">
				<el-input v-model="queryParams.constructionUnit" placeholder="请输入施工单位" clearable @keyup.enter="handleQuery"
					class="!w-200px" />
			</el-form-item>
			<el-form-item label="合同名称" prop="contractName">
				<el-input v-model="queryParams.contractName" placeholder="请输入合同名称" clearable @keyup.enter="handleQuery"
					class="!w-200px" />
			</el-form-item>
			<el-form-item label="审计序列" prop="auditSequence">
				<el-select v-model="queryParams.auditSequence" placeholder="请选择审计序列" clearable class="!w-200px">
					<el-option v-for="dict in getIntDictOptions('audit_sequences')" :key="dict.value"
						:label="dict.label" :value="dict.value" />
				</el-select>
			</el-form-item>
			<el-form-item label="日期" prop="publishDate">
				<el-date-picker v-model="queryParams.publishDate" type="daterange" unlink-panels range-separator="-"
					start-placeholder="开始" end-placeholder="结束" value-format="YYYY-MM-DD" clearable @change="changeDate" />
			</el-form-item>
			<el-form-item>
				<el-button type="primary" @click="handleQuery">
					<Icon icon="ep:search" class="mr-5px" />搜索
				</el-button>
				<el-button @click="resetQuery">
					<Icon icon="ep:refresh" class="mr-5px" />重置
				</el-button>
			</el-form-item>
		</el-form>
	</ContentWrap>

	<!-- 列表 -->
	<ContentWrap>
		<el-button :loading="exportLoading" plain @click="handleExport">
			<Icon class="mr-5px" icon="ep:download" />
			导出
		</el-button>
		<el-tabs v-model="activeName" class="demo-tabs" @tab-change="handleClick">
			<el-tab-pane label="投资单位（甲方）" name="1" />
			<el-tab-pane label="管理单位" name="2" />
			<el-tab-pane label="中介机构" name="3" />
			<el-tab-pane label="施工单位（乙方）" name="4" />
		</el-tabs>
		<el-table border v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true" show-summary :summary-method="getSummaries">
			<el-table-column label="序号" width="60" align="center">
				<template #default="{ $index }">{{ (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1
					}}</template>
			</el-table-column>
			<el-table-column :label="activeNameList" align="left" prop="investmentUnit" min-width="260">
				<template #default="scope">
					<span class="click-pointer" @click="openDetailForm(scope.row.id)" >{{ scope.row.investmentUnit }}</span>
				</template>
			</el-table-column>
			<el-table-column label="审定结算数量(项)" align="center" prop="settlementCount" min-width="150" />
			<el-table-column label="报审结算总金额(万元)" align="center" prop="reportedAmount" min-width="150" />
			<el-table-column label="审定结算总金额(万元)" align="center" prop="approvedAmount" min-width="150" />
			<el-table-column label="累计审减值(万元)" align="center" prop="reductionValue" min-width="150" />
			<el-table-column label="平均审减率" align="center" prop="avgReductionRate" min-width="120">
				<template #default="scope">
					{{scope.row.avgReductionRate ?scope.row.avgReductionRate + '%' : '0%'}}
				</template>
			</el-table-column>
			<el-table-column label="甲方应付审计费(元)" align="center" prop="partyAAuditFee" min-width="150" />
			<el-table-column label="乙方应付审计费(元)" align="center" prop="partyBAuditFee" min-width="150" />
		</el-table>
		<!-- 分页 -->
		<Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
			@pagination="getList" />
	</ContentWrap>
	<!-- 单位详情弹窗 -->
	<Detail ref="detailRef" />
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { AuditFeeVO, AuditFeeApi } from '@/api/decision/engineering/projectSettlement/auditFee'
import { reformingStandingBookApi } from '@/api/auditManagement/auditRectifica/reformingStandingBook'
import Detail from './Detail.vue'
import download from '@/utils/download'
import { h } from 'vue'
import type { VNode } from 'vue'
import type { TableColumnCtx } from 'element-plus'
defineOptions({ name: 'AuditFee' })
const message = useMessage() // 消息弹窗
const loading = ref(true) // 列表的加载中
const list = ref<AuditFeeVO[]>([]) // 列表的数据
const queryParams = reactive({
	pageSize: 10,
	pageNo: 1,
	investmentUnit: undefined,
	managementUnit: undefined,
	constructionUnit: undefined,
	auditUnit: undefined,
	contractName: undefined,
	auditSequence: undefined,
	startDate: undefined,
	endDate: undefined,
	fessType: '1',
})
const queryFormRef = ref() // 搜索的表单
const total = ref(0)
const activeName = ref('1')
const activeNameList = ref('投资单位')
const handleClick = async (type) => {
	queryParams.fessType = type
	if(type == 1){
		activeNameList.value = '投资单位'
	}else if(type == 2){
		activeNameList.value = '管理单位'
	}else if(type == 3){
		activeNameList.value = '中介机构'
	}else if(type == 4){
		activeNameList.value = '施工单位'
	}
	await getList()
}

// 时间选择触发事件
const changeDate = async(data) => {
	queryParams.startDate = data[0]
	queryParams.endDate = data[1]
}

/** 查询列表 */
const getList = async () => {
	loading.value = true
	try {
		const data = await AuditFeeApi.getAuditFeeList(queryParams)
		list.value = data.list
		total.value = data.total
		await getTotal()
	} finally {
		loading.value = false
	}
}

// 查询合计数据
const dataTotal = ref()
const getTotal = async () => {
	loading.value = true
	try {
		dataTotal.value = await AuditFeeApi.getAuditFeeTotal(queryParams)
	} finally {
		loading.value = false
	}
}

interface SummaryMethodProps<T = AuditFeeVO> {
  columns: TableColumnCtx<T>[]
  data: T[]
}
// 表尾合计显示
const getSummaries = (param: SummaryMethodProps) => {
  const { columns, data } = param
  const sums: (string | VNode)[] = []
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = h('div', { style: { textDecoration: 'underline' } }, [
        '合计',
      ])
      return
    }
    const values = data.map((item) => Number(item[column.property]))
    if (!values.every((value) => Number.isNaN(value)) &&  dataTotal.value) {
	  sums[2] = dataTotal.value.settlementCount
	  sums[3] = dataTotal.value.reportedAmountCount
	  sums[4] = dataTotal.value. approvedAmountCount
	  sums[5] = dataTotal.value.reductionValueCount
	  sums[6] = dataTotal.value. avgReductionRateCount?(dataTotal.value. avgReductionRateCount + '%'):'0%'
	  sums[7] = dataTotal.value.partyAAuditFeeCount
	  sums[8] = dataTotal.value. partyBAuditFeeCount
    } else {
      sums[index] = ''
    }
  })
  return sums
}

/** 搜索按钮操作 */
const handleQuery = () => {
	queryParams.pageNo = 1
	getList()
}
/** 重置按钮操作 */
const resetQuery = () => {
	queryFormRef.value.resetFields()
	queryParams.startDate = undefined,
	queryParams.endDate = undefined,
	handleQuery()
}
const exportLoading = ref(false) // 导出的加载中
/** 导出按钮操作 */
const handleExport = async () => {
	try {
		// 导出的二次确认
		await message.exportConfirm()
		// 发起导出
		exportLoading.value = true
		const data = await AuditFeeApi.exportAuditFee(queryParams)
		download.excel(data, '工程结算-审计费.xls')
	} catch {
	} finally {
		exportLoading.value = false
	}
}
/** 点击单位进详情 */
const detailRef = ref()
const openDetailForm = (id?:number) => {
	console.log(queryParams.fessType)
	detailRef.value.open(id, queryParams.fessType)
}
/** 初始化 **/
onMounted(() => {
	getList()
})
</script>
