<!--
* @Author: li<PERSON><PERSON>g
* @Date: 2024-10-23 10:30:58
* @Description: 一审单位详情列表=>
-->
<template>
	<Dialog v-model="dialogVisible" :loading="loading" :scroll="true" title="一审单位详情列表" width="75%">
		<el-button :loading="exportLoading" plain @click="handleExport">
			<Icon class="mr-5px" icon="ep:download" />
			导出
		</el-button>
		<el-table ref="tableRef" border :data="detailData" :stripe="true" :show-overflow-tooltip="true" :span-method="arraySpanMethod" show-summary :summary-method="getSummaries" >
			<el-table-column label="序号" type="index" width="60" align="center"/>
			<el-table-column label="工程名称" align="left" prop="engineeringName" min-width="260"/>
			<el-table-column label="工程概况" align="left" prop="engineeringOverview" min-width="240"/>
			<el-table-column label="审计单位" prop="auditorName" align="left" min-width="260" />
			<el-table-column label="投资单位" align="left" prop="investorName" min-width="260" />
			<el-table-column label="管理单位" align="left" prop="managerName" min-width="260" />
			<el-table-column label="施工单位" align="left" prop="constructorName" min-width="260" />
			<el-table-column label="初审意见耗时(天)" align="center" prop="initialAuditDuration" min-width="160" />
			<el-table-column label="终审意见耗时(天)" align="center" prop="finalAuditDuration" min-width="160" />
			<el-table-column label="纸版审计报告耗时(天)" align="center" prop="reportDuration" min-width="180" />、
		</el-table>
		<!-- 分页 -->
		<Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
			@pagination="getList" />
	</Dialog>
</template>

<script lang="ts" setup>
import { AuditEfficiencyDetailsVO, AuditEfficiencyApi } from '@/api/decision/engineering/projectSettlement/auditEfficiency'
import download from '@/utils/download'
import { h } from 'vue'
import type { VNode } from 'vue'
import type { TableColumnCtx } from 'element-plus'
import { mergeCells } from '@/utils/mergeCells'
defineOptions({ name: 'Detail' })

const queryParams = reactive({
	pageSize: 10,
	pageNo: 1,
	id: undefined,
	enType: undefined,
})
const dialogVisible = ref(false) // 弹窗的是否展示
const loading = ref(true) // 列表的加载中
const detailData = ref<AuditEfficiencyDetailsVO[]>([]) // 详情数据
const total = ref(0)
const message = useMessage() // 消息弹窗

/** 打开弹窗 */
const open = async (id?: number, type: string) => {
	queryParams.id = id
	queryParams.enType = type
	dialogVisible.value = true
	await getList()
	await getTotal()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

// 数据详情列表
const getList = async() => {
	// 设置数据
	loading.value = true
	try {
		const data = await AuditEfficiencyApi.getAuditEfficiencyDetails(queryParams)
		detailData.value = data.list
		total.value = data.total

	} finally {
		loading.value = false
	}
}

// 查询合计数据
const dataTotal = ref()
const getTotal = async () => {
	loading.value = true
	try {
		dataTotal.value = await AuditEfficiencyApi.getAuditEfficiencyDetailsTotal(queryParams)
	} finally {
		loading.value = false
	}
}

// 合并表尾合计行单元格的方法
const tableRef = ref()
const arraySpanMethod = ({ row, column, rowIndex, columnIndex }:any)=> {
	mergeCells(tableRef, 1, 6)
}

interface SummaryMethodProps<T = AuditEfficiencyVO> {
  columns: TableColumnCtx<T>[]
  data: T[]
}
// 表尾合计显示
const getSummaries = (param: SummaryMethodProps) => {
  const { columns, data } = param
  const sums: (string | VNode)[] = []
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = ''
      return
    }
    const values = data.map((item) => Number(item[column.property]))
    if (!values.every((value) => Number.isNaN(value)) &&  dataTotal.value) {
	  sums[7] = dataTotal.value.initialAuditDurationTotal
	  sums[8] = dataTotal.value.finalAuditDurationTotal
	  sums[9] = dataTotal.value. reportDurationTotal
    } else {
      sums[index] = h('div', { style: { textDecoration: 'underline' } }, [
        '平均值',
      ])
    }
  })
  return sums
}

/** 导出按钮操作 */
const exportLoading = ref(false) // 导出的加载中
const handleExport = async () => {
	try {
		// 导出的二次确认
		await message.exportConfirm()
		// 发起导出
		exportLoading.value = true
		const data = await AuditEfficiencyApi.exportAuditEfficiencyDetails(queryParams)
		download.excel(data, '一审单位详情.xls')
	} catch {
	} finally {
		exportLoading.value = false
	}
}
</script>
