<script lang="ts" setup>
import * as resourceApi from "@/api/model/resource";
import {ElButton, ElTree} from "element-plus";
import CatalogForm from "@/views/model/resource/directory/components/CatalogForm.vue";
import DataForm from "./components/DataForm.vue"
import ResourceTableDetail from "@/views/model/resource/directory/components/ResourceTableDetail.vue"
import {DICT_TYPE, getStrDictOptions} from "@/utils/dict";

onMounted(() => {
  getResourceTree()
  getPageList()
})


const loading = ref(true)
const list = ref([])

const getResourceTree = async () => {
  loading.value = true
  try {
    list.value = await resourceApi.getResourceTree();
  } finally {
    loading.value = false
  }
}
const defaultProps = {
  children: 'children',
  label: 'label',
}

const formRef = ref()
const openForm = (type: string, data?: object) => {
  formRef.value.open(type, data)
}

const message = useMessage() // 消息弹窗
// 删除目录树节点
const handleDelete = async (id: string) => {
  await message.delConfirm()
  try {
    await resourceApi.deleteResourceCatalog(id);
    await getResourceTree();
    message.success('删除成功')
  } catch {
  }
}

const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  tableNameCn: '',
  tableNameEn: '',
  catalogId:'',
  sourceSystem: '',
  extractMethod: '',
  tableType: ''
})
const queryFormRef = ref() // 查询表单
/** 查询按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1;
  getPageList();
}

/** 重置查询条件 **/
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}


const tableLoading = ref(true)
const total = ref(0) // 列表的总页数
const tableList = ref([])
/** 查询列表 */
const getPageList = async () => {
  try {
    tableLoading.value = true;
    const res = await resourceApi.getResourceCatalogData(queryParams);
    tableList.value = res.list;
    total.value = res.total;
  } finally {
    tableLoading.value = false;
  }
}

const handleNodeClick = async (data: any) =>{
  queryParams.catalogId = data.id
  await getPageList()
}

const dataFormRef = ref()
const openDataForm = (type: string, id?: string) => {
  dataFormRef.value.open(type, id)
}

// 删除资源
const handleDeleteResource = async (id: number) => {
  await message.delConfirm()
  try {
    await resourceApi.deleteResourceTable(id);
    await getPageList();
    message.success('删除成功')
  } catch {
  }
}

const levelFil = (level: number) => {
  const list = ['clarity:layers-solid','oui:layers','mynaui:layers-two','fluent:organization-48-regular','proicons:branch']
  return list[level - 1]
}

const resourceTableDetail = ref()
const openTableDetail = (data: object) => {
  resourceTableDetail.value.open(data)
}

</script>

<template>
  <el-row :gutter="10">
    <el-col :span="5">
      <ContentWrap class="h-1/1">
        <el-scrollbar height="calc(100vh - 90px)">
          <el-tree
            style="max-width: 600px"
            :data="list"
            :props="defaultProps"
            :default-expand-all="true"
            :expand-on-click-node="false"
            @node-click="handleNodeClick"
            node-key="id"
            :indent="6"
          >
            <template #default="{ node, data }">
              <div style="display: flex;">
                <div class="custom-tree-node" @click="handleNodeClick(data)">
                  <Icon :size="18" :icon="levelFil(node.level)" style="margin: 0 5px;"/>
                  <i class="custom-iconguanbi-quxiao-guanbi"></i>
                  {{ node.label }}
                </div>
                <div class="but_right">
                  <el-col :span="8" class="cursor-pointer">
                    <Icon icon="carbon:add-filled" :size="20" @click="openForm('create', data)" />
                  </el-col>
                  <el-col :span="8" class="cursor-pointer">
                    <Icon icon="iconoir:edit" :size="20" @click="openForm('update', data)" v-if="data.id !== '1'" />
                  </el-col>
                  <el-col :span="8" class="cursor-pointer">
                    <Icon icon="weui:delete-outlined" :size="20" @click="handleDelete(data.id)" v-if="data.id !== '1' && !(data?.children && data.children.length > 0)" />
                  </el-col>
                </div>
              </div>
            </template>
          </el-tree>
        </el-scrollbar>
      </ContentWrap>
    </el-col>
    <el-col :span="19">
      <ContentWrap class="common-card-search">
        <el-form :model="queryParams" ref="queryFormRef" class="-mb-15px common-search-form" label-width="60px">
          <el-row :gutter="10">
            <el-col :span="8">
              <el-form-item label="中文表名" prop="tableNameCn">
                <el-input v-model="queryParams.tableNameCn" placeholder="请输入中文表名" style="width: 100%;" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="英文表名" prop="tableNameEn">
                <el-input v-model="queryParams.tableNameEn" placeholder="请输入英文表名" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="来源系统" prop="sourceSystem">
                <el-select v-model="queryParams.sourceSystem" placeholder="请选择来源系统" style="width: 100%;" clearable>
                  <el-option v-for="dict in getStrDictOptions(DICT_TYPE.MODEL_SOURCE_SYSTEM)" :key="dict.value" :value="dict.value" :label="dict.label" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="8">
              <el-form-item label="取数方式" prop="extractMethod">
                <el-select v-model="queryParams.extractMethod" placeholder="请选择取数方式" style="width: 100%;" clearable>
                  <el-option v-for="dict in getStrDictOptions(DICT_TYPE.MODEL_DATA_EXTRACT_METHOD)" :key="dict.value" :value="dict.value" :label="dict.label" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="表类型 " prop="tableType">
                <el-select v-model="queryParams.tableType" placeholder="请选择表类型" style="width: 100%;" clearable>
                  <el-option v-for="dict in getStrDictOptions(DICT_TYPE.MODEL_TABLE_TYPE)" :key="dict.value" :value="dict.value" :label="dict.label" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div class="right-search-btn">
          <el-button  type="primary"  @click="handleQuery">
              <Icon icon="ep:search" class="mr-5px" />搜索
            </el-button>
            <el-button @click="resetQuery">
              <Icon icon="ep:refresh" class="mr-5px" />重置
            </el-button>
        </div>
      </ContentWrap>
      <ContentWrap>
        <el-button type="primary" size="default" plain @click="openDataForm('create','')">新增</el-button>
        <el-table border stripe v-loading="tableLoading" :data="tableList">
          <el-table-column label="序号" align="center" width="60">
            <template #default="{ $index }">
              {{ (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1 }}
            </template>
          </el-table-column>
          <el-table-column label="中文表名" prop="tableNameCn" align="left" width="240"/>
          <el-table-column label="英文表名" prop="tableNameEn" align="left" width="320"/>
          <el-table-column label="来源系统" prop="sourceSystem" align="center" width="100">
            <template #default="scope">
              <dict-tag :type="DICT_TYPE.MODEL_SOURCE_SYSTEM" :value="scope.row.sourceSystem" />
            </template>
          </el-table-column>
          <el-table-column label="需求提出人" prop="proposerUserName" align="left" width="100"/>
          <el-table-column label="关键字" prop="keyWords" align="left" min-width="300" show-overflow-tooltip/>
          <el-table-column label="操作" fixed="right" align="center" width="160">
            <template #default="scope">
              <el-button link type="primary" @click="openTableDetail(scope.row)">查看</el-button>
              <el-button link type="primary" @click="openDataForm('update',scope.row.id)">修改</el-button>
              <el-button link type="danger" @click="handleDeleteResource(scope.row.id)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页组件 -->
        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="getPageList"
        />
      </ContentWrap>
    </el-col>
  </el-row>

  <CatalogForm ref="formRef" @success="getResourceTree"/>

  <DataForm ref="dataFormRef" @success="getPageList"/>

  <ResourceTableDetail ref="resourceTableDetail"/>

</template>

<style scoped>
.el-tree-node__content {
  position: relative;
}
.but_right {
  display: none;
  position: absolute;
  right: 0;
  z-index: 999;
}
.el-tree-node__content:hover .but_right{
  display: flex;
}
.cursor-pointer {
  padding-right: 4px !important;
  padding-left: 4px !important;
}

::v-deep(.el-table .el-table__header .el-table__cell) {
  text-align: center;
}
</style>
