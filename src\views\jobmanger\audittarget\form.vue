<!--
* @Author: lijunliang
* @Date: 2024-09-12 17:31:32
* @Description: 审计对象库表单=>
-->
<template>
	<Dialog :title="dialogTitle" :scroll="true" v-model="dialogVisible" width="75%">
		<el-form ref="formRef"  class="common-submit-form" :model="formData" :rules="formRules" v-loading="formLoading">
			<el-row>
				<!-- <el-col :span="8"> -->
					<el-form-item label="公司名称" prop="companyName" label-width="100px">
						<el-input v-model="formData.companyName" placeholder="请输入公司名称" readonly class="!w-240px" />
					</el-form-item>
				<!-- </el-col> -->
				<!-- <el-col :span="8"> -->
					<el-form-item label="公司法人" prop="legalPerson" label-width="100px">
						<el-input v-model="formData.legalPerson" placeholder="请输入公司法人" class="!w-240px" />
					</el-form-item>
				<!-- </el-col> -->
				<!-- <el-col :span="8"> -->
					<el-form-item label="公司类别" prop="companyType" label-width="100px">
						<!-- <el-select v-model="formData.companyType" placeholder="请选择公司类别" clearable class="!w-240px">
							<el-option v-for="dict in getIntDictOptions(DICT_TYPE.AUDIT_TYPE_STATUS)" :key="dict.value"
								:label="dict.label" :value="dict.value" />
						</el-select> -->
						<el-input v-model="formData.companyType" placeholder="请输入公司类别" class="!w-240px" />
					</el-form-item>
				<!-- </el-col> -->
				<!-- <el-col :span="8"> -->
					<el-form-item label="公司性质" prop="comNature" label-width="100px">
						<el-select v-model="formData.comNature" placeholder="请选择公司性质" clearable class="!w-240px">
							<el-option v-for="dict in getIntDictOptions('audit_tank_object_company')" :key="dict.value"
								:label="dict.label" :value="dict.value" />
						</el-select>
					</el-form-item>
				<!-- </el-col> -->
				<!-- <el-col :span="8"> -->
					<el-form-item label="注册资金(万元)" prop="registerCapital" label-width="100px">
						<el-input v-model="formData.registerCapital" placeholder="请输入注册资金" class="!w-240px" />
					</el-form-item>
				<!-- </el-col> -->
				<!-- <el-col :span="8"> -->
					<el-form-item label="注册地址" prop="address" label-width="100px">
						<el-input v-model="formData.address" placeholder="请输入注册地址" class="!w-240px" />
					</el-form-item>
				<!-- </el-col> -->
				<!-- <el-col :span="8"> -->
					<el-form-item label="注册日期" prop="registerTime" label-width="100px">
						<el-date-picker v-model="formData.registerTime" type="date" value-format="x" format="YYYY-MM-DD" clearable placeholder="选择某一天"
							class="!w-240px" />
					</el-form-item>
				<!-- </el-col> -->
				<!-- <el-col :span="8"> -->
					<el-form-item label="联系电话" prop="linkTel" label-width="100px">
						<el-input v-model="formData.linkTel" placeholder="请输入联系电话" class="!w-240px" />
					</el-form-item>
				<!-- </el-col> -->
				<!-- <el-col :span="8"> -->
					<el-form-item label="董事长" prop="chairmanName" label-width="100px">
						<div style="display: flex;">
							<el-input v-model="formData.chairmanName" @click="handleCheckPerson('president')" placeholder="请点击选择" readonly class="!w-240px" />
							<!-- <el-button type="primary" @click="handleCheckPerson('president')">选择</el-button> -->
						</div>
					</el-form-item>
				<!-- </el-col> -->
				<!-- <el-col :span="8"> -->
					<el-form-item label="总经理" prop="managerName" label-width="100px">
						<!-- <el-input v-model="formData.managerName" placeholder="请选择" readonly class="!w-145px mr-13px" /> -->
						<el-input v-model="formData.managerName" placeholder="请点击选择" readonly @click="handleCheckPerson('manager')" class="!w-240px" />
						<!-- <el-button type="primary" @click="handleCheckPerson('manager')">选择</el-button> -->
					</el-form-item>
				<!-- </el-col> -->
				<!-- <el-col :span="16"> -->
					<el-form-item label="公司领导" prop="leaders" label-width="100px">
						<el-input v-model="formData.leaders" placeholder="请输入公司领导" class="!w-240px" />
					</el-form-item>
				<!-- </el-col> -->
			</el-row>
			<el-form-item label="基本情况" prop="basicInfo">
				<el-input v-model="formData.basicInfo" type="textarea" placeholder="请输入" :autosize="{minRows: 3,maxRows: 3}" />
			</el-form-item>
			<el-form-item label="营业执照范围" prop="licenseScope">
				<el-input v-model="formData.licenseScope" type="textarea" placeholder="请输入" :autosize="{minRows: 3,maxRows: 3}" />
			</el-form-item>
			<el-form-item label="主要开展业务情况" prop="mainBusi">
				<el-input v-model="formData.mainBusi" type="textarea" placeholder="请输入" :autosize="{minRows: 3,maxRows: 3}" />
			</el-form-item>
			<el-form-item label="战略定位" prop="strategy">
				<el-input v-model="formData.strategy" type="textarea" placeholder="请输入" :autosize="{minRows: 3,maxRows: 3}" />
			</el-form-item>
			<el-form-item label="主责主业" prop="mainResp">
				<el-input v-model="formData.mainResp" type="textarea" placeholder="请输入" :autosize="{minRows: 3,maxRows: 3}" />
			</el-form-item>
			<el-form-item label="专精特新" prop="masteryNew">
				<el-input v-model="formData.masteryNew" type="textarea" placeholder="请输入" :autosize="{minRows: 3,maxRows: 3}" />
			</el-form-item>
			<el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
				<el-tab-pane label="历任领导" name="0">
					<div>
						<el-button type="primary" plain @click="openWbList('create')"
							v-hasPermi="['system:audit-role:create']">
							<Icon icon="ep:plus" class="mr-5px" />新增
						</el-button>
					</div>
					<el-table :data="formData.tankObjLeaderHisDOList" border :stripe="true" :show-overflow-tooltip="true">
						<el-table-column type="index" label="序号" width="60" align="center"/>
						<el-table-column label="姓名" align="left" prop="leaderName" min-width="180" />
						<el-table-column label="职务" align="center" prop="duties" min-width="100"/>
						<el-table-column label="任职开始日期" align="center" prop="startDate" min-width="120">
							<template #default="scope">
								{{ formatDate(scope.row.startDate, 'YYYY-MM-DD') }}
							</template>
						</el-table-column>
						<el-table-column label="任职结束日期" align="center" prop="endDate" min-width="120">
							<template #default="scope">
								{{ formatDate(scope.row.endDate, 'YYYY-MM-DD') }}
							</template>
						</el-table-column>
						<el-table-column label="主要责任" align="left" prop="responsibilities" min-width="180"/>
						<el-table-column label="资金情况" align="center" prop="fundSituation" min-width="120"/>
						<el-table-column label="经营情况" align="center" prop="busiSituation" min-width="120"/>
						<el-table-column label="任期内主要工作" align="left" prop="mainJob" min-width="180"/>
						<el-table-column label="操作" align="center" width="150">
							<template #default="scope">
								<el-button link type="primary" @click="openWbList('update', scope.row)">编辑</el-button>
								<el-button link type="danger" @click="handleWbDelete(scope.row)"
									v-hasPermi="['system:audit-role:delete']">删除</el-button>
							</template>
						</el-table-column>
					</el-table>
				</el-tab-pane>
				<el-tab-pane label="财务摘要" name="1">
					<div>
						<el-button type="primary" plain @click="openNbList('create')"
							v-hasPermi="['system:audit-role:create']">
							<Icon icon="ep:plus" class="mr-5px" />新增
						</el-button>
					</div>
					<el-table :data="formData.tankObjFinancialSumDOList" border :stripe="true" :show-overflow-tooltip="true">
						<el-table-column type="index" label="序号" width="60" align="center"/>
						<el-table-column label="年度" align="center" prop="sumYear" min-width="120">
							<template #default="scope">
								<el-date-picker v-model="scope.row.sumYear" type="year" value-format="YYYY" clearable placeholder="选择" />
							</template>
						</el-table-column>
						<el-table-column label="收入(万元)" align="center" prop="income">
							<template #default="scope">
								<el-input v-model="scope.row.income"/>
							</template>
						</el-table-column>
						<el-table-column label="利润(万元)" align="center" prop="profit">
							<template #default="scope">
								<el-input v-model="scope.row.profit"/>
							</template>
						</el-table-column>
						<el-table-column label="操作" align="center" width="100">
							<template #default="scope">
								<!-- <el-button link type="primary" @click="handleNbView(scope.row)">编辑</el-button> -->
								<el-button link type="danger" @click="handleNbDelete(scope.row)"
									v-hasPermi="['system:audit-role:delete']">删除</el-button>
							</template>
						</el-table-column>
					</el-table>
				</el-tab-pane>
				<el-tab-pane label="其他重点关注内容" name="2">
					<div>
						<el-button type="primary" plain @click="handleImport('file')"
							v-hasPermi="['system:audit-role:create']">
							<Icon icon="ep:plus" class="mr-5px" />新增
						</el-button>
					</div>
					<el-table :data="formData.tankObjectFileRespVOList" border :stripe="true" :show-overflow-tooltip="true">
						<el-table-column type="index" label="序号" width="60" align="center"/>
						<el-table-column label="附件名称" align="left" prop="fileName" min-width="120" />
						<el-table-column label="上传时间" align="center" prop="createTime">
							<template #default="scope">
								{{ formatDate(scope.row.createTime)}}
							</template>
						</el-table-column>
						<el-table-column label="操作" align="center" width="150">
							<template #default="scope">
								<!-- <el-button link type="primary" @click="handleMxView(scope.row)">编辑</el-button> -->
								<el-button link type="primary" @click="handleDownload(scope.row.fileUrl,scope.row.fileName)">下载</el-button>
								<el-button link type="danger" @click="handleDelete(scope.row)"
									v-hasPermi="['system:audit-role:delete']">删除</el-button>
							</template>
						</el-table-column>
					</el-table>
				</el-tab-pane>
			</el-tabs>
		</el-form>
		<template #footer>
			<el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
			<el-button @click="dialogVisible = false">取 消</el-button>
		</template>
	</Dialog>

	<!-- 选择人员组件 -->
	<CheckOnePerson ref="checkOnePersonRef" @success="checkPersonSuccess" />

	<!-- 新增历任领导 -->
	<CreateLeaders ref="CreateLeadersRef" @success="pushList" />
	<!-- 文件上传 -->
	<FileForm ref="formImgRef" @success="handleUploadSuccess" :type="fileType" :limit="fileLimit" />
</template>
<script setup lang="ts">
	import { getIntDictOptions, getStrDictOptions, DICT_TYPE } from '@/utils/dict'
	import { ObjectbaseApi, ObjectVO, ObjectDetailVO } from '@/api/jobmanger/audittarget'
	import FileForm from '@/views/infra/file/FileForm.vue'
	import CreateLeaders from './CreateLeaders.vue'
	import { ro } from 'element-plus/es/locale';
	import {CheckOnePerson} from '@/components/CheckOnePerson'
	import { formatDate } from '@/utils/formatTime'
	import { downFile } from '@/utils/fileName'

	/** 审计角色 表单 */
	defineOptions({ name: 'AudittargetForm' })

	const { t } = useI18n() // 国际化
	const message = useMessage() // 消息弹窗

	const dialogVisible = ref(false) // 弹窗的是否展示
	const dialogTitle = ref('') // 弹窗的标题
	const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
	const formType = ref('') // 表单的类型：create - 新增；update - 修改
	// const formData = ref<ObjectDetailVO>()
	const formData = ref({
		id: undefined,
		parentCompanyId: undefined,// 父级公司id
		companyName : undefined, // 公司名称
		companyId: undefined, //公司的id
		legalPerson : undefined, //公司法人
		companyType : undefined, //公司类别id
		comNature : undefined, //公司性质id
		registerCapital : undefined, //注册资金(万元）
		address: undefined,//注册地址
		registerTime: undefined,//注册日期
		linkTel: undefined,//联系电话
		chairmanId: undefined,//董事长id
		chairmanName: undefined,//董事长名称
		managerId: undefined,//总经理id
		managerName: undefined,//总经理名称
		leaders: undefined,//公司领导
		basicInfo: undefined,//基本情况
		licenseScope: undefined,//营业执照范围
		mainBusi: undefined,//主要开展业务情况
		strategy: undefined,//战略定位
		mainResp: undefined,//主责主业
		masteryNew: undefined,//专精特新
		tankObjLeaderHisDOList: [], //历任领导
		tankObjFinancialSumDOList: [], //财务摘要
		tankObjectFileRespVOList: [], //附件说明
	})
	const formRules = reactive({
		auditRoleName: [{ required: true, message: '不能为空', trigger: 'blur' }],
		sort: [{ required: true, message: '排序不能为空', trigger: 'blur' }],
		parentId: [{ required: true, message: '父级编号不能为空', trigger: 'change' }],
		// linkTel: [
		// 	{ required: true, message: '联系电话不能为空', trigger: 'blur' },
		// 	{ pattern: /^(?:\+?86)?(?:0[0-9]{2,3}[- ]?)?[1-9][0-9]{4,14}$/, message: '联系电话格式不正确', trigger: 'blur' }
		// ]
	})
	const formRef = ref() // 表单 Ref

	const activeName = ref('0')
	const handleClick = () => {

	}

	const formImgRef = ref()
	const fileType = ref('img')
	const fileLimit = ref(1)
	const personnel = ref() //判断董事长还是总经理的人员选择
	// 上传方法
	const handleImport = (type : string) => {
		fileType.value = type
		fileLimit.value = type === 'file' ? 5 : 1
		formImgRef.value.open()
	}
	const handleUploadSuccess = (fileList) => {
		let fileArr =
			fileList && fileList.length > 0
				? fileList.map((item) => {
					formData.value.tankObjectFileRespVOList = formData.value.tankObjectFileRespVOList??[]
					formData.value.tankObjectFileRespVOList.unshift(item.response.data)
					return {
						url: item.response.data,
					}
				})
				: []
				return
		if (fileType.value === 'file') {
			formData.value.tankObjectFileRespVOList = formData.value.tankObjectFileRespVOList.concat(...fileArr)
		} else if (fileType.value === 'img') {
			formData.value.showImg = fileArr
			formRef.value.validateField('showImg')
		}
	}
	// 删除上传附件
	const handleDelete = async (row) => {
		await message.delConfirm()
		let indexItem: number = formData.value.tankObjectFileRespVOList.indexOf(row)
		formData.value.tankObjectFileRespVOList.splice(indexItem, 1)
		await message.success(t('common.delSuccess'))
	}

	/** 打开弹窗 */
	const open = async (type : string, row: any, id?: number) => {
		dialogVisible.value = true
		dialogTitle.value = t('action.' + type)
		formType.value = type
		resetForm()
		// 获取选择公司数据
		if (row.id) {
			formLoading.value = true
			try {
				formData.value.parentCompanyId = row.parentId
				formData.value.companyId = row.id
				formData.value.companyName = row.name
			} finally {
				formLoading.value = false
			}
		}
		if(type == 'update') {
			// formData.value = await ObjectbaseApi.getCasebase(id)
			// 使用结构性赋值优化
			const data = await ObjectbaseApi.getCasebase(row.id)
			formData.value = data
			formData.value.comNature = formData.value.comNature?Number(formData.value.comNature): undefined
			// formData.value = formData.value as unknown as ObjectDetailVO
			// formData.value = row
		}
	}
	defineExpose({ open }) // 提供 open 方法，用于打开弹窗

	// 打开人员选择组件
	const checkOnePersonRef = ref()
	const handleCheckPerson = (type: string) => {
		personnel.value = type
		checkOnePersonRef.value.open()
	}

	// 人员选择器获取人员信息
	const checkPersonSuccess = (row) => {
		if(personnel.value == 'president') {
			formData.value.chairmanId= row.id
			formData.value.chairmanName = row.nickname
		} else if(personnel.value == 'manager') {
			formData.value.managerId= row.id
			formData.value.managerName = row.nickname
		}
	}

	/*
	* @Author: lijunliang
	* @Date: 2024-09-12 10:25:44
	* @Description: 历届领导人新增=>
	*/
	const CreateLeadersRef = ref()
	const typeOf = ref() //历届领导人编辑/新增
	const openWbList = (type: string, data?: any) => {
		console.log(type,data)
		typeOf.value = type
		CreateLeadersRef.value.open(type, data)
	}
	/*
	* @Author: lijunliang
	* @Date: 2024-09-12 10:27:52
	* @Description: 删除历任领导=>
	*/
	const handleWbDelete = async(row) => {
		await message.delConfirm()
		let indexItem: number = formData.value.tankObjLeaderHisDOList.indexOf(row)
		formData.value.tankObjLeaderHisDOList.splice(indexItem, 1)
		await message.success(t('common.delSuccess'))
	}
	/*
	* @Author: lijunliang
	* @Date: 2024-09-12 10:26:36
	* @Description: 外部法律法规新增=>
	*/
	const handleWbView = (val) => { }

	// 新增历任领导
	const pushList = (data: any) => {
		if(typeOf.value == 'create'){
			formData.value.tankObjLeaderHisDOList.push(data)
		}else {

		}
	}

	// 财务摘要的新增
	const openNbList = () => {
		const item = reactive({
			sumYear: undefined,
			income: undefined,
			profit: undefined,
		},)
		if(!formData.value.tankObjFinancialSumDOList){
			formData.value.tankObjFinancialSumDOList.push(item)
		}else if(formData.value.tankObjFinancialSumDOList) {
			formData.value.tankObjFinancialSumDOList.push(item)
		}
	}
	// 财务摘要的删除
	const handleNbDelete = async(row) => {
		await message.delConfirm()
		let indexItem: number = formData.value.tankObjFinancialSumDOList.indexOf(row)
		formData.value.tankObjFinancialSumDOList.splice(indexItem, 1)
		await message.success(t('common.delSuccess'))
	}

	// 文件下载
	const handleDownload = async(url: string, name: string) =>{
		// 二次确认
		await message.confirm('是否确认下载？')
		downFile(url, name)
	}
	const handleNbView = () => { }

	const openMxList = () => { }
	const handleMxDelete = () => { }
	const handleMxView = () => { }

	/** 提交表单 */
	const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
	const submitForm = async () => {
		// 校验表单
		await formRef.value.validate()
		// 提交请求
		formLoading.value = true
		try {
			const data = formData.value as unknown as ObjectVO
			if (formType.value === 'create') {
				await ObjectbaseApi.createObjectbase(data)
				message.success(t('common.createSuccess'))
			} else {
				await ObjectbaseApi.updateObjectbase(data)
				message.success(t('common.updateSuccess'))
			}
			dialogVisible.value = false
			// 发送操作成功的事件
			emit('success')
		} finally {
			formLoading.value = false
		}
	}

	/** 重置表单 */
	const resetForm = () => {
		formData.value = {
			id: undefined,
			auditRoleName: undefined,
			parentId: undefined,
			sort: undefined,
			status: undefined,
			projectPhase: undefined,
			date: undefined,
			content: undefined,
			wbflfgList: [{
				name: '1'
			}],
			nbgzzdList: [],
			sjmxList: [],

			parentCompanyId: undefined,// 父级公司id
			companyName : undefined, // 公司名称
			companyId: undefined, //公司的id
			legalPerson : undefined, //公司法人
			companyType : undefined, //公司类别id
			comNature : undefined, //公司性质id
			registerCapital : undefined, //注册资金(万元）
			address: undefined,//注册地址
			registerTime: undefined,//注册日期
			linkTel: undefined,//联系电话
			chairmanId: undefined,//董事长id
			chairmanName: undefined,//董事长名称
			managerId: undefined,//总经理id
			managerName: undefined,//总经理名称
			leaders: undefined,//公司领导
			basicInfo: undefined,//基本情况
			licenseScope: undefined,//营业执照范围
			mainBusi: undefined,//主要开展业务情况
			strategy: undefined,//战略定位
			mainResp: undefined,//主责主业
			masteryNew: undefined,//专精特新
			tankObjLeaderHisDOList: [], //历任领导
			tankObjFinancialSumDOList: [], //财务摘要
			tankObjectFileRespVOList: [], //附件说明
		}
		formRef.value?.resetFields()
	}
</script>
