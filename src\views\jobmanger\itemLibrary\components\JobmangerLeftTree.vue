<template>
	<div class="head-container">
		<el-row :gutter="8" :align="'middle'">
			<el-col :span="showEdit ? 18 : 24">
				<el-input v-model="name" class="mb-20px" clearable placeholder="请输入名称" v-if="showSearch">
					<template #prefix>
						<Icon icon="ep:search" />
					</template>
				</el-input>
			</el-col>
			<el-col :span="6" v-if="showEdit">
				<el-row :gutter="8" class="mb-16px">
					<el-col :span="8" class="cursor-pointer">
						<Icon icon="carbon:add-filled" :size="20" @click="openForm('create')" />
					</el-col>
					<!-- <el-col :span="8" class="cursor-pointer">
						<Icon icon="iconoir:edit" :size="20" @click="openForm('update', currentRow?.id)" />
					</el-col>
					<el-col :span="8" class="cursor-pointer">
						<Icon icon="weui:delete-outlined" :size="20" @click="handleDelete(currentRow?.id)" />
					</el-col> -->
				</el-row>
			</el-col>
		</el-row>

		<el-scrollbar height="calc(100vh - 90px)">
			<el-tree ref="treeRef" :data="deptList" :expand-on-click-node="false" :filter-node-method="filterNode"
				:props="defaultProps" :default-expand-all="false" highlight-current node-key="id"
				@node-click="handleNodeClick" :default-expanded-keys="defaultExpandedKeys" :show-checkbox='checkbox'
    			@check-change="handleCheckChange">
				<template #default="{ node, data }">
					<div style="display: flex;">
						<div class="custom-tree-node">
							<Icon :size="18" :icon="levelFil(node.level)" style="margin: 0 5px;"/>
          					<!-- <Icon icon="f7:doc" :size="12" /> -->
							<i class="custom-iconguanbi-quxiao-guanbi"></i>
							{{ node.label }}
						</div>
						<div class="but_right" v-if="showBut">
							<!-- <el-col :span="8" class="cursor-pointer">
								<Icon icon="carbon:add-filled" :size="20" @click="openForm('create', '', data?.id)" />
							</el-col> -->
							<el-col :span="8" class="cursor-pointer">
								<Icon icon="iconoir:edit" :size="20" @click="openForm('update', data?.id)" />
							</el-col>
							<el-col :span="8" class="cursor-pointer">
								<Icon icon="weui:delete-outlined" :size="20" @click="handleDelete(data?.id)" />
							</el-col>
						</div>
					</div>
					
				</template>
			</el-tree>
		</el-scrollbar>
	</div>
	<JobmangerLeftTreeForm ref="formRef" @success="getTree" :searchUrl="searchUrl" :createUrl="createUrl"
		:editUrl="editUrl" :delUrl="delUrl" :detailsUrl="detailsUrl" :type="type" />
</template>

<script lang="ts" setup>
	import { ElTree } from 'element-plus'
	import * as DeptApi from '@/api/system/dept'
	import { defaultProps, handleTree, findPath, treeToList } from '@/utils/tree'
	import type Node from 'element-plus/es/components/tree/src/model/node'
	import { string } from 'vue-types'
	import JobmangerLeftTreeForm from './JobmangerLeftTreeForm.vue'
	import * as JobmangerLeftTreeApi from '@/api/jobmanger/jobmangerLeftTree'
	defineOptions({ name: 'jobmangerLeftTree' })
	const { t } = useI18n() // 国际化
	const message = useMessage() // 消息弹窗
	interface TreeNode {
		id : number
		name : string
		parentId : number | string
		children ?: TreeNode[]
		isLeaf ?: boolean
	}
	const name = ref('')
	const deptList = ref<TreeNode[]>([]) // 树形结构
	const treeRef = ref<InstanceType<typeof ElTree>>()
	const queryParams = reactive({
		id: 0
	})
	const defaultExpandedKeys = ref<(string | number)[]>([])
	const levelFil = (level:number) => {
  const list = ['clarity:layers-solid','oui:layers','mynaui:layers-two','fluent:organization-48-regular','proicons:branch']
  return list[level - 1]
    }
  /** 获得部门树 */
	const getTree = async (id) => {
		currentRow.value = {}
		deptList.value = []
		const res = await JobmangerLeftTreeApi.getJobLeftTreeList(props.searchUrl)
		deptList.value.push(...handleTree(res, 'id', 'parentId'))
		defaultExpandedKeys.value = deptList.value.map((node) => node.id)
	}
	defineExpose({ getTree })
	const props = defineProps({
		showSearch: {
			default: true,
			type: Boolean
		},
		searchUrl: {
			default: '/',
			type: String
		},
		createUrl: {
			default: '/',
			type: String
		},
		editUrl: {
			default: '/',
			type: String
		},
		delUrl: {
			default: '/',
			type: String
		},
		detailsUrl: {
			default: '/',
			type: String
		},
		typeName: {
			default: '名称',
			type: String
		},
		showEdit: {
			default: true,
			type: Boolean
		},
		type: {
			default: '',
			type: String
		},
		checkbox: {
			default: false,
			type: Boolean
		},
		showBut:  {
			default: false,
			type: Boolean
		}
	})
	/** 基于名字过滤 */
	const filterNode = (name : string, data : TreeNode) => {
		if (!name) return true
		return data.name.includes(name)
	}
	const currentRow = ref()
	/** 处理部门被点击 */
	const handleNodeClick = async (row : { [key : string] : any }) => {
		currentRow.value = row
		emits('node-click', row)
	}
	const handleDelete = async (id : number) => {
		if (!id) {
			message.error('请选择需要删除的节点')
			return
		}
		await message.confirm('确定删除该节点吗？')
		await JobmangerLeftTreeApi.delJobLeftTreeList(props.delUrl, id)
		message.success(t('common.delSuccess'))
		// 刷新列表
		await getTree()
	}
	const formRef = ref()
	const openForm = (type : string, id ?: number, parent: number) => {
		if (type === 'update') {
			if (!id) {
				message.error('请先选择节点')
				return
			}
		}
		formRef.value.open(type, id, parent)
	}
	const emits = defineEmits(['node-click', 'checkedGet'])
	/** name */
	watch(name, (val) => {
		treeRef.value!.filter(val)
	})

	// 选择树形结构触发事件
	const checkedIds = ref([])
	const handleCheckChange = (data: Tree, checked: boolean, indeterminate: boolean) => {
		if (checked) {
			// 添加到已选择节点 ID 数组
			checkedIds.value.push(data)
		} else {
			// 从已选择节点 ID 数组中移除
			checkedIds.value = checkedIds.value.filter((id) => id !== data.id)
		}
		emits('checkedGet', checkedIds.value)
	}

	/** 初始化 */
	onMounted(async () => {
		await getTree(queryParams.id)
	})
</script>
<style scoped>
.el-tree-node__content {
	position: relative;
}
.but_right {
	display: none;
	position: absolute;
	right: 0;
	z-index: 999;
}
.el-tree-node__content:hover .but_right{
	display: flex;
	justify-content: center;
}
.cursor-pointer {
    padding-left: 8px !important;
}
</style>
