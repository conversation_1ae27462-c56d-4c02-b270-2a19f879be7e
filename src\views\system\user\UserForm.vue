<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle" width="60%">
    <el-descriptions :column="2" border >
      <el-descriptions-item label="姓名">{{ formData.nickname }}</el-descriptions-item>
      <el-descriptions-item label="账号">{{ formData.username }}</el-descriptions-item>
      <el-descriptions-item label="性别">{{ formData.sex == 0? '男' : '女' }}</el-descriptions-item>
      <el-descriptions-item label="手机号码">{{ formData.mobile }}</el-descriptions-item>
      <el-descriptions-item label="所属公司">{{ formData.unitName }}</el-descriptions-item>
      <el-descriptions-item label="所属岗位">{{ formData.postName }}</el-descriptions-item>
      <el-descriptions-item label="年龄">{{ formData.age}}</el-descriptions-item>
      <el-descriptions-item label="身份证号">{{formData.identNo }}</el-descriptions-item>
      <el-descriptions-item label="生日">{{formData.birthDay }}</el-descriptions-item>
      <el-descriptions-item label="状态">{{ formData.status == 0? '正常' : '禁用'}}</el-descriptions-item>
      <el-descriptions-item label="青岛港工号">{{ formData.qdgNo }}</el-descriptions-item>
      <el-descriptions-item label="创建时间">{{ formatTime(formData.createTime, 'yyyy-MM-dd') }}</el-descriptions-item>
      <el-descriptions-item label="修改时间">{{ formatTime(formData.updateTime, 'yyyy-MM-dd') }}</el-descriptions-item>
    </el-descriptions>
    <!-- <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>-->
  </Dialog>
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { CommonStatusEnum } from '@/utils/constants'
import { handleTree } from '@/utils/tree'
import * as PostApi from '@/api/system/post'
import * as DeptApi from '@/api/system/dept'
import * as UserApi from '@/api/system/user'
import { formatTime } from '@/utils'
defineOptions({ name: 'SystemUserForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  nickname: '',
  deptId: '',
  deptName: '',
  age: '',
  mobile: '',
  email: '',
  identNo: '',
  birthDay: '',
  id: undefined,
  username: '',
  password: '',
  sex: undefined,
  postIds: [],
  postName: '',
  remark: '',
  status: CommonStatusEnum.ENABLE,
  roleIds: [],
  qdgNo: '',
  createTime: '',
  updateTime: '',
})
const list = ref([]) // 列表的数
const loading = ref(false) // 列表的加载中
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10
})

const formRef = ref() // 表单 Ref
const deptList = ref<Tree[]>([]) // 树形结构
const postList = ref([] as PostApi.PostVO[]) // 岗位列表
const getList = async () => {
  loading.value = true
  try {
    const data = await UserApi.getUserPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}
/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await UserApi.getUser(id)
    } finally {
      formLoading.value = false
    }
  }
  // 加载部门树
  deptList.value = handleTree(await DeptApi.getSimpleDeptList())
  // 加载岗位列表
  postList.value = await PostApi.getSimplePostList()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
</script>
<style lang="scss" scoped>
.demo-tabs > .el-tabs__content {
  padding: 32px;
  color: #6b778c;
  font-size: 32px;
  font-weight: 600;
}
</style>