<script setup lang="ts">
import CatalogForm from "./components/CatalogForm.vue";
import SolidifiedModelForm from "./components/SolidifiedModelForm.vue";
import SqlModelResult from "./components/SqlModelResult.vue";
import * as catalogApi from "@/api/model/catalog";
import * as modelApi from "@/api/model/info";
import {dateFormatter} from "@/utils/formatTime";
import router from "@/router";

const message = useMessage() // 消息弹窗

const handleNodeClick = (data: Tree) => {
  queryParams.catalogId = data.id
  handleQuery()
}

const list = ref([])
const data = list


const defaultProps = {
  children: 'children',
  label: 'label',
}

const loading = ref(true)

const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  modelName: null,
  modelType: null,
  isPublish: null,
  catalogId: '',
})
const queryFormRef = ref() // 查询表单

const tableLoading = ref(true)
const total = ref(0) // 列表的总页数
const tableList = ref([])

const modelTypeOptions = [
  {
    value: 'query',
    label: '查询类',
  },
  {
    value: 'doubtful',
    label: '疑点类',
  }
]

const publishOptions = [
  {
    value: '1',
    label: '已发布',
  },
  {
    value: '0',
    label: '未发布',
  }
]


/** 查询按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1;
  getModelPageList();
}

/** 重置查询条件 **/
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}
/** 查询列表 */
const getModelPageList = async () => {
  try {
    tableLoading.value = true;
    const res = await modelApi.getModelInfoPage(queryParams);
    tableList.value = res.list;
    total.value = res.total;
  } finally {
    tableLoading.value = false;
  }
}

onMounted(() => {
  getList();
  getModelPageList();
})

const getList = async () => {
  loading.value = true
  try {
    const res = await catalogApi.getModelCatalogTree();
    list.value = res;
  } finally {
    loading.value = false
  }
}

const formRef = ref()
const openForm = (type: string, data?: object) => {
  formRef.value.open(type, data)
}

const formRefModel = ref()
const newModelForm = (type: string, modelType?: string, id?: number) => {
  formRefModel.value.open(type, modelType, '', id)
}

// 删除模型目录树节点
const handleDelete = async (id: number) => {
  await message.delConfirm()
  try {
    await catalogApi.deleteCatalogInfo(id);
    await getList();
    message.success('删除成功')
  } catch {
  }
}

// 删除模型
const handleDeleteModel = async (id: number) => {
  await message.delConfirm()
  try {
    await modelApi.deleteModelInfo(id);
    await getModelPageList();
    message.success('删除成功')
  } catch {
  }
}


const sqlResultRef = ref()
const sqlResultShow = async (data: object) => {
  sqlResultRef.value.open(data.id, data.modelName)
}

const openSqlPage = () => {
  router.push('/model/factory/sqlmodel')
}

const modelShow = async (data: object) => {
  const params = {
    modelId: data.id
  }
  const modelData = await modelApi.getModelInfo(data.id)
  const path = modelData.pageLink
  if (path) {
    router.push({
      path: path,
      query: params
    })
  }
}

const levelFil = (level: number) => {
  const list = ['clarity:layers-solid','oui:layers','mynaui:layers-two','fluent:organization-48-regular','proicons:branch']
  return list[level - 1]
}
</script>

<template>
  <el-row :gutter="10">
    <el-col :span="5">
      <ContentWrap class="h-1/1">
        <el-scrollbar height="calc(100vh - 90px)">
          <el-tree
            style="max-width: 600px"
            :data="data"
            :props="defaultProps"
            :default-expand-all="true"
            :expand-on-click-node="false"
            @node-click="handleNodeClick"
            node-key="id"
            :indent="6"
          >
            <template #default="{ node, data }">
              <div style="display: flex;">
                <div class="custom-tree-node" @click="handleNodeClick(data)">
                  <Icon :size="18" :icon="levelFil(node.level)" style="margin: 0 5px;"/>
                  <i class="custom-iconguanbi-quxiao-guanbi"></i>
                  {{ node.label }}
                </div>
                <div class="but_right">
                  <el-col :span="8" class="cursor-pointer">
                    <Icon icon="carbon:add-filled" :size="20" @click="openForm('create',data)" v-if="data.parentId === '1' || data.id === '1'" />
                  </el-col>
                  <el-col :span="8" class="cursor-pointer">
                    <Icon icon="iconoir:edit" :size="20" @click="openForm('update',data)" v-if="data.id !== '1'" />
                  </el-col>
                  <el-col :span="8" class="cursor-pointer">
                    <Icon icon="weui:delete-outlined" :size="20" @click="handleDelete(data.id)" v-if="data.id !== '1'" />
                  </el-col>
                </div>
              </div>
            </template>
          </el-tree>
        </el-scrollbar>
      </ContentWrap>
    </el-col>
    <el-col :span="19">
      <ContentWrap class="common-card-search">
        <el-form
          :model="queryParams"
          class="-mb-15px common-search-form"
          ref="queryFormRef"
          :inline="true"
          label-width="76px"
        >
          <el-form-item label="模型名称" prop="modelName">
            <el-input
              v-model="queryParams.modelName"
              placeholder="请输入模型名称"
              clearable
              class="!w-200px"
            />
          </el-form-item>
          <el-form-item label="模型状态" prop="isPublish">
            <el-select placeholder="请选择状态" v-model="queryParams.isPublish" clearable
                       class="!w-200px">
              <el-option
                v-for="item in publishOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="模型分类" prop="modelType">
            <el-select v-model="queryParams.modelType"
                       placeholder="请选择模型分类"
                       clearable
                       class="!w-200px"
            >
              <el-option
                v-for="item in modelTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-form>
        <div class="right-search-btn">
          <el-button  type="primary"  @click="handleQuery">
              <Icon icon="ep:search" class="mr-5px" />搜索
            </el-button>
            <el-button @click="resetQuery">
              <Icon icon="ep:refresh" class="mr-5px" />重置
            </el-button>
        </div>
      </ContentWrap>
      <ContentWrap>
        <div class="button_margin15">
            <el-button
              type="primary"
              plain
              size="default"
              @click="openSqlPage()"
            >
              新增SQL模型
            </el-button>
            <el-button
              type="primary"
              size="default"
              plain
              @click="newModelForm('create','doubtful')"
            >
              新增固化模型
            </el-button>
        </div>

        <el-table border v-loading="tableLoading" :data="tableList">
          <el-table-column label="序号" align="center" width="60">
            <template #default="{ $index }">
              {{ (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1 }}
            </template>
          </el-table-column>
          <el-table-column label="模型名称" align="left" prop="modelName" width="240"/>
          <el-table-column label="风险点" show-overflow-tooltip align="left" prop="riskPointDescription" width="320"/>
          <el-table-column label="风险数据特征" show-overflow-tooltip align="left" prop="riskDataCharacteristic" width="320"/>
          <el-table-column label="模型规则" show-overflow-tooltip align="left" prop="modelRule" width="320"/>
          <el-table-column label="构建方式" align="center" prop="buildMethod" width="90">
            <template #default="scope">
              <span>{{ scope.row.buildMethod === 'fix' ? '固化' : 'SQL' }}</span>
            </template>
          </el-table-column>
          >
          <el-table-column label="模型分类" align="center" prop="modelType" width="90">
            <template #default="scope">
              <span>{{ scope.row.modelType === 'query' ? '查询类' : '疑点类' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="提出人" align="center" prop="creator" width="90"/>
          <el-table-column label="创建时间" align="center" prop="createTime" show-overflow-tooltip :formatter="dateFormatter" width="180"/>
          <el-table-column label="操作" width="190" fixed="right" align="center">
            <template #default="scope">
              <el-button type="primary" @click="sqlResultShow(scope.row)" link v-if="scope.row.buildMethod === 'sql'">
                结果查询
              </el-button>
              <el-button type="primary" @click="modelShow(scope.row)" link v-else>
                模型预览
              </el-button>
              <el-button type="primary" @click="newModelForm('update',scope.row.modelType,scope.row.id)" link v-if="scope.row.editEnable">
                修改
              </el-button>
              <el-button type="danger" @click="handleDeleteModel(scope.row.id)" link v-if="scope.row.editEnable">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页组件 -->
        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="getModelPageList"
        />

      </ContentWrap>
    </el-col>
  </el-row>
  <CatalogForm ref="formRef" @success="getList"/>

  <SolidifiedModelForm ref="formRefModel" @success="getModelPageList"/>

  <SqlModelResult ref="sqlResultRef"/>
</template>

<style scoped lang="scss">
  .el-tree-node__content {
    position: relative;
  }
  .but_right {
    display: none;
    position: absolute;
    right: 0;
    z-index: 999;
  }
  .el-tree-node__content:hover .but_right{
    display: flex;
  }
  .cursor-pointer {
    padding-right: 4px !important;
    padding-left: 4px !important;
  }

  ::v-deep(.el-table .el-table__header .el-table__cell) {
    text-align: center;
  }
</style>
