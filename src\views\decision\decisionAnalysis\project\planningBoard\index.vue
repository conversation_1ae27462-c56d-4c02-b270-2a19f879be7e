<!-- 计划看板 -->
<template>
    <div class="header">
        <div class="header_title">
            <div class="header_title_size">
                计划执行情况统计
            </div>
            <div class="header_date">
                <div class="pr-14px">年度</div>
                <el-date-picker v-model="queryParams.publishDate" type="year" placeholder="开始" class="!w-90px"
                    @change="handleQuery" value-format="YYYY"/>
            </div>
        </div>
        <div class="flex items-center pt-12px pr-20px pl-20px">
            <div class="header_card1">
                <div class="header_card_title">
                    计划总数
                </div>
                <el-row class="flex items-center pl-20px pt-10px">
                    <el-col :span="10" :xs="24">
                        <el-image style="width: 64px; height: 64px" :src="url1" fit="cover" />
                    </el-col>
                    <el-col :span="14" :xs="24">
                        <div class="card_number text-center">
                            20
                        </div>
                        <div class="card_text text-center">
                            当前计划总数
                        </div>
                    </el-col>
                </el-row>
            </div>
            <div class="header_card2">
                <div class="header_card_title">
                    执行中
                </div>
                <el-row class="flex items-center pl-20px pt-10px">
                    <el-col :span="6" :xs="24">
                        <el-image style="width: 64px; height: 64px" :src="url2" fit="cover" />
                    </el-col>
                    <el-col :span="9" :xs="24">
                        <div class="card_number text-center">
                            15
                        </div>
                        <div class="card_text text-center">
                            执行中的计划
                        </div>
                    </el-col>
                    <el-col :span="9" :xs="24">
                        <div class="card_number text-center">
                            75.00%
                        </div>
                        <div class="card_text text-center">
                            占比
                        </div>
                    </el-col>
                </el-row>
            </div>
            <div class="header_card2">
                <div class="header_card_title">
                    已完成
                </div>
                <el-row class="flex items-center pl-20px pt-10px">
                    <el-col :span="6" :xs="24">
                        <el-image style="width: 64px; height: 64px" :src="url3" fit="cover" />
                    </el-col>
                    <el-col :span="9" :xs="24">
                        <div class="card_number text-center">
                            5
                        </div>
                        <div class="card_text text-center">
                            已完成的计划
                        </div>
                    </el-col>
                    <el-col :span="9" :xs="24">
                        <div class="card_number text-center">
                            25.00%
                        </div>
                        <div class="card_text text-center">
                            占比
                        </div>
                    </el-col>
                </el-row>
            </div>
            <div class="header_card2">
                <div class="header_card_title">
                    未立项
                </div>
                <el-row class="flex items-center pl-20px pt-10px">
                    <el-col :span="6" :xs="24">
                        <el-image style="width: 64px; height: 64px" :src="url4" fit="cover" />
                    </el-col>
                    <el-col :span="9" :xs="24">
                        <div class="card_number text-center">
                            1
                        </div>
                        <div class="card_text text-center">
                            未立项的计划
                        </div>
                    </el-col>
                    <el-col :span="9" :xs="24">
                        <div class="card_number text-center">
                            5.00%
                        </div>
                        <div class="card_text text-center">
                            占比
                        </div>
                    </el-col>
                </el-row>
            </div>
            <div class="header_card2">
                <div class="header_card_title">
                    已超期
                </div>
                <el-row class="flex items-center pl-20px pt-10px">
                    <el-col :span="6" :xs="24">
                        <el-image style="width: 64px; height: 64px" :src="url5" fit="cover" />
                    </el-col>
                    <el-col :span="9" :xs="24">
                        <div class="card_number text-center">
                            0
                        </div>
                        <div class="card_text text-center">
                            已超期的计划
                        </div>
                    </el-col>
                    <el-col :span="9" :xs="24">
                        <div class="card_number text-center">
                            00.00%
                        </div>
                        <div class="card_text text-center">
                            占比
                        </div>
                    </el-col>
                </el-row>
            </div>
        </div>
    </div>
    <el-row :gutter="8" class="pt-16px">
        <el-col :span="6" :xs="24">
            <ContentWrap>
                <div class="flex_title">
                    <div class="title-left"></div>
                    <div class="pl-8px">计划类型分布</div>
                </div>
                <div :class="oneChart" ref="oneChartRef" style="height:288px;width:100%;"></div>
            </ContentWrap>
        </el-col>
        <el-col :span="6" :xs="24">
            <ContentWrap>
                <div class="flex_title">
                    <div class="title-left"></div>
                    <div class="pl-8px">计划变更次数分布</div>
                </div>
                <div :class="twoChart" ref="twoChartRef" style="height:288px;width:100%;"></div>
            </ContentWrap>
        </el-col>
        <el-col :span="6" :xs="24">
            <ContentWrap>
                <div class="flex_title">
                    <div class="title-left"></div>
                    <div class="pl-8px">计划执行情况分布</div>
                </div>
                <div :class="threeChart" ref="threeChartRef" style="height:288px;width:100%;"></div>
            </ContentWrap>
        </el-col>
        <el-col :span="6" :xs="24">
            <ContentWrap>
                <div class="flex_title">
                    <div class="title-left"></div>
                    <div class="pl-8px">计划组织分布</div>
                </div>
                <div :class="fourChart" ref="fourChartRef" style="height:288px;width:100%;"></div>
            </ContentWrap>
        </el-col>
    </el-row>
    <ContentWrap>
        <div class="flex_title">
            <div class="title-left"></div>
            <div class="pl-8px">各单位计划执行情况统计</div>
        </div>
        <div :class="fiveChart" ref="fiveChartRef" style="height:288px;width:100%;"></div>
    </ContentWrap>
</template>

<script lang="ts" setup>
import * as echarts from 'echarts';
import demoviewIcon1 from '@/assets/imgs/decision/icon-jihua.png';
import demoviewIcon2 from '@/assets/imgs/decision/icon-zhixingzhong.png';
import demoviewIcon3 from '@/assets/imgs/decision/icon-yiwancheng.png';
import demoviewIcon4 from '@/assets/imgs/decision/icon-weilixiang.png';
import demoviewIcon5 from '@/assets/imgs/decision/icon-yichaoqi.png';
import { colorList } from '../../index'
defineOptions({
    name: 'PlanningBoard'
})
const queryParams = reactive({
    publishDate: undefined,
})
const loading = ref(true) // 列表的加载中

const url1 = ref(demoviewIcon1)
const url2 = ref(demoviewIcon2)
const url3 = ref(demoviewIcon3)
const url4 = ref(demoviewIcon4)
const url5 = ref(demoviewIcon5)
const color = ['#2A71C0', '#FF9C01', '#AC3C52', '#C96F37', '#95C2ED', '#E6E8F4', '#ABEDD8']

// 第一个echarts
let oneChart: echarts.ECharts | null = null;
const oneChartRef = ref()
const getOneChart = async () => {
    oneChart = echarts.init(oneChartRef.value)
    const data = [
        { value: 10, name: '专项设计' },
        { value: 5, name: '离任审计' },
        { value: 20, name: '任中审计' },
    ]
    oneChart.setOption({
        color: color,
        // tooltip: {
        //     trigger: 'item',
        // },
        legend: {
            top: 'bottom',
            itemWidth: 14,
            data: data
        },
        series: [
            {
                name: '姓名',
                type: 'pie',
                radius: ['50%', '60%'],
                center: ['50%', '40%'],
                avoidLabelOverlap: false,
                padAngle: 5,
                itemStyle: {
                    borderRadius: 10
                },
                label: {
                    show: false,
                    position: 'center'
                },
                emphasis: {
                    label: {
                        show: true,
                        fontSize: 15,
                        fontWeight: 500,
                        formatter: function (params) {
                            return '{total|' + params.value + '}' + '\n ' + params.name;
                        },
                        rich: {
                            total: {
                                fontSize: 25,
                                color: '#000',
                                lineHeight: 40
                            }
                        }
                    }
                },
                labelLine: {
                    show: false
                },
                data: data
            }
        ]
    })
}

// 第二个echarts
let twoChart: echarts.ECharts | null = null;
const twoChartRef = ref()
const getTwoChart = async () => {
    twoChart = echarts.init(twoChartRef.value)
    const data = [
        { value: 10, name: '0次' },
        { value: 8, name: '1次' },
        { value: 7, name: '2次' },
        { value: 6, name: '3次' },
        { value: 5, name: '4次' },
    ]
    twoChart.setOption({
        color: color,
        tooltip: {
            trigger: 'item',
        },
        legend: {
            top: 'bottom',
            itemWidth: 14,
            data: data
        },
        series: [
            {
                name: '姓名',
                type: 'pie',
                radius: [20, 100],
                center: ['50%', '40%'],
                roseType: 'area',
                data: data,
                itemStyle: {
                    borderRadius: 2,
                    normal: {
                        color: function (params) {
                            return colorList[params.dataIndex];
                        },
                    },
                },
                label: {
                    show: false
                },
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                },
            }
        ]
    })
}

// 第三个echarts
let threeChart: echarts.ECharts | null = null;
const threeChartRef = ref()
const getThreeChart = async () => {
    threeChart = echarts.init(threeChartRef.value)
    threeChart.setOption({
        color: color,
        grid: {
            top: "10%"
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross',
                crossStyle: {
                    color: '#999'
                }
            }
        },
        legend: {
            data: ['项目数量', '占比'],
            itemWidth: 14,
            top: 'bottom',
        },
        xAxis: [
            {
                type: 'category',
                data: ['未立项', '项目准备', '项目实施', '项目报告', '归档结项'],
                axisPointer: {
                    type: 'shadow'
                },
                axisLabel: {
                    interval: 0,
                }
            }
        ],
        yAxis: [
            {
                type: 'value',
            },
            {
                type: 'value',
                splitLine: {
                    show: false,
                }
            }
        ],
        series: [
            {
                name: '项目数量',
                type: 'bar',
                data: [20, 50, 65, 35, 30],
                barMaxWidth: 40,
                itemStyle: {
                    borderRadius: [10, 10, 0, 0]
                }
            },
            {
                name: '占比',
                type: 'line',
                yAxisIndex: 1,
                data: [10.00, 25.00, 32.50, 17.50, 16.00]
            }
        ]
    })
}

// 第四个echarts
let fourChart: echarts.ECharts | null = null;
const fourChartRef = ref()
const getFourChart = async () => {
    fourChart = echarts.init(fourChartRef.value)
    const data = [
        { value: 10, name: '山东港集团' },
        { value: 20, name: '青岛港' },
        { value: 17, name: '烟台港' },
        { value: 16, name: '日照港' },
        { value: 15, name: '渤海湾港' },
    ]
    fourChart.setOption({
        color: color,
        tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b} : {c} ({d}%)'
        },
        legend: {
            top: 'bottom',
            itemWidth: 14,
            data: data
        },
        series: [
            {
                name: '姓名',
                type: 'pie',
                radius: '60%',
                center: ['50%', '40%'],
                data: data,
                label: {
                    show: false
                },
                emphasis: {
                    itemStyle: {
                        scale: true,
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                },
                selectedMode: "single",
            }
        ]
    })
}

// 第五个echarts
let fiveChart: echarts.ECharts | null = null;
const fiveChartRef = ref()
const getFiveChart = async () => {
    fiveChart = echarts.init(fiveChartRef.value)
    fiveChart.setOption({
        color: color,
        grid: {
            top: "10%",
            right: 50,
            left: 50,
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross',
                crossStyle: {
                    color: '#999'
                }
            }
        },
        legend: {
            data: ['计划数', '已完成', '计划完成率'],
            itemWidth: 14,
            top: 'bottom',
        },
        xAxis: [
            {
                type: 'category',
                data: ['青岛港引航站有限公司', '青岛港务局驻深圳办', '青岛国际邮轮开发建', '山东港信期货有限公司', '青岛港融资担保有限', '青岛港口投资建设', '山东港口青港实华能', '山东港口威海港有限', '青岛港国际股份有限', '山东港口投资控股有'],
                axisPointer: {
                    type: 'shadow'
                },
                axisLabel: {
                    interval: 0,
                }
            }
        ],
        yAxis: [
            {
                type: 'value',
            },
            {
                type: 'value',
                splitLine: {
                    show: false,
                }
            }
        ],
        series: [
            {
                name: '计划数',
                type: 'bar',
                data: [20, 30, 23, 32, 30, 20, 25, 20, 15, 21],
                barMaxWidth: 40,
                itemStyle: {
                    borderRadius: [10, 10, 0, 0],
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 1, color: '#BBC8DE' },
                        { offset: 0, color: '#07397E' }
                    ])
                }
            },
            {
                name: '已完成',
                type: 'bar',
                data: [5, 15, 11, 15, 12, 10, 5, 2, 5, 11],
                barMaxWidth: 40,
                itemStyle: {
                    borderRadius: [10, 10, 0, 0],
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 1, color: '#75ACE3' },
                        { offset: 0, color: '#82ADD7' }
                    ])
                }
            },
            {
                name: '计划完成率',
                type: 'line',
                yAxisIndex: 1,
                data: [25.00, 50.00, 47.83, 46.88, 40.00, 50.00, 20.00, 10.00, 33.33, 52.38]
            }
        ]
    })
}

const getAllApi = async () => {
    await Promise.all([getOneChart()])
    await Promise.all([getTwoChart()])
    await Promise.all([getThreeChart()])
    await Promise.all([getFourChart()])
    await Promise.all([getFiveChart()])
    loading.value = false
}

/** 搜索按钮操作 */
const handleQuery = () => {
    console.log('000')
    getAllApi()
}

/** 初始化 **/
onMounted(() => {
    getAllApi()
})
</script>

<style scoped>
/* 去除输入框边框 */
::v-deep .el-date-editor {
    --el-input-border-color: transparent !important;
    --el-input-bg-color: transparent;
    --el-input-hover-border-color: #fff;
}

.flex_title {
    display: flex;
    align-items: center;
    line-height: 33px;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 700;
    font-size: 16px;
    color: #333333;
    border-bottom: 1px solid #EEEFF0;
}

.title-left {
    width: 5px;
    height: 15px;
    background: #2F4CAD;
    border-radius: 0px 0px 0px 0px;
}

.header {
    height: 199px;
    background-image: url('@/assets/imgs/decision/bg.png');
    background-repeat: no-repeat;
    background-size: cover;
}

.header_title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 20px 0;
}

.header_title_size {
    height: 26px;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 500;
    font-size: 18px;
    color: #333333;
    line-height: 26px;
}

.header_date {
    width: 144px;
    height: 34px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 8px 8px 8px 8px;
    border: 2px solid #FFFFFF;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    font-size: 15px;
    color: #999;
    line-height: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.header_card1 {
    flex: 1;
    height: 125px;
    background: rgba(246, 247, 254, 0.7);
    border-radius: 8px 8px 8px 8px;
    border: 2px solid #FFFFFF;
}

.header_card2 {
    flex: 1.5;
    height: 125px;
    background: rgba(246, 247, 254, 0.7);
    border-radius: 8px 8px 8px 8px;
    border: 2px solid #FFFFFF;
    margin-left: 6px;
}

.header_card_title {
    height: 23px;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    font-size: 16px;
    color: #333333;
    line-height: 23px;
    padding-top: 14px;
    padding-left: 20px;
}

.header_card_item {
    flex: 1;
}

.card_number {
    height: 41px;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 700;
    font-size: 26px;
    color: #333333;
    line-height: 41px;
}

.card_text {
    height: 22px;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    font-size: 15px;
    color: #999999;
    line-height: 22px;
}
</style>