<!--
* @Author: wangk
* @Date: 2024-10-26 17:10:50
* @Description: 领导班子业绩考核=>
-->
<template>
  <div class="flex flex-col">
   <ContentWrap>
    <div class="flex flex-row w-100%">

      <ContentWrap class="w-50%" :title="'签约、采购金额'" :shut="true">
        <!-- 应收款余额表格 -->
        <el-table
          v-loading="loading"
          :data="list"
          :stripe="true"
          :show-overflow-tooltip="true"
        >
          <el-table-column label="序号" width="60" align="center">
            <template #default="{ $index }">{{
              (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1
            }}</template>
          </el-table-column>
          <el-table-column label="指标名称" align="center" prop="questionType" />
          <el-table-column label="上档基础分" align="center" prop="questionTitle" />
          <el-table-column label="本档基础分" align="center" prop="questionTitle" />
          <el-table-column label="调整分" align="center" prop="questionTitle" />
          <el-table-column label="指标得分" align="center" prop="questionTitle" />
        </el-table>
        <!-- 分页 -->
        <Pagination
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="props.getList()"
        />
      </ContentWrap>
      <ContentWrap class="w-50% m-l-15px"  :title="'分类别采购对比'" :shut="true">
        <div>
          <div :class="bottomChart" ref="bottomChartRef" style="height: 250px; width: 100%"></div>
        </div>
      </ContentWrap>
    </div>
   </ContentWrap>
   <ContentWrap>
    <el-table
     v-loading="loading"
     :data="list"
     :stripe="true"
     :show-overflow-tooltip="true"
   >
     <el-table-column label="序号" width="60" align="center">
       <template #default="{ $index }">{{
         (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1
       }}</template>
     </el-table-column>
     <el-table-column label="指标名称" align="center" prop="questionType" />
     <el-table-column label="上档基础分" align="center" prop="questionTitle" />
     <el-table-column label="本档基础分" align="center" prop="questionTitle" />
     <el-table-column label="调整分" align="center" prop="questionTitle" />
     <el-table-column label="指标得分" align="center" prop="questionTitle" />
   </el-table>
   </ContentWrap>
   <Pagination
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="props.getList()"
        />
  </div>
</template>

<script setup lang="ts">
import { RegulationsVO } from '@/api/jobmanger/regulations/outregulations'
// import { reformingStandingBookApi } from '@/api/auditManagement/auditRectifica/reformingStandingBook'
import { handleLazyTree } from '@/utils/tree'
import * as DeptApi from '@/api/system/dept'
import * as echarts from 'echarts'
import { propTypes } from '@/utils/propTypes'
// import { formatTime } from '@/utils'
defineOptions({ name: 'Card2' })

const loading = ref(false) // 列表的加载中
const list = ref() // 列表的数据
const queryParams = reactive({
  pageSize: 10,
  pageNo: 1
})
const total = ref(0)
// 定义属性
const props = defineProps({
  tableData: propTypes.object.def({}),
  getList: {
    type: Function,
    default: ()=>{}
  }
})
list.value=props.tableData
// 获取单位
interface DeptNode {
  id: number
  masterOrgId?: number | string
  name: string
  parentId: number | string
  children?: DeptNode[]
  isLeaf?: boolean
}
const deptList = ref<DeptNode[]>([]) // 树形结构
const defaultExpandedKeys = ref<(string | number)[]>([])
const getTree = async (id) => {
  deptList.value = []
  const res = await DeptApi.getSimpleDeptList(id)
  deptList.value.push(...handleLazyTree(res, 'id', 'parentId'))
  defaultExpandedKeys.value = deptList.value.map((node) => node.id)
}
// 右边图表
let bottomChart = null
const bottomChartRef = ref<InstanceType<typeof ElTree>>()
const getBottomChart = async () => {
  bottomChart = echarts.init(bottomChartRef.value)
  bottomChart.setOption({
  tooltip: {
    trigger: "item",
    formatter: "{a} <br/>{b}: {c} ({d}%)",
  },
  legend: {
    orient: "vertical",
    x: "left",
    data: [
      "直达",
      "营销广告",
      "搜索引擎",
      "邮件营销",
      "联盟广告",
      "视频广告",
      "百度",
      "谷歌",
      "必应",
      "其他",
    ],
  },
  series: [
    {
      name: "访问来源",
      type: "pie",
      selectedMode: "single",
      radius: [0, "30%"],

      label: {
        normal: {
          position: "inner",
        },
      },
      labelLine: {
        normal: {
          show: false,
        },
      },
      data: [
        { value: 335, name: "直达", selected: true },
        { value: 679, name: "营销广告" },
        { value: 1548, name: "搜索引擎" },
      ],
    },
    {
      name: "访问来源",
      type: "pie",
      radius: ["40%", "55%"],

      data: [
        { value: 335, name: "直达" },
        { value: 310, name: "邮件营销" },
        { value: 234, name: "联盟广告" },
        { value: 135, name: "视频广告" },
        { value: 1048, name: "百度" },
        { value: 251, name: "谷歌" },
        { value: 147, name: "必应" },
        { value: 102, name: "其他" },
      ],
    },
  ],
})
}


/** 初始化 **/
onMounted(async () => {
  list.value=props.tableData
  total.value=list.value.length
  console.log(list,'11111')
  getTree(0)
  await getBottomChart()
})
</script>
